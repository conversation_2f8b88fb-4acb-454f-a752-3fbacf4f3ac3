[2025-10-09 18:01:38] local.INFO: TRACES API Get EU-Import Certificate by Reference {"import_reference":"IMPORT.EU.MR.2025.0003940","endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","timestamp":"2025-10-09 18:01:38 GMT"} 
[2025-10-09 18:01:38] local.INFO: TRACES EU-Import API Request {"endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","soap_action":"getEuImportCertificate","request_size":1904,"timeout":1200,"verify_ssl":true} 
[2025-10-09 18:01:40] local.INFO: TRACES API Get EU-Import Certificate Response {"import_reference":"IMPORT.EU.MR.2025.0003940","response_size":87438} 
[2025-10-09 18:01:40] local.INFO: EU-Import XML response saved {"filename":"eu_import_certificate_2025-10-09_18-01-40.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_certificate_2025-10-09_18-01-40.xml","size":87438} 
[2025-10-09 18:01:40] local.INFO: EU-Import XML response saved {"filename":"eu_import_IMPORT.EU.MR.2025.0003940.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_IMPORT.EU.MR.2025.0003940.xml","size":87438} 
[2025-10-09 18:01:40] local.INFO: XML Debug - Trade line pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns3:IncludedSPSTradeLineItem","count":9} 
[2025-10-09 18:01:40] local.INFO: XML Debug - First trade line structure {"reference":"IMPORT.EU.MR.2025.0003940","xml_snippet":"<ns4:IncludedSPSTradeLineItem><ns4:SequenceNumeric>0</ns4:SequenceNumeric><ns4:Description>Consignment totals and summary</ns4:Description><ns4:NetWeightMeasure unitCode=\"KGM\">544.4</ns4:NetWeightMeasure><ns4:GrossWeightMeasure unitCode=\"KGM\">609</ns4:GrossWeightMeasure><ns4:PhysicalSPSPackage><ns4:LevelCode name=\"No packaging hierarchy\">4</ns4:LevelCode><ns4:TypeCode name=\"Boîte en polystyrène\">QR</ns4:TypeCode><ns4:ItemQuantity>43.0</ns4:ItemQuantity></ns4:PhysicalSPSPackage><ns4:OriginSPSCountry><ns4:ID>MR</ns4:ID><ns4:Name languageID=\"fr\">Mauritanie</ns4:Name></ns4:OriginSPSCountry></ns4:IncludedSPSTradeLineItem>"} 
[2025-10-09 18:01:40] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns5:SPSConsignment","count":1} 
[2025-10-09 18:01:40] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//SPSConsignment","count":0} 
[2025-10-09 18:01:40] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//*[local-name()=\"SPSConsignment\"]","count":1} 
[2025-10-09 18:01:40] local.INFO: Found trade line items {"import_id":"IMPORT.EU.MR.2025.0003940","count":9} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":"544.4","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"1","scientific_name":"Epinephelus aeneus","net_weight":"181","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"1","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"1","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"1","system_id":"FAO_ASFIS","class_code":"GPW","class_names":["Epinephelus aeneus"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"1","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"1","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"1","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"2","scientific_name":"Epinephelus marginatus","net_weight":"3.3","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"2","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"2","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"2","system_id":"FAO_ASFIS","class_code":"GPD","class_names":["Epinephelus marginatus"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"2","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"2","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"2","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"3","scientific_name":"Pagrus auriga","net_weight":"61.5","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"3","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"3","system_id":"CN","class_code":"03028590","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","Dorades (Sparidés) (Sparidae)","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"3","system_id":"FAO_ASFIS","class_code":"REA","class_names":["Pagrus auriga"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"3","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"3","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"3","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"4","scientific_name":"Epinephelus costae","net_weight":"17.3","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"4","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"4","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"4","system_id":"FAO_ASFIS","class_code":"EPK","class_names":["Epinephelus costae"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"4","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"4","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"4","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"5","scientific_name":"Pagrus caeruleostictus","net_weight":"75.7","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"5","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"5","system_id":"CN","class_code":"03028590","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","Dorades (Sparidés) (Sparidae)","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"5","system_id":"FAO_ASFIS","class_code":"BSC","class_names":["Pagrus caeruleostictus"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"5","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"5","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"5","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"6","scientific_name":"Mycteroperca rubra","net_weight":"7.5","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"6","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"6","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"6","system_id":"FAO_ASFIS","class_code":"MKU","class_names":["Mycteroperca rubra"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"6","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"6","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"6","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"7","scientific_name":"Solea senegalensis","net_weight":"99.6","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"7","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"7","system_id":"CN","class_code":"03022300","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, œufs et laitances","Soles (Solea spp.)","Soles (Solea spp.)"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"7","system_id":"FAO_ASFIS","class_code":"OAL","class_names":["Solea senegalensis"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"7","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"7","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"7","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Parsing product data {"sequence":"8","scientific_name":"Pseudupeneus prayensis","net_weight":"98.5","unit":"KGM"} 
[2025-10-09 18:01:40] local.INFO: Found classifications {"sequence":"8","count":5} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"8","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"8","system_id":"FAO_ASFIS","class_code":"GOA","class_names":["Pseudupeneus prayensis"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"8","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"8","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:01:40] local.INFO: Found classification {"sequence":"8","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:01:40] local.INFO: Certificate data ready for storage {"reference":"IMPORT.EU.MR.2025.0003940","data_keys":["id","name","issue_date_time","type_code","type_name","status_code","status_name","consignments","parties","locations","referenced_documents","authentication","notes","raw_xml"]} 
[2025-10-09 18:02:47] local.INFO: TRACES API Get EU-Import Certificate by Reference {"import_reference":"IMPORT.EU.MR.2025.0004232","endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","timestamp":"2025-10-09 18:02:47 GMT"} 
[2025-10-09 18:02:47] local.INFO: TRACES EU-Import API Request {"endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","soap_action":"getEuImportCertificate","request_size":1904,"timeout":1200,"verify_ssl":true} 
[2025-10-09 18:02:50] local.INFO: TRACES API Get EU-Import Certificate Response {"import_reference":"IMPORT.EU.MR.2025.0004232","response_size":146458} 
[2025-10-09 18:02:50] local.INFO: EU-Import XML response saved {"filename":"eu_import_certificate_2025-10-09_18-02-50.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_certificate_2025-10-09_18-02-50.xml","size":146458} 
[2025-10-09 18:02:50] local.INFO: EU-Import XML response saved {"filename":"eu_import_IMPORT.EU.MR.2025.0004232.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_IMPORT.EU.MR.2025.0004232.xml","size":146458} 
[2025-10-09 18:02:50] local.INFO: XML Debug - Trade line pattern {"reference":"IMPORT.EU.MR.2025.0004232","pattern":"//ns3:IncludedSPSTradeLineItem","count":21} 
[2025-10-09 18:02:50] local.INFO: XML Debug - First trade line structure {"reference":"IMPORT.EU.MR.2025.0004232","xml_snippet":"<ns5:IncludedSPSTradeLineItem><ns5:SequenceNumeric>0</ns5:SequenceNumeric><ns5:Description>Consignment totals and summary</ns5:Description><ns5:NetWeightMeasure unitCode=\"KGM\">22151.7</ns5:NetWeightMeasure><ns5:GrossWeightMeasure unitCode=\"KGM\">23040.7</ns5:GrossWeightMeasure><ns5:PhysicalSPSPackage><ns5:LevelCode name=\"No packaging hierarchy\">4</ns5:LevelCode><ns5:TypeCode name=\"Carton\">CT</ns5:TypeCode><ns5:ItemQuantity>889.0</ns5:ItemQuantity></ns5:PhysicalSPSPackage><ns5:OriginSPSCountry><ns5:ID>MR</ns5:ID><ns5:Name languageID=\"fr\">Mauritanie</ns5:Name></ns5:OriginSPSCountry></ns5:IncludedSPSTradeLineItem>"} 
[2025-10-09 18:02:50] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004232","pattern":"//ns5:SPSConsignment","count":1} 
[2025-10-09 18:02:50] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004232","pattern":"//SPSConsignment","count":0} 
[2025-10-09 18:02:50] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004232","pattern":"//*[local-name()=\"SPSConsignment\"]","count":1} 
[2025-10-09 18:02:50] local.INFO: Found trade line items {"import_id":"IMPORT.EU.MR.2025.0004232","count":21} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:02:50] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:02:50] local.INFO: Certificate data ready for storage {"reference":"IMPORT.EU.MR.2025.0004232","data_keys":["id","name","issue_date_time","type_code","type_name","status_code","status_name","consignments","parties","locations","referenced_documents","authentication","notes","raw_xml"]} 
[2025-10-09 18:03:05] local.INFO: TRACES API Get EU-Import Certificate by Reference {"import_reference":"IMPORT.EU.MR.2025.0004154","endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","timestamp":"2025-10-09 18:03:05 GMT"} 
[2025-10-09 18:03:05] local.INFO: TRACES EU-Import API Request {"endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","soap_action":"getEuImportCertificate","request_size":1904,"timeout":1200,"verify_ssl":true} 
[2025-10-09 18:03:08] local.INFO: TRACES API Get EU-Import Certificate Response {"import_reference":"IMPORT.EU.MR.2025.0004154","response_size":131162} 
[2025-10-09 18:03:08] local.INFO: EU-Import XML response saved {"filename":"eu_import_certificate_2025-10-09_18-03-08.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_certificate_2025-10-09_18-03-08.xml","size":131162} 
[2025-10-09 18:03:08] local.INFO: EU-Import XML response saved {"filename":"eu_import_IMPORT.EU.MR.2025.0004154.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_IMPORT.EU.MR.2025.0004154.xml","size":131162} 
[2025-10-09 18:03:08] local.INFO: XML Debug - Trade line pattern {"reference":"IMPORT.EU.MR.2025.0004154","pattern":"//ns3:IncludedSPSTradeLineItem","count":18} 
[2025-10-09 18:03:08] local.INFO: XML Debug - First trade line structure {"reference":"IMPORT.EU.MR.2025.0004154","xml_snippet":"<ns4:IncludedSPSTradeLineItem><ns4:SequenceNumeric>0</ns4:SequenceNumeric><ns4:Description>Consignment totals and summary</ns4:Description><ns4:NetWeightMeasure unitCode=\"KGM\">21147.7</ns4:NetWeightMeasure><ns4:GrossWeightMeasure unitCode=\"KGM\">21912.7</ns4:GrossWeightMeasure><ns4:PhysicalSPSPackage><ns4:LevelCode name=\"No packaging hierarchy\">4</ns4:LevelCode><ns4:TypeCode name=\"Carton\">CT</ns4:TypeCode><ns4:ItemQuantity>765.0</ns4:ItemQuantity></ns4:PhysicalSPSPackage><ns4:OriginSPSCountry><ns4:ID>MR</ns4:ID><ns4:Name languageID=\"fr\">Mauritanie</ns4:Name></ns4:OriginSPSCountry></ns4:IncludedSPSTradeLineItem>"} 
[2025-10-09 18:03:08] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004154","pattern":"//ns5:SPSConsignment","count":1} 
[2025-10-09 18:03:08] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004154","pattern":"//SPSConsignment","count":0} 
[2025-10-09 18:03:08] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0004154","pattern":"//*[local-name()=\"SPSConsignment\"]","count":1} 
[2025-10-09 18:03:08] local.INFO: Found trade line items {"import_id":"IMPORT.EU.MR.2025.0004154","count":18} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":"21147.7","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"1","scientific_name":"Loligo vulgaris","net_weight":"4281.2","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"1","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"1","system_id":"CN","class_code":"03074331","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","Loligo spp","Loligo vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"1","system_id":"FAO_ASFIS","class_code":"SQR","class_names":["Loligo vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"1","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"1","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"1","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"2","scientific_name":"Sepia officinalis","net_weight":"3864","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"2","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"2","system_id":"CN","class_code":"03074329","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","Sepiola spp","Sepia officinalis, Rossia macrosoma"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"2","system_id":"FAO_ASFIS","class_code":"CTC","class_names":["Sepia officinalis"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"2","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"2","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"2","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"3","scientific_name":"Dicologlossa cuneata","net_weight":"39","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"3","count":6} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"EPPT","class_code":"DCLGCU","class_names":["Dicologlossa cuneata"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"FAO_ASFIS","class_code":"CET","class_names":["Dicologlossa cuneata"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"3","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"4","scientific_name":"Sepia spp","net_weight":"78.9","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"4","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"4","system_id":"CN","class_code":"03074399","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","autres"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"4","system_id":"FAO_ASFIS","class_code":"IAX","class_names":["Sepia spp"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"4","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"4","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"4","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"5","scientific_name":"Microchirus azevia","net_weight":"430.5","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"5","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"5","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"5","system_id":"FAO_ASFIS","class_code":"MIA","class_names":["Microchirus azevia"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"5","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"5","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"5","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"6","scientific_name":"Octopus vulgaris","net_weight":"2818.8","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"6","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"6","system_id":"CN","class_code":"03075200","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Poulpes ou pieuvres (Octopus spp.)","congelées","congelées"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"6","system_id":"FAO_ASFIS","class_code":"OCC","class_names":["Octopus vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"6","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"6","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"6","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"7","scientific_name":"Sepia spp","net_weight":"51","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"7","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"7","system_id":"CN","class_code":"03074399","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","autres"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"7","system_id":"FAO_ASFIS","class_code":"IAX","class_names":["Sepia spp"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"7","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"7","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"7","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"8","scientific_name":"Cynoglossus senegalensis","net_weight":"163.1","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"8","count":6} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"EPPT","class_code":"CYNOSE","class_names":["Cynoglossus senegalensis"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"FAO_ASFIS","class_code":"YOE","class_names":["Cynoglossus senegalensis"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"8","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"9","scientific_name":"Octopus vulgaris","net_weight":"2556.4","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"9","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"9","system_id":"CN","class_code":"03075200","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Poulpes ou pieuvres (Octopus spp.)","congelées","congelées"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"9","system_id":"FAO_ASFIS","class_code":"OCC","class_names":["Octopus vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"9","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"9","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"9","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"10","scientific_name":"Sepia spp","net_weight":"241.2","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"10","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"10","system_id":"CN","class_code":"03074399","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","autres"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"10","system_id":"FAO_ASFIS","class_code":"IAX","class_names":["Sepia spp"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"10","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"10","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"10","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"11","scientific_name":"Microchirus azevia","net_weight":"532.5","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"11","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"11","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"11","system_id":"FAO_ASFIS","class_code":"MIA","class_names":["Microchirus azevia"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"11","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"11","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"11","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"12","scientific_name":"Octopus vulgaris","net_weight":"3536.9","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"12","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"12","system_id":"CN","class_code":"03075200","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Poulpes ou pieuvres (Octopus spp.)","congelées","congelées"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"12","system_id":"FAO_ASFIS","class_code":"OCC","class_names":["Octopus vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"12","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"12","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"12","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"13","scientific_name":"Loligo vulgaris","net_weight":"471.5","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"13","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"13","system_id":"CN","class_code":"03074331","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","Loligo spp","Loligo vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"13","system_id":"FAO_ASFIS","class_code":"SQR","class_names":["Loligo vulgaris"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"13","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"13","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"13","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"14","scientific_name":"Sepia officinalis","net_weight":"63.9","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"14","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"14","system_id":"CN","class_code":"03074329","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","Sepiola spp","Sepia officinalis, Rossia macrosoma"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"14","system_id":"FAO_ASFIS","class_code":"CTC","class_names":["Sepia officinalis"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"14","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"14","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"14","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"15","scientific_name":"Sepia spp","net_weight":"296.8","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"15","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"15","system_id":"CN","class_code":"03074399","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Mollusques, même séparés de leur coquille, vivants, frais, réfrigérés,  congelés, séchés, salés ou en saumure; mollusques, même décortiqués,  fumés, même cuits avant ou pendant le fumage:","Seiches  et sépioles; calmars et encornets.","congelées","autres"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"15","system_id":"FAO_ASFIS","class_code":"IAX","class_names":["Sepia spp"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"15","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"15","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"15","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"16","scientific_name":"Microchirus azevia","net_weight":"1590","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"16","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"16","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"16","system_id":"FAO_ASFIS","class_code":"MIA","class_names":["Microchirus azevia"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"16","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"16","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"16","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Parsing product data {"sequence":"17","scientific_name":"Arnoglossus kessleri","net_weight":"132","unit":"KGM"} 
[2025-10-09 18:03:08] local.INFO: Found classifications {"sequence":"17","count":5} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"17","system_id":"CN","class_code":"03033985","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons congelés, à l'exception des filets de poissons et autre chair de poissons du n° 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, oeufs et laitances","autres","autres que. 0303 39 10; 0303 39 30; 0303 93 50;"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"17","system_id":"FAO_ASFIS","class_code":"RKZ","class_names":["Arnoglossus kessleri"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"17","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"17","system_id":"TREATMENT_TYPE","class_code":"FROZEN","class_names":["Congelé(e)"]} 
[2025-10-09 18:03:08] local.INFO: Found classification {"sequence":"17","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:03:08] local.INFO: Certificate data ready for storage {"reference":"IMPORT.EU.MR.2025.0004154","data_keys":["id","name","issue_date_time","type_code","type_name","status_code","status_name","consignments","parties","locations","referenced_documents","authentication","notes","raw_xml"]} 
[2025-10-09 18:03:21] local.INFO: TRACES API Get EU-Import Certificate by Reference {"import_reference":"IMPORT.EU.MR.2025.0003940","endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","timestamp":"2025-10-09 18:03:21 GMT"} 
[2025-10-09 18:03:21] local.INFO: TRACES EU-Import API Request {"endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","soap_action":"getEuImportCertificate","request_size":1904,"timeout":1200,"verify_ssl":true} 
[2025-10-09 18:03:23] local.INFO: TRACES API Get EU-Import Certificate Response {"import_reference":"IMPORT.EU.MR.2025.0003940","response_size":87438} 
[2025-10-09 18:03:23] local.INFO: EU-Import XML response saved {"filename":"eu_import_certificate_2025-10-09_18-03-23.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_certificate_2025-10-09_18-03-23.xml","size":87438} 
[2025-10-09 18:03:23] local.INFO: EU-Import XML response saved {"filename":"eu_import_IMPORT.EU.MR.2025.0003940.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_IMPORT.EU.MR.2025.0003940.xml","size":87438} 
[2025-10-09 18:03:23] local.INFO: XML Debug - Trade line pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns3:IncludedSPSTradeLineItem","count":9} 
[2025-10-09 18:03:23] local.INFO: XML Debug - First trade line structure {"reference":"IMPORT.EU.MR.2025.0003940","xml_snippet":"<ns5:IncludedSPSTradeLineItem><ns5:SequenceNumeric>0</ns5:SequenceNumeric><ns5:Description>Consignment totals and summary</ns5:Description><ns5:NetWeightMeasure unitCode=\"KGM\">544.4</ns5:NetWeightMeasure><ns5:GrossWeightMeasure unitCode=\"KGM\">609</ns5:GrossWeightMeasure><ns5:PhysicalSPSPackage><ns5:LevelCode name=\"No packaging hierarchy\">4</ns5:LevelCode><ns5:TypeCode name=\"Boîte en polystyrène\">QR</ns5:TypeCode><ns5:ItemQuantity>43.0</ns5:ItemQuantity></ns5:PhysicalSPSPackage><ns5:OriginSPSCountry><ns5:ID>MR</ns5:ID><ns5:Name languageID=\"fr\">Mauritanie</ns5:Name></ns5:OriginSPSCountry></ns5:IncludedSPSTradeLineItem>"} 
[2025-10-09 18:03:23] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns5:SPSConsignment","count":1} 
[2025-10-09 18:03:23] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//SPSConsignment","count":0} 
[2025-10-09 18:03:23] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//*[local-name()=\"SPSConsignment\"]","count":1} 
[2025-10-09 18:03:23] local.INFO: Found trade line items {"import_id":"IMPORT.EU.MR.2025.0003940","count":9} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":null,"unit":null} 
[2025-10-09 18:03:23] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:03:23] local.INFO: Certificate data ready for storage {"reference":"IMPORT.EU.MR.2025.0003940","data_keys":["id","name","issue_date_time","type_code","type_name","status_code","status_name","consignments","parties","locations","referenced_documents","authentication","notes","raw_xml"]} 
[2025-10-09 18:04:59] local.INFO: TRACES API Get EU-Import Certificate by Reference {"import_reference":"IMPORT.EU.MR.2025.0003940","endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","timestamp":"2025-10-09 18:04:59 GMT"} 
[2025-10-09 18:04:59] local.INFO: TRACES EU-Import API Request {"endpoint":"https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01","soap_action":"getEuImportCertificate","request_size":1904,"timeout":1200,"verify_ssl":true} 
[2025-10-09 18:05:03] local.INFO: TRACES API Get EU-Import Certificate Response {"import_reference":"IMPORT.EU.MR.2025.0003940","response_size":87438} 
[2025-10-09 18:05:03] local.INFO: EU-Import XML response saved {"filename":"eu_import_certificate_2025-10-09_18-05-03.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_certificate_2025-10-09_18-05-03.xml","size":87438} 
[2025-10-09 18:05:03] local.INFO: EU-Import XML response saved {"filename":"eu_import_IMPORT.EU.MR.2025.0003940.xml","path":"C:\\laragon\\www\\sionispa\\storage\\app/traces_responses/eu_import_IMPORT.EU.MR.2025.0003940.xml","size":87438} 
[2025-10-09 18:05:03] local.INFO: XML Debug - Trade line pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns3:IncludedSPSTradeLineItem","count":9} 
[2025-10-09 18:05:03] local.INFO: XML Debug - First trade line structure {"reference":"IMPORT.EU.MR.2025.0003940","xml_snippet":"<ns4:IncludedSPSTradeLineItem><ns4:SequenceNumeric>0</ns4:SequenceNumeric><ns4:Description>Consignment totals and summary</ns4:Description><ns4:NetWeightMeasure unitCode=\"KGM\">544.4</ns4:NetWeightMeasure><ns4:GrossWeightMeasure unitCode=\"KGM\">609</ns4:GrossWeightMeasure><ns4:PhysicalSPSPackage><ns4:LevelCode name=\"No packaging hierarchy\">4</ns4:LevelCode><ns4:TypeCode name=\"Boîte en polystyrène\">QR</ns4:TypeCode><ns4:ItemQuantity>43.0</ns4:ItemQuantity></ns4:PhysicalSPSPackage><ns4:OriginSPSCountry><ns4:ID>MR</ns4:ID><ns4:Name languageID=\"fr\">Mauritanie</ns4:Name></ns4:OriginSPSCountry></ns4:IncludedSPSTradeLineItem>"} 
[2025-10-09 18:05:03] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//ns5:SPSConsignment","count":1} 
[2025-10-09 18:05:03] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//SPSConsignment","count":0} 
[2025-10-09 18:05:03] local.INFO: XML Debug - Consignment pattern {"reference":"IMPORT.EU.MR.2025.0003940","pattern":"//*[local-name()=\"SPSConsignment\"]","count":1} 
[2025-10-09 18:05:03] local.INFO: Found trade line items {"import_id":"IMPORT.EU.MR.2025.0003940","count":9} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":null,"scientific_name":null,"net_weight":"544.4","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":null,"count":0} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"1","scientific_name":"Epinephelus aeneus","net_weight":"181","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"1","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"1","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"1","system_id":"FAO_ASFIS","class_code":"GPW","class_names":["Epinephelus aeneus"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"1","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"1","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"1","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"2","scientific_name":"Epinephelus marginatus","net_weight":"3.3","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"2","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"2","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"2","system_id":"FAO_ASFIS","class_code":"GPD","class_names":["Epinephelus marginatus"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"2","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"2","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"2","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"3","scientific_name":"Pagrus auriga","net_weight":"61.5","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"3","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"3","system_id":"CN","class_code":"03028590","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","Dorades (Sparidés) (Sparidae)","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"3","system_id":"FAO_ASFIS","class_code":"REA","class_names":["Pagrus auriga"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"3","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"3","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"3","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"4","scientific_name":"Epinephelus costae","net_weight":"17.3","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"4","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"4","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"4","system_id":"FAO_ASFIS","class_code":"EPK","class_names":["Epinephelus costae"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"4","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"4","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"4","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"5","scientific_name":"Pagrus caeruleostictus","net_weight":"75.7","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"5","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"5","system_id":"CN","class_code":"03028590","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","Dorades (Sparidés) (Sparidae)","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"5","system_id":"FAO_ASFIS","class_code":"BSC","class_names":["Pagrus caeruleostictus"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"5","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"5","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"5","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"6","scientific_name":"Mycteroperca rubra","net_weight":"7.5","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"6","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"6","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"6","system_id":"FAO_ASFIS","class_code":"MKU","class_names":["Mycteroperca rubra"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"6","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"6","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"6","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"7","scientific_name":"Solea senegalensis","net_weight":"99.6","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"7","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"7","system_id":"CN","class_code":"03022300","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Poissons plats (Pleuronectidés, Bothidés, Cynoglossidés, Soléidés, Scophthalmidés et Citharidés), à l'exclusion des foies, œufs et laitances","Soles (Solea spp.)","Soles (Solea spp.)"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"7","system_id":"FAO_ASFIS","class_code":"OAL","class_names":["Solea senegalensis"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"7","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"7","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"7","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Parsing product data {"sequence":"8","scientific_name":"Pseudupeneus prayensis","net_weight":"98.5","unit":"KGM"} 
[2025-10-09 18:05:03] local.INFO: Found classifications {"sequence":"8","count":5} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"8","system_id":"CN","class_code":"03028990","class_names":["POISSONS ET CRUSTACÉS, MOLLUSQUES ET AUTRES INVERTÉBRÉS AQUATIQUES","Poissons frais ou réfrigérés, à l'exception des filets de poissons et autre chair de poissons du nº 0304","Autres poissons, à l’exclusion des abats de poissons comestibles des sous-positions 0302 91 à 0302 99","autres","autres"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"8","system_id":"FAO_ASFIS","class_code":"GOA","class_names":["Pseudupeneus prayensis"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"8","system_id":"NATURE_OF_COMMODITY","class_code":"WILD_STOCK","class_names":["Produits de la pêche, Stocks sauvages"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"8","system_id":"TREATMENT_TYPE","class_code":"CHILLED","class_names":["Réfrigérée"]} 
[2025-10-09 18:05:03] local.INFO: Found classification {"sequence":"8","system_id":"FINAL_CONSUMER","class_code":"FALSE","class_names":[""]} 
[2025-10-09 18:05:03] local.INFO: Certificate data ready for storage {"reference":"IMPORT.EU.MR.2025.0003940","data_keys":["id","name","issue_date_time","type_code","type_name","status_code","status_name","consignments","parties","locations","referenced_documents","authentication","notes","raw_xml"]} 
