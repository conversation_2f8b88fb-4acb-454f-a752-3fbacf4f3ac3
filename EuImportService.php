<?php


namespace App\Http\Controllers;

// Ensure required classes are available
if (!class_exists('DateTime')) {
    throw new Exception('DateTime class not found. Please check your PHP installation.');
}

if (!class_exists('DateTimeZone')) {
    throw new Exception('DateTimeZone class not found. Please check your PHP installation.');
}

if (!class_exists('DOMDocument')) {
    throw new Exception('DOMDocument class not found. Please install php-xml extension.');
}

if (!class_exists('DOMXPath')) {
    throw new Exception('DOMXPath class not found. Please install php-xml extension.');
}

/**
 * EU Import Certificate Service for Laravel 5.4
 * 
 * This service provides functionality to fetch EU Import certificates from TRACES API
 * and store them in the database. It's adapted from the newer Laravel project to work
 * with Laravel 5.4.
 * 
 * Usage in your controller:
 * 
 * $service = new EuImportService();
 * $result = $service->fetchEuImport($searchTerm, $forceRefresh);
 * 
 * <AUTHOR> for Laravel 5.4
 * @version 1.0
 */
class EuImportService
{
    // TRACES API Configuration
    private $tracesUsername = 'n00385tm';
    private $tracesAuthKey = '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh';
    private $tracesClientId = 'onispa-mr';
    private $useProduction = true;
    private $timeout = 1200;
    private $verifySSL = true;

    /**
     * Main method to fetch EU Import certificate data
     * 
     * @param string $reference The EU Import reference number
     * @param bool $forceRefresh Whether to force refresh from API
     * @param bool $saveXml Whether to save XML response to file
     * @return array Response data
     */
    public function fetchEuImport($reference, $forceRefresh = true, $saveXml = true)
    {
        try {


            // Fetch from TRACES API
            $certificateData = $this->fetchCertificateFromApi($reference, $saveXml);

            if (!$certificateData) {
                return [
                    'success' => false,
                    'message' => 'Certificate not found in TRACES API',
                    'reference' => $reference
                ];
            }

            // Parse and format products
            $products = [];
            if (isset($certificateData['raw_xml'])) {
                // Debug: Save XML and analyze structure
                $this->debugXmlStructure($certificateData['raw_xml'], $reference);
                $products = $this->parseProductsFromXml($certificateData['raw_xml'], $reference);
            }

            // Store in database if you have tables set up
            $this->storeCertificateData($certificateData, $reference);

            return [
                'success' => true,
                'message' => 'Certificate retrieved and stored successfully',
                'source' => 'traces_api',
                'data' => $certificateData,
                'products' => $products,
                'reference' => $reference
            ];

        } catch (\Exception $e) {
            \Log::error('EU-Import certificate fetch error', [
                'reference' => $reference,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to retrieve certificate: ' . $e->getMessage(),
                'reference' => $reference
            ];
        }
    }

    /**
     * Fetch certificate from TRACES API
     */
    private function fetchCertificateFromApi($reference, $saveXml = true)
    {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';

        \Log::info('TRACES API Get EU-Import Certificate by Reference', [
            'import_reference' => $reference,
            'endpoint' => $endpoint,
            'timestamp' => gmdate('Y-m-d H:i:s T')
        ]);

        $soapRequest = $this->createGetEuImportCertificateRequest($reference);

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportCertificate');
            
            \Log::info('TRACES API Get EU-Import Certificate Response', [
                'import_reference' => $reference,
                'response_size' => strlen($response)
            ]);
            
            $parsedData = $this->parseEuImportCertificateResponse($response);
            // Add the raw XML to the parsed data for storage
            $parsedData['raw_xml'] = $response;
            
            if ($saveXml) {
                $this->saveXmlResponse($response, 'eu_import_' . $reference . '.xml');
            }
            
            return $parsedData;
        } catch (\Exception $e) {
            // If we get an authentication error, try different timestamp methods
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed for getEuImportCertificate, trying alternative methods', [
                    'error' => $e->getMessage(),
                    'import_reference' => $reference
                ]);

                return $this->tryAlternativeAuthMethodsForGetEuImportCertificate($reference, $endpoint);
            }

            throw $e;
        }
    }

    /**
     * Create SOAP request for getEuImportCertificate method
     */
    private function createGetEuImportCertificateRequest($importReference)
    {
        $body = '<euimport:GetEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($importReference) . '</euimport:ID>
        </euimport:GetEuImportCertificateRequest>';

        return $this->createSoapRequest($body);
    }

    /**
     * Create base SOAP request with authentication headers
     */
    private function createSoapRequest($body)
    {
        date_default_timezone_set('UTC');

        $nonceRaw = $this->generateRandomBytes(16);
        $nonce = base64_encode($nonceRaw);

        // Get current time and apply timestamp adjustments for TRACES API synchronization
        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // TRACES API timestamp synchronization fixes:
        // 1. Subtract a few seconds to account for network delay and server time drift
        $now->modify('-10 seconds');

        // 2. Use longer expiration time (15 minutes instead of 5) as suggested in docs
        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+15 minutes')->format('Y-m-d\TH:i:s\Z');

        // Create password digest: Base64(SHA1(nonce + created + password))
        $passwordDigest = base64_encode(sha1($nonceRaw . $created . $this->tracesAuthKey, true));

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
    xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->tracesUsername) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode>fr</v3:LanguageCode>
        <v3:WebServiceClientId>' . htmlspecialchars($this->tracesClientId) . '</v3:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        ' . $body . '
    </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * Generate random bytes (compatible with PHP 5.6+)
     */
    private function generateRandomBytes($length)
    {
        if (function_exists('random_bytes')) {
            return random_bytes($length);
        }
        
        // Fallback for older PHP versions
        $bytes = '';
        for ($i = 0; $i < $length; $i++) {
            $bytes .= chr(mt_rand(0, 255));
        }
        return $bytes;
    }

    /**
     * Send SOAP request to TRACES API
     */
    private function sendSoapRequest($endpoint, $soapRequest, $soapAction)
    {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "' . $soapAction . '"',
            'Content-Length: ' . strlen($soapRequest),
            'User-Agent: ONISPA-Laravel-EuImport-Client/1.0'
        ];

        // Log request for debugging
        \Log::info('TRACES EU-Import API Request', [
            'endpoint' => $endpoint,
            'soap_action' => $soapAction,
            'request_size' => strlen($soapRequest),
            'timeout' => $this->timeout,
            'verify_ssl' => $this->verifySSL
        ]);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $endpoint,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => $this->verifySSL,
            CURLOPT_SSL_VERIFYHOST => $this->verifySSL ? 2 : 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'ONISPA-Laravel-EuImport-Client/1.0',
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            throw new \Exception("CURL Error: $curlError");
        }

        if ($httpCode !== 200) {
            \Log::error('TRACES EU-Import API HTTP Error', [
                'http_code' => $httpCode,
                'response_preview' => substr($response, 0, 1000)
            ]);
            throw new \Exception("HTTP Error $httpCode: " . substr($response, 0, 500));
        }

        // Check for authentication errors in the response
        if (strpos($response, 'Unauthenticated\Exception') !== false) {
            \Log::error('TRACES EU-Import API Authentication Error', [
                'response_preview' => substr($response, 0, 1000)
            ]);
            throw new \Exception('Authentication failed: Invalid credentials or expired session');
        }

        return $response;
    }

    /**
     * Parse EU-Import certificate XML response
     */
    private function parseEuImportCertificateResponse($xmlResponse)
    {
        // Save the raw response for analysis
        $this->saveXmlResponse($xmlResponse, 'eu_import_certificate_' . date('Y-m-d_H-i-s') . '.xml');

        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);

        if (!$dom->loadXML($xmlResponse)) {
            $errors = libxml_get_errors();
            $errorMessages = array_map(function($e) { return $e->message; }, $errors);
            throw new \Exception("Failed to parse XML response:\n" . implode("\n", $errorMessages));
        }

        $xpath = new \DOMXPath($dom);
        $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xpath->registerNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
        $xpath->registerNamespace('ns3', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
        $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
        $xpath->registerNamespace('ns5', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
        $xpath->registerNamespace('ns6', 'http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01');

        // Check for SOAP faults
        $faultNodes = $xpath->query('//S:Fault | //soap:Fault');
        if ($faultNodes->length > 0) {
            $faultCode = $xpath->query('.//faultcode', $faultNodes->item(0))->item(0);
            $faultString = $xpath->query('.//faultstring', $faultNodes->item(0))->item(0);
            $faultCodeValue = $faultCode ? $faultCode->nodeValue : 'Unknown';
            $faultStringValue = $faultString ? $faultString->nodeValue : 'Unknown error';
            throw new \Exception("SOAP Fault: $faultCodeValue - $faultStringValue");
        }

        // Find the SPSCertificate element - it's nested in the response
        $certificateNodes = $xpath->query('//ns6:SPSCertificate');
        if ($certificateNodes->length === 0) {
            throw new \Exception('No SPSCertificate found in response');
        }

        $certificate = $certificateNodes->item(0);

        // Parse the certificate data according to UN/CEFACT structure
        // The SPSExchangedDocument contains the main certificate info
        $exchangedDoc = $xpath->query('.//ns5:SPSExchangedDocument', $certificate)->item(0);

        $certificateData = [
            'id' => $this->getNodeValue($xpath->query('.//ns3:ID', $exchangedDoc), 0),
            'name' => $this->getNodeValue($xpath->query('.//ns3:Name', $exchangedDoc), 0),
            'issue_date_time' => $this->getNodeValue($xpath->query('.//ns3:IssueDateTime/ns4:DateTime', $exchangedDoc), 0),
            'type_code' => $this->getNodeValue($xpath->query('.//ns3:TypeCode', $exchangedDoc), 0),
            'type_name' => $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $exchangedDoc), 0, 'name'),
            'status_code' => $this->getNodeValue($xpath->query('.//ns3:StatusCode', $exchangedDoc), 0),
            'status_name' => $this->getAttributeValue($xpath->query('.//ns3:StatusCode', $exchangedDoc), 0, 'name'),
            'consignments' => [],
            'parties' => [],
            'locations' => [],
            'referenced_documents' => [],
            'authentication' => [],
            'notes' => []
        ];

        // Parse consignments
        $consignmentNodes = $xpath->query('.//ns5:SPSConsignment', $certificate);
        foreach ($consignmentNodes as $consignment) {
            $consignmentData = [
                'export_exit_date_time' => $this->getNodeValue($xpath->query('.//ns3:ExportExitDateTime/ns4:DateTime', $consignment), 0),
                'transport_movement' => [],
                'consignment_items' => [],
                'parties' => []
            ];

            // Parse consignment items
            $itemNodes = $xpath->query('.//ns3:IncludedSPSConsignmentItem', $consignment);
            foreach ($itemNodes as $item) {
                $itemData = [
                    'nature_identification' => [],
                    'trade_lines' => []
                ];

                // Parse nature identification
                $natureNodes = $xpath->query('.//ns3:NatureIdentificationSPSCargo', $item);
                foreach ($natureNodes as $nature) {
                    $itemData['nature_identification'][] = [
                        'type_code' => $this->getNodeValue($xpath->query('.//ns3:TypeCode', $nature), 0),
                        'type_name' => $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $nature), 0, 'name')
                    ];
                }

                // Parse trade line items (products)
                $tradeLineNodes = $xpath->query('.//ns3:IncludedSPSTradeLineItem', $item);
                foreach ($tradeLineNodes as $tradeLine) {
                    $tradeLineData = [
                        'sequence_numeric' => $this->getNodeValue($xpath->query('.//ns3:SequenceNumeric', $tradeLine), 0),
                        'description' => $this->getNodeValue($xpath->query('.//ns3:Description', $tradeLine), 0),
                        'scientific_name' => $this->getNodeValue($xpath->query('.//ns3:ScientificName', $tradeLine), 0),
                        'production_batch_id' => $this->getNodeValue($xpath->query('.//ns3:ProductionBatchID', $tradeLine), 0),
                        'net_weight' => $this->getNodeValue($xpath->query('.//ns3:NetWeightMeasure', $tradeLine), 0),
                        'net_weight_unit' => $this->getAttributeValue($xpath->query('.//ns3:NetWeightMeasure', $tradeLine), 0, 'unitCode'),
                        'gross_weight' => $this->getNodeValue($xpath->query('.//ns3:GrossWeightMeasure', $tradeLine), 0),
                        'gross_weight_unit' => $this->getAttributeValue($xpath->query('.//ns3:GrossWeightMeasure', $tradeLine), 0, 'unitCode'),
                        'classifications' => [],
                        'packages' => [],
                        'processes' => []
                    ];

                    // Parse classifications (CN codes, FAO codes, etc.)
                    $classificationNodes = $xpath->query('.//ns3:ApplicableSPSClassification', $tradeLine);
                    foreach ($classificationNodes as $classification) {
                        $tradeLineData['classifications'][] = [
                            'system_id' => $this->getNodeValue($xpath->query('.//ns3:SystemID', $classification), 0),
                            'system_name' => $this->getNodeValue($xpath->query('.//ns3:SystemName', $classification), 0),
                            'class_code' => $this->getNodeValue($xpath->query('.//ns3:ClassCode', $classification), 0),
                            'class_name' => $this->getNodeValue($xpath->query('.//ns3:ClassName', $classification), 0)
                        ];
                    }

                    // Parse packages
                    $packageNodes = $xpath->query('.//ns3:PhysicalSPSPackage', $tradeLine);
                    foreach ($packageNodes as $package) {
                        $tradeLineData['packages'][] = [
                            'level_code' => $this->getNodeValue($xpath->query('.//ns3:LevelCode', $package), 0),
                            'level_name' => $this->getAttributeValue($xpath->query('.//ns3:LevelCode', $package), 0, 'name'),
                            'type_code' => $this->getNodeValue($xpath->query('.//ns3:TypeCode', $package), 0),
                            'type_name' => $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $package), 0, 'name'),
                            'item_quantity' => $this->getNodeValue($xpath->query('.//ns3:ItemQuantity', $package), 0)
                        ];
                    }

                    $itemData['trade_lines'][] = $tradeLineData;
                }

                $consignmentData['consignment_items'][] = $itemData;
            }

            $certificateData['consignments'][] = $consignmentData;
        }

        return $certificateData;
    }

    /**
     * Helper method to get node value safely
     */
    private function getNodeValue($nodeList, $index)
    {
        return ($nodeList && $nodeList->length > $index && $nodeList->item($index))
            ? $nodeList->item($index)->nodeValue
            : null;
    }

    /**
     * Helper method to get attribute value safely
     */
    private function getAttributeValue($nodeList, $index, $attributeName)
    {
        if ($nodeList && $nodeList->length > $index && $nodeList->item($index)) {
            $attribute = $nodeList->item($index)->getAttribute($attributeName);
            return $attribute ?: null;
        }
        return null;
    }

    /**
     * Parse products from XML and format them for response
     */
    private function parseProductsFromXml($xmlContent, $importId)
    {
        $products = [];

        try {
            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlContent)) {
                \Log::warning('Failed to parse XML for products', ['import_id' => $importId]);
                return $products;
            }

            $xpath = new \DOMXPath($dom);
            $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
            $xpath->registerNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
            $xpath->registerNamespace('ns3', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
            $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
            $xpath->registerNamespace('ns5', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
            $xpath->registerNamespace('ns6', 'http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01');

            // Find all trade line items (products) - try multiple XPath patterns
            $tradeLineNodes = $xpath->query('//ns3:IncludedSPSTradeLineItem');

            // If not found, try alternative paths
            if ($tradeLineNodes->length === 0) {
                $tradeLineNodes = $xpath->query('//IncludedSPSTradeLineItem');
            }

            \Log::info('Found trade line items', [
                'import_id' => $importId,
                'count' => $tradeLineNodes->length
            ]);

            foreach ($tradeLineNodes as $tradeLine) {
                $sequenceNumber = $this->getNodeValue($xpath->query('.//ns4:SequenceNumeric', $tradeLine), 0)
                               ?: $this->getNodeValue($xpath->query('.//ns3:SequenceNumeric', $tradeLine), 0)
                               ?: $this->getNodeValue($xpath->query('.//SequenceNumeric', $tradeLine), 0);

                \Log::info('Processing trade line item', [
                    'sequence_number' => $sequenceNumber,
                    'sequence_type' => gettype($sequenceNumber),
                    'is_zero_string' => ($sequenceNumber === '0'),
                    'is_zero_int' => ($sequenceNumber === 0),
                    'equals_zero' => ($sequenceNumber == '0')
                ]);

                // Skip the summary item (sequence 0)
                if ($sequenceNumber == '0' || $sequenceNumber === '0' || $sequenceNumber === 0) {
                    \Log::info('Skipping summary item', ['sequence' => $sequenceNumber]);
                    continue;
                }

                $productData = $this->parseProductFromTradeLineItem($xpath, $tradeLine, $importId, $sequenceNumber);

                if ($productData) {
                    $products[] = $productData;
                }
            }

        } catch (\Exception $e) {
            \Log::error('Error parsing products from XML', [
                'import_id' => $importId,
                'error' => $e->getMessage()
            ]);
        }

        return $products;
    }

    /**
     * Parse individual product from trade line item
     */
    private function parseProductFromTradeLineItem($xpath, $tradeLine, $importId, $sequenceNumber)
    {
        // Try multiple XPath patterns for each field - use ns4 first (from debug logs)
        $scientificName = $this->getNodeValue($xpath->query('.//ns4:ScientificName', $tradeLine), 0)
                       ?: $this->getNodeValue($xpath->query('.//ns3:ScientificName', $tradeLine), 0)
                       ?: $this->getNodeValue($xpath->query('.//ScientificName', $tradeLine), 0);

        $productionBatchId = $this->getNodeValue($xpath->query('.//ns4:ProductionBatchID', $tradeLine), 0)
                          ?: $this->getNodeValue($xpath->query('.//ns3:ProductionBatchID', $tradeLine), 0)
                          ?: $this->getNodeValue($xpath->query('.//ProductionBatchID', $tradeLine), 0);

        $netWeight = $this->getNodeValue($xpath->query('.//ns4:NetWeightMeasure', $tradeLine), 0)
                  ?: $this->getNodeValue($xpath->query('.//ns3:NetWeightMeasure', $tradeLine), 0)
                  ?: $this->getNodeValue($xpath->query('.//NetWeightMeasure', $tradeLine), 0);

        $netWeightUnit = $this->getAttributeValue($xpath->query('.//ns4:NetWeightMeasure', $tradeLine), 0, 'unitCode')
                      ?: $this->getAttributeValue($xpath->query('.//ns3:NetWeightMeasure', $tradeLine), 0, 'unitCode')
                      ?: $this->getAttributeValue($xpath->query('.//NetWeightMeasure', $tradeLine), 0, 'unitCode');

        $grossWeight = $this->getNodeValue($xpath->query('.//ns4:GrossWeightMeasure', $tradeLine), 0)
                    ?: $this->getNodeValue($xpath->query('.//ns3:GrossWeightMeasure', $tradeLine), 0)
                    ?: $this->getNodeValue($xpath->query('.//GrossWeightMeasure', $tradeLine), 0);

        $grossWeightUnit = $this->getAttributeValue($xpath->query('.//ns4:GrossWeightMeasure', $tradeLine), 0, 'unitCode')
                        ?: $this->getAttributeValue($xpath->query('.//ns3:GrossWeightMeasure', $tradeLine), 0, 'unitCode')
                        ?: $this->getAttributeValue($xpath->query('.//GrossWeightMeasure', $tradeLine), 0, 'unitCode');

        $productData = [
            'import_id' => $importId,
            'sequence_number' => $sequenceNumber,
            'scientific_name' => $scientificName,
            'production_batch_id' => $productionBatchId,
            'net_weight' => $netWeight,
            'net_weight_unit' => $netWeightUnit,
            'gross_weight' => $grossWeight,
            'gross_weight_unit' => $grossWeightUnit,
        ];

        \Log::info('Parsing product data', [
            'sequence' => $sequenceNumber,
            'scientific_name' => $scientificName,
            'net_weight' => $netWeight,
            'unit' => $netWeightUnit
        ]);

        // Parse origin country
        $originCountryNode = $xpath->query('.//ns4:OriginSPSCountry', $tradeLine)->item(0);
        if (!$originCountryNode) {
            $originCountryNode = $xpath->query('.//ns3:OriginSPSCountry', $tradeLine)->item(0);
        }
        if (!$originCountryNode) {
            $originCountryNode = $xpath->query('.//OriginSPSCountry', $tradeLine)->item(0);
        }

        if ($originCountryNode) {
            $productData['origin_country_id'] = $this->getNodeValue($xpath->query('.//ns4:ID', $originCountryNode), 0)
                                             ?: $this->getNodeValue($xpath->query('.//ns3:ID', $originCountryNode), 0)
                                             ?: $this->getNodeValue($xpath->query('.//ID', $originCountryNode), 0);
            $productData['origin_country_name'] = $this->getNodeValue($xpath->query('.//ns4:Name', $originCountryNode), 0)
                                               ?: $this->getNodeValue($xpath->query('.//ns3:Name', $originCountryNode), 0)
                                               ?: $this->getNodeValue($xpath->query('.//Name', $originCountryNode), 0);
        }

        // Parse classifications to extract specific information
        $classifications = [];
        $classificationNodes = $xpath->query('.//ns4:ApplicableSPSClassification', $tradeLine);

        // Try alternative paths if no classifications found
        if ($classificationNodes->length === 0) {
            $classificationNodes = $xpath->query('.//ns3:ApplicableSPSClassification', $tradeLine);
        }
        if ($classificationNodes->length === 0) {
            $classificationNodes = $xpath->query('.//ApplicableSPSClassification', $tradeLine);
        }

        \Log::info('Found classifications', [
            'sequence' => $sequenceNumber,
            'count' => $classificationNodes->length
        ]);

        foreach ($classificationNodes as $classification) {
            $systemId = $this->getNodeValue($xpath->query('.//ns4:SystemID', $classification), 0)
                     ?: $this->getNodeValue($xpath->query('.//ns3:SystemID', $classification), 0)
                     ?: $this->getNodeValue($xpath->query('.//SystemID', $classification), 0);
            $classCode = $this->getNodeValue($xpath->query('.//ns4:ClassCode', $classification), 0)
                      ?: $this->getNodeValue($xpath->query('.//ns3:ClassCode', $classification), 0)
                      ?: $this->getNodeValue($xpath->query('.//ClassCode', $classification), 0);

            // Get all class names
            $classNames = [];
            $classNameNodes = $xpath->query('.//ns4:ClassName', $classification);
            if ($classNameNodes->length === 0) {
                $classNameNodes = $xpath->query('.//ns3:ClassName', $classification);
            }
            if ($classNameNodes->length === 0) {
                $classNameNodes = $xpath->query('.//ClassName', $classification);
            }

            foreach ($classNameNodes as $classNameNode) {
                $classNames[] = $classNameNode->nodeValue;
            }

            $systemName = $this->getNodeValue($xpath->query('.//ns4:SystemName', $classification), 0)
                       ?: $this->getNodeValue($xpath->query('.//ns3:SystemName', $classification), 0)
                       ?: $this->getNodeValue($xpath->query('.//SystemName', $classification), 0);

            $classificationData = [
                'system_id' => $systemId,
                'system_name' => $systemName,
                'class_code' => $classCode,
                'class_names' => $classNames,
                'class_name' => implode(' - ', $classNames)
            ];

            $classifications[] = $classificationData;

            \Log::info('Found classification', [
                'sequence' => $sequenceNumber,
                'system_id' => $systemId,
                'class_code' => $classCode,
                'class_names' => $classNames
            ]);

            // Extract specific classification types
            switch ($systemId) {
                case 'CN':
                    $productData['cn_code'] = $classCode;
                    $productData['cn_description'] = implode(' - ', $classNames);
                    break;

                case 'FAO_ASFIS':
                    $productData['fao_code'] = $classCode;
                    $productData['fao_description'] = implode(' - ', $classNames);
                    break;

                case 'NATURE_OF_COMMODITY':
                    $productData['nature_of_commodity'] = $classCode;
                    $productData['nature_description'] = implode(' - ', $classNames);
                    $productData['is_wild_stock'] = ($classCode === 'WILD_STOCK');
                    break;

                case 'TREATMENT_TYPE':
                    $productData['treatment_type'] = $classCode;
                    $productData['treatment_description'] = implode(' - ', $classNames);
                    break;

                case 'FINAL_CONSUMER':
                    $productData['for_final_consumer'] = ($classCode === 'TRUE');
                    break;
            }
        }

        $productData['all_classifications'] = $classifications;

        // Parse packaging information
        $packageNodes = $xpath->query('.//ns4:PhysicalSPSPackage', $tradeLine);
        if ($packageNodes->length === 0) {
            $packageNodes = $xpath->query('.//ns3:PhysicalSPSPackage', $tradeLine);
        }
        if ($packageNodes->length === 0) {
            $packageNodes = $xpath->query('.//PhysicalSPSPackage', $tradeLine);
        }

        foreach ($packageNodes as $package) {
            $productData['package_type_code'] = $this->getNodeValue($xpath->query('.//ns4:TypeCode', $package), 0)
                                              ?: $this->getNodeValue($xpath->query('.//ns3:TypeCode', $package), 0)
                                              ?: $this->getNodeValue($xpath->query('.//TypeCode', $package), 0);
            $productData['package_type_name'] = $this->getAttributeValue($xpath->query('.//ns4:TypeCode', $package), 0, 'name')
                                              ?: $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $package), 0, 'name')
                                              ?: $this->getAttributeValue($xpath->query('.//TypeCode', $package), 0, 'name');
            $productData['package_level_code'] = $this->getNodeValue($xpath->query('.//ns4:LevelCode', $package), 0)
                                               ?: $this->getNodeValue($xpath->query('.//ns3:LevelCode', $package), 0)
                                               ?: $this->getNodeValue($xpath->query('.//LevelCode', $package), 0);
            $productData['package_level_name'] = $this->getAttributeValue($xpath->query('.//ns4:LevelCode', $package), 0, 'name')
                                               ?: $this->getAttributeValue($xpath->query('.//ns3:LevelCode', $package), 0, 'name')
                                               ?: $this->getAttributeValue($xpath->query('.//LevelCode', $package), 0, 'name');
            $productData['package_quantity'] = $this->getNodeValue($xpath->query('.//ns4:ItemQuantity', $package), 0)
                                             ?: $this->getNodeValue($xpath->query('.//ns3:ItemQuantity', $package), 0)
                                             ?: $this->getNodeValue($xpath->query('.//ItemQuantity', $package), 0);
            break; // Take first package info
        }

        // Parse processing information
        $processes = [];
        $processNodes = $xpath->query('.//ns3:AppliedSPSProcess', $tradeLine);
        foreach ($processNodes as $process) {
            $processData = [
                'type_code' => $this->getNodeValue($xpath->query('.//ns3:TypeCode', $process), 0),
                'type_name' => $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $process), 0, 'name'),
                'completion_start_date' => $this->getNodeValue($xpath->query('.//ns3:CompletionSPSPeriod/ns3:StartDateTime/ns4:DateTime', $process), 0),
            ];

            // Parse operator/processing plant
            $operatorNode = $xpath->query('.//ns3:OperatorSPSParty', $process)->item(0);
            if ($operatorNode) {
                $processData['operator'] = [
                    'id' => $this->getNodeValue($xpath->query('.//ns3:ID', $operatorNode), 0),
                    'name' => $this->getNodeValue($xpath->query('.//ns3:Name', $operatorNode), 0),
                    'type_code' => $this->getNodeValue($xpath->query('.//ns3:TypeCode', $operatorNode), 0),
                    'type_name' => $this->getAttributeValue($xpath->query('.//ns3:TypeCode', $operatorNode), 0, 'name'),
                ];

                // Set processing plant info from first operator
                if (!isset($productData['processing_plant_id'])) {
                    $productData['processing_plant_id'] = $processData['operator']['id'];
                    $productData['processing_plant_name'] = $processData['operator']['name'];
                }
            }

            $processes[] = $processData;
        }

        $productData['processes'] = $processes;

        // Derive common name
        $productData['common_name'] = $this->deriveCommonName(
            $productData['scientific_name'],
            isset($productData['fao_description']) ? $productData['fao_description'] : null
        );

        return $productData;
    }

    /**
     * Derive common name from scientific name and other sources
     */
    private function deriveCommonName($scientificName, $faoDescription)
    {
        // Map of scientific names to common names
        $commonNameMap = [
            'Epinephelus aeneus' => 'White grouper',
            'Epinephelus marginatus' => 'Dusky grouper',
            'Pagrus auriga' => 'Redbanded seabream',
            'Epinephelus costae' => 'Goldblotch grouper',
            'Pagrus caeruleostictus' => 'Bluespotted seabream',
            'Mycteroperca rubra' => 'Mottled grouper',
            'Solea senegalensis' => 'Senegalese sole',
            'Pseudupeneus prayensis' => 'West African goatfish'
        ];

        return isset($commonNameMap[$scientificName]) ? $commonNameMap[$scientificName] : $faoDescription;
    }

    /**
     * Save XML response to file for analysis
     */
    private function saveXmlResponse($xmlContent, $filename = null)
    {
        if (!$filename) {
            $filename = 'eu_import_response_' . date('Y-m-d_H-i-s') . '.xml';
        }

        // Create storage directory if it doesn't exist
        $storagePath = storage_path('app/traces_responses');
        if (!file_exists($storagePath)) {
            mkdir($storagePath, 0755, true);
        }

        $fullPath = $storagePath . '/' . $filename;
        file_put_contents($fullPath, $xmlContent);

        \Log::info('EU-Import XML response saved', [
            'filename' => $filename,
            'path' => $fullPath,
            'size' => strlen($xmlContent)
        ]);

        return $fullPath;
    }

    /**
     * Try alternative authentication methods for getEuImportCertificate
     */
    private function tryAlternativeAuthMethodsForGetEuImportCertificate($reference, $endpoint)
    {
        $attempts = [
            ['offset' => 0, 'description' => 'No time offset'],
            ['offset' => -30, 'description' => '30 seconds back'],
            ['offset' => -60, 'description' => '1 minute back'],
            ['offset' => 30, 'description' => '30 seconds forward']
        ];

        foreach ($attempts as $attempt) {
            try {
                \Log::info("Trying alternative auth method", [
                    'attempt' => $attempt['description'],
                    'reference' => $reference
                ]);

                $soapRequest = $this->createSoapRequestWithTimeOffset(
                    '<euimport:GetEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
                        <euimport:ID>' . htmlspecialchars($reference) . '</euimport:ID>
                    </euimport:GetEuImportCertificateRequest>',
                    $attempt['offset']
                );

                $response = $this->sendSoapRequest($endpoint, $soapRequest, 'getEuImportCertificate');
                $parsedData = $this->parseEuImportCertificateResponse($response);
                $parsedData['raw_xml'] = $response;

                \Log::info("Alternative auth method succeeded", [
                    'attempt' => $attempt['description'],
                    'reference' => $reference
                ]);

                return $parsedData;

            } catch (\Exception $e) {
                \Log::warning("Alternative auth method failed", [
                    'attempt' => $attempt['description'],
                    'reference' => $reference,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }

        throw new \Exception('All authentication methods failed for reference: ' . $reference);
    }

    /**
     * Create SOAP request with time offset for authentication troubleshooting
     */
    private function createSoapRequestWithTimeOffset($body, $offsetSeconds = 0)
    {
        date_default_timezone_set('UTC');

        $nonceRaw = $this->generateRandomBytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new \DateTime('now', new \DateTimeZone('UTC'));
        $now->modify($offsetSeconds . ' seconds');

        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+15 minutes')->format('Y-m-d\TH:i:s\Z');

        $passwordDigest = base64_encode(sha1($nonceRaw . $created . $this->tracesAuthKey, true));

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
    xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->tracesUsername) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode>fr</v3:LanguageCode>
        <v3:WebServiceClientId>' . htmlspecialchars($this->tracesClientId) . '</v3:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        ' . $body . '
    </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * Find existing certificate in database (implement based on your database structure)
     */
    private function findExistingCertificate($reference)
    {
        // If you have a database table for certificates, implement the search here
        // For now, return null to always fetch from API
        return null;

        // Example implementation if you have a certificates table:
        /*
        try {
            $certificate = DB::table('eu_import_certificates')
                ->where('import_id', $reference)
                ->first();

            if ($certificate) {
                return json_decode($certificate->raw_data, true);
            }
        } catch (\Exception $e) {
            \Log::warning('Error checking existing certificate', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
        }

        return null;
        */
    }

    /**
     * Store certificate data in database (implement based on your database structure)
     */
    private function storeCertificateData($certificateData, $reference)
    {
        // Implement database storage based on your table structure
        // For now, just log the data
        \Log::info('Certificate data ready for storage', [
            'reference' => $reference,
            'data_keys' => array_keys($certificateData)
        ]);

        // Example implementation if you have database tables:
        /*
        try {
            DB::table('eu_import_certificates')->updateOrInsert(
                ['import_id' => $reference],
                [
                    'issue_date_time' => $certificateData['issue_date_time'],
                    'type_code' => $certificateData['type_code'],
                    'status_code' => $certificateData['status_code'],
                    'raw_data' => json_encode($certificateData),
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );

            \Log::info('Certificate stored in database', ['reference' => $reference]);
        } catch (\Exception $e) {
            \Log::error('Error storing certificate', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
        }
        */
    }

    /**
     * Debug XML structure to understand the parsing issues
     */
    private function debugXmlStructure($xmlContent, $reference)
    {
        try {
            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlContent)) {
                \Log::error('Failed to load XML for debugging', ['reference' => $reference]);
                return;
            }

            $xpath = new \DOMXPath($dom);

            // Register all possible namespaces
            $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
            $xpath->registerNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
            $xpath->registerNamespace('ns3', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
            $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
            $xpath->registerNamespace('ns5', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
            $xpath->registerNamespace('ns6', 'http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01');

            // Try to find trade line items with different patterns
            $patterns = [
                '//ns3:IncludedSPSTradeLineItem',
                '//IncludedSPSTradeLineItem',
                '//*[local-name()="IncludedSPSTradeLineItem"]',
                '//ns5:SPSConsignment//ns3:IncludedSPSTradeLineItem',
                '//SPSConsignment//IncludedSPSTradeLineItem'
            ];

            foreach ($patterns as $pattern) {
                $nodes = $xpath->query($pattern);
                \Log::info('XML Debug - Trade line pattern', [
                    'reference' => $reference,
                    'pattern' => $pattern,
                    'count' => $nodes->length
                ]);

                if ($nodes->length > 0) {
                    // Log the first trade line item structure
                    $firstNode = $nodes->item(0);
                    \Log::info('XML Debug - First trade line structure', [
                        'reference' => $reference,
                        'xml_snippet' => $dom->saveXML($firstNode)
                    ]);
                    break;
                }
            }

            // Also check for consignment structure
            $consignmentPatterns = [
                '//ns5:SPSConsignment',
                '//SPSConsignment',
                '//*[local-name()="SPSConsignment"]'
            ];

            foreach ($consignmentPatterns as $pattern) {
                $nodes = $xpath->query($pattern);
                \Log::info('XML Debug - Consignment pattern', [
                    'reference' => $reference,
                    'pattern' => $pattern,
                    'count' => $nodes->length
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Error in XML debugging', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
        }
    }
}
