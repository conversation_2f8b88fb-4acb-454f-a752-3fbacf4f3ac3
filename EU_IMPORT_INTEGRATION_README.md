# EU Import Service Integration for Laravel 5.4

This package provides a standalone service to fetch EU Import certificates from the TRACES API and integrate them into your Laravel 5.4 project, replacing the need for external API calls.

## Files Included

1. **EuImportService.php** - Main service class containing all EU Import logic
2. **ExampleEuImportController.php** - Example controller showing how to use the service
3. **EU_IMPORT_INTEGRATION_README.md** - This documentation file

## Installation Steps

### 1. Copy the Service File

Copy `EuImportService.php` to your Laravel project:

```bash
# Create the Services directory if it doesn't exist
mkdir -p app/Services

# Copy the service file
cp EuImportService.php app/Services/
```

### 2. Update Your Controller

Replace your current `fetchEuImport` method with the new implementation. You can either:

**Option A: Update your existing controller**
```php
public function fetchEuImport(Request $request)
{
    $searchTerm = $request->input('search');
    $forceRefresh = $request->input('force_refresh', true);
    
    // Include the service
    require_once app_path('Services/EuImportService.php');
    $euImportService = new \EuImportService();
    
    // Use the service instead of HTTP calls
    $result = $euImportService->fetchEuImport($searchTerm, $forceRefresh);
    
    if ($result['success']) {
        // Format products for your frontend
        $formattedProducts = [];
        if (isset($result['products'])) {
            foreach ($result['products'] as $product) {
                $formattedProducts[] = [
                    'id' => $product['sequence_number'],
                    'product_name' => $product['common_name'] ?: $product['scientific_name'],
                    'scientific_name' => $product['scientific_name'],
                    'origin_country' => $product['origin_country_name'],
                    'nature' => $product['nature_description'],
                    'quantity' => $product['net_weight'],
                    'unit' => $product['net_weight_unit'],
                    // Add other fields as needed
                ];
            }
        }
        
        return response()->json([
            'success' => true,
            'products' => $formattedProducts,
            'message' => $result['message']
        ]);
    }
    
    return response()->json($result, $result['success'] ? 200 : 500);
}
```

**Option B: Use the example controller**
Copy `ExampleEuImportController.php` to `app/Http/Controllers/` and update your routes.

### 3. Configure TRACES API Credentials

The service includes default credentials, but you should update them in the service file:

```php
// In EuImportService.php, update these properties:
private $tracesUsername = 'your_username';
private $tracesAuthKey = 'your_auth_key';
private $tracesClientId = 'your_client_id';
private $useProduction = true; // Set to false for testing
```

### 4. Set Up Storage Directories

The service saves XML responses for analysis. Ensure the storage directory exists:

```bash
mkdir -p storage/app/traces_responses
chmod 755 storage/app/traces_responses
```

### 5. Update Routes (if using new controller)

Add routes to your `routes/web.php` or `routes/api.php`:

```php
// For API routes
Route::post('/eu-import/fetch', 'ExampleEuImportController@fetchEuImport');
Route::post('/eu-import/pdf', 'ExampleEuImportController@getPdf');
Route::get('/eu-import/test', 'ExampleEuImportController@test');
```

## Usage Examples

### Basic Usage

```php
// In your controller
require_once app_path('Services/EuImportService.php');
$service = new \EuImportService();

$result = $service->fetchEuImport('IMPORT.EU.MR.2025.0003940');

if ($result['success']) {
    $products = $result['products'];
    $certificateData = $result['data'];
    // Process the data
}
```

### With Error Handling

```php
try {
    require_once app_path('Services/EuImportService.php');
    $service = new \EuImportService();
    
    $result = $service->fetchEuImport($reference, $forceRefresh = true);
    
    if ($result['success']) {
        // Success - process products
        foreach ($result['products'] as $product) {
            echo "Product: " . $product['scientific_name'] . "\n";
            echo "Weight: " . $product['net_weight'] . " " . $product['net_weight_unit'] . "\n";
            echo "Origin: " . $product['origin_country_name'] . "\n";
        }
    } else {
        // Handle error
        Log::error('EU Import fetch failed', $result);
    }
} catch (Exception $e) {
    Log::error('EU Import service error', ['error' => $e->getMessage()]);
}
```

## Database Integration (Optional)

If you want to store certificates in your database, you can:

### 1. Create Migration

```php
// Create migration: php artisan make:migration create_eu_import_certificates_table

Schema::create('eu_import_certificates', function (Blueprint $table) {
    $table->increments('id');
    $table->string('import_id')->unique();
    $table->datetime('issue_date_time')->nullable();
    $table->string('type_code')->nullable();
    $table->string('status_code')->nullable();
    $table->text('raw_data')->nullable();
    $table->string('xml_file_path')->nullable();
    $table->timestamps();
});
```

### 2. Update Service Methods

Uncomment and modify the database methods in `EuImportService.php`:

```php
private function findExistingCertificate($reference)
{
    try {
        $certificate = DB::table('eu_import_certificates')
            ->where('import_id', $reference)
            ->first();
        
        if ($certificate) {
            return json_decode($certificate->raw_data, true);
        }
    } catch (Exception $e) {
        \Log::warning('Error checking existing certificate', [
            'reference' => $reference,
            'error' => $e->getMessage()
        ]);
    }
    
    return null;
}

private function storeCertificateData($certificateData, $reference)
{
    try {
        DB::table('eu_import_certificates')->updateOrInsert(
            ['import_id' => $reference],
            [
                'issue_date_time' => $certificateData['issue_date_time'],
                'type_code' => $certificateData['type_code'],
                'status_code' => $certificateData['status_code'],
                'raw_data' => json_encode($certificateData),
                'created_at' => now(),
                'updated_at' => now()
            ]
        );
        
        \Log::info('Certificate stored in database', ['reference' => $reference]);
    } catch (Exception $e) {
        \Log::error('Error storing certificate', [
            'reference' => $reference,
            'error' => $e->getMessage()
        ]);
    }
}
```

## Features

- ✅ Fetch EU Import certificates from TRACES API
- ✅ Parse and extract product information
- ✅ Handle authentication and retry logic
- ✅ Save XML responses for analysis
- ✅ Compatible with Laravel 5.4
- ✅ Detailed logging and error handling
- ✅ Fallback authentication methods
- ⚠️ Database storage (requires implementation)
- ⚠️ PDF retrieval (requires additional implementation)

## Troubleshooting

### Common Issues

1. **Authentication Errors**: The service includes fallback authentication methods with different timestamps
2. **XML Parsing Errors**: Check the saved XML files in `storage/app/traces_responses/`
3. **Memory Issues**: Large XML responses are handled efficiently
4. **Network Timeouts**: Configurable timeout settings

### Logging

The service logs extensively. Check your Laravel logs:

```bash
tail -f storage/logs/laravel.log
```

### Testing

Use the test endpoint to verify everything is working:

```bash
curl -X GET "http://your-domain.com/eu-import/test"
```

## Support

This service is adapted from a newer Laravel project and includes all the necessary functionality to replace your external API calls with direct TRACES API integration.

For issues or questions, check the Laravel logs and the saved XML responses for debugging information.
