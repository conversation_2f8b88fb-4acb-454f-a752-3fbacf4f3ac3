<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * Example Controller showing how to use the EuImportService in Laravel 5.4
 * 
 * This replaces your current API call with direct service usage
 */
class ExampleEuImportController extends Controller
{
    /**
     * Fetch EU Import certificate data using the service
     * 
     * This method replaces your current fetchEuImport method that calls the external API
     */
    public function fetchEuImport(Request $request): JsonResponse
    {
        $searchTerm = $request->input('search');
        $forceRefresh = $request->input('force_refresh', true);
        
        // Validate input
        if (empty($searchTerm)) {
            return response()->json([
                'error' => 'Search term is required',
                'message' => 'Please provide a valid EU Import reference number'
            ], 400);
        }

        // Create the service instance
        // Make sure to include the EuImportService.php file
        require_once app_path('Services/EuImportService.php');
        $euImportService = new \EuImportService();

        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount < $maxRetries) {
            try {
                // Use the service instead of making HTTP calls
                $result = $euImportService->fetchEuImport($searchTerm, $forceRefresh);
                
                if ($result['success']) {
                    // Format the response similar to your current API response
                    $formattedProducts = [];
                    
                    if (isset($result['products']) && is_array($result['products'])) {
                        foreach ($result['products'] as $product) {
                            // Skip products without sequence number (summary items)
                            if (empty($product['sequence_number']) || $product['sequence_number'] == '0') {
                                continue;
                            }

                            $formattedProducts[] = [
                                'id' => $product['sequence_number'] ?? null,
                                'product_name' => $product['common_name'] ?: $product['scientific_name'],
                                'scientific_name' => $product['scientific_name'] ?? null,
                                'common_name' => $product['common_name'] ?? null,
                                'origin_country' => $product['origin_country_name'] ?? null,
                                'storage_location' => $product['processing_plant_name'] ?? null,
                                'nature' => $product['nature_description'] ?? null,
                                'nature_description' => $product['nature_description'] ?? null,
                                'product_type' => $product['treatment_description'] ?? null,
                                'treatment_description' => $product['treatment_description'] ?? null,
                                'packaging_type' => $product['package_type_name'] ?? null,
                                'unit' => $product['net_weight_unit'] ?? null,
                                'net_weight_unit' => $product['net_weight_unit'] ?? null,
                                'quantity' => $product['net_weight'] ?? null,
                                'net_weight' => $product['net_weight'] ?? null,
                                'package_count' => $product['package_quantity'] ?? null,
                                'batch_id' => $product['production_batch_id'] ?? null,
                                'fao_code' => $product['fao_code'] ?? null,
                                'cn_code' => $product['cn_code'] ?? null,
                                'is_wild' => $product['is_wild_stock'] ?? false,
                                'collection_date' => $product['collection_date'] ?? null,
                                'processing_plant' => [
                                    'id' => $product['processing_plant_id'] ?? null,
                                    'name' => $product['processing_plant_name'] ?? null
                                ],
                                'processing_plant_name' => $product['processing_plant_name'] ?? null,
                                'processes' => $product['processes'] ?? [],

                                // Add all the raw service data for debugging and flexibility
                                'raw_data' => $product
                            ];
                        }
                    }

                    return response()->json([
                        'success' => true,
                        'message' => $result['message'],
                        'source' => $result['source'],
                        'products' => $formattedProducts,
                        'reference' => $searchTerm,
                        'certificate_data' => $result['data'] ?? null
                    ]);
                } else {
                    // API returned an error, log it and retry
                    \Log::warning("EU Import service returned error on attempt " . ($retryCount + 1) . ": " . $result['message']);
                }
                
            } catch (\Exception $e) {
                \Log::error("Exception on attempt " . ($retryCount + 1) . ": " . $e->getMessage());
            }
            
            $retryCount++;
            
            if ($retryCount < $maxRetries) {
                \Log::info("Retrying EU Import fetch in 2 seconds... (Attempt " . ($retryCount + 1) . "/$maxRetries)");
                sleep(2);
            }
        }
        
        // All retries failed
        \Log::error("All $maxRetries attempts failed for EU import reference: $searchTerm");
        
        return response()->json([
            'error' => 'Service temporarily unavailable',
            'message' => "Unable to fetch EU import data after $maxRetries attempts. Please try again later.",
            'retry_count' => $maxRetries,
            'reference' => $searchTerm
        ], 503);
    }

    /**
     * Get EU Import certificate PDF (if you need this functionality)
     */
    public function getPdf(Request $request): JsonResponse
    {
        $reference = $request->input('reference');
        $extraLanguages = $request->input('extra_languages', []);
        
        if (empty($reference)) {
            return response()->json([
                'error' => 'Reference is required',
                'message' => 'Please provide a valid EU Import reference number'
            ], 400);
        }

        try {
            require_once app_path('Services/EuImportService.php');
            $euImportService = new \EuImportService();
            
            // You would need to add a getPdf method to the service
            // For now, return a placeholder response
            return response()->json([
                'success' => false,
                'message' => 'PDF functionality not yet implemented in service',
                'reference' => $reference
            ], 501);
            
        } catch (\Exception $e) {
            \Log::error('EU-Import PDF fetch error', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve PDF: ' . $e->getMessage(),
                'reference' => $reference
            ], 500);
        }
    }

    /**
     * Search EU Import certificates in local database (if you implement storage)
     */
    public function search(Request $request): JsonResponse
    {
        // This would search your local database if you implement the storage
        // For now, return a placeholder response
        return response()->json([
            'success' => false,
            'message' => 'Local search functionality not yet implemented',
            'note' => 'You can implement this by creating database tables and modifying the service storage methods'
        ], 501);
    }

    /**
     * Test method to verify the service is working
     */
    public function test(Request $request): JsonResponse
    {
        try {
            require_once app_path('Services/EuImportService.php');
            $euImportService = new \EuImportService();
            
            // Test with a known reference
            $testReference = 'IMPORT.EU.MR.2025.0003940';
            $result = $euImportService->fetchEuImport($testReference, true);
            
            return response()->json([
                'success' => true,
                'message' => 'Service test completed',
                'test_reference' => $testReference,
                'result' => $result
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Service test failed: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
