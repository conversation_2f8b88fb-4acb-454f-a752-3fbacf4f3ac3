<?php

/**
 * Example usage of the EU-Import Certificate API
 * 
 * This script shows how to use the TracesNtEuImportClient class
 * to retrieve EU-Import certificates from TRACES API.
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Providers\TracesNtEuImportClient;

// Example configuration (you should use your actual TRACES credentials)
$config = [
    'username' => 'your_traces_username',
    'auth_key' => 'your_traces_auth_key',
    'client_id' => 'your_client_id',
    'use_production' => false // Set to true for production environment
];

// Create the client
$client = new TracesNtEuImportClient(
    $config['username'],
    $config['auth_key'],
    $config['client_id'],
    $config['use_production']
);

echo "EU-Import Certificate API Usage Examples\n";
echo "========================================\n\n";

// Example 1: Get EU-Import certificate by reference
echo "1. Getting EU-Import certificate by reference\n";
echo "----------------------------------------------\n";

$importReference = 'IMPORT.EU.IT.2020.1000091'; // Example reference

try {
    echo "Fetching certificate: $importReference\n";
    
    $certificateData = $client->getEuImportCertificate($importReference);
    
    echo "✓ Certificate retrieved successfully!\n";
    echo "  ID: " . ($certificateData['id'] ?? 'N/A') . "\n";
    echo "  Issue Date: " . ($certificateData['issue_date_time'] ?? 'N/A') . "\n";
    echo "  Type Code: " . ($certificateData['type_code'] ?? 'N/A') . "\n";
    echo "  Purpose Code: " . ($certificateData['purpose_code'] ?? 'N/A') . "\n";
    
    // Show consignments
    if (isset($certificateData['consignments'])) {
        echo "  Consignments: " . count($certificateData['consignments']) . "\n";
        
        foreach ($certificateData['consignments'] as $index => $consignment) {
            echo "    Consignment " . ($index + 1) . ":\n";
            echo "      ID: " . ($consignment['id'] ?? 'N/A') . "\n";
            
            if (isset($consignment['consignment_items'])) {
                echo "      Items: " . count($consignment['consignment_items']) . "\n";
                
                foreach ($consignment['consignment_items'] as $itemIndex => $item) {
                    echo "        Item " . ($itemIndex + 1) . ":\n";
                    echo "          Sequence: " . ($item['sequence_numeric'] ?? 'N/A') . "\n";
                    
                    if (isset($item['nature_identification'])) {
                        foreach ($item['nature_identification'] as $nature) {
                            echo "          Commodity: " . ($nature['system_id'] ?? 'N/A') . " - " . ($nature['class_code'] ?? 'N/A') . "\n";
                            echo "          Description: " . ($nature['class_name'] ?? 'N/A') . "\n";
                        }
                    }
                }
            }
        }
    }
    
    // Show parties
    if (isset($certificateData['parties'])) {
        echo "  Parties: " . count($certificateData['parties']) . "\n";
        
        foreach ($certificateData['parties'] as $party) {
            echo "    " . ($party['type'] ?? 'Unknown') . ": " . ($party['name'] ?? 'N/A') . "\n";
            if (isset($party['address']['country_id'])) {
                echo "      Country: " . $party['address']['country_id'] . "\n";
            }
        }
    }
    
    // Show referenced documents
    if (isset($certificateData['referenced_documents'])) {
        echo "  Referenced Documents: " . count($certificateData['referenced_documents']) . "\n";
        
        foreach ($certificateData['referenced_documents'] as $doc) {
            echo "    Document: " . ($doc['type_name'] ?? 'Unknown') . "\n";
            echo "      ID: " . ($doc['id'] ?? 'N/A') . "\n";
            if (isset($doc['attachment_filename'])) {
                echo "      Attachment: " . $doc['attachment_filename'] . "\n";
            }
        }
    }
    
    // Save XML response for analysis
    if (isset($certificateData['raw_xml'])) {
        $xmlPath = $client->saveXmlResponse($certificateData['raw_xml'], 'example_' . $importReference . '.xml');
        echo "  XML saved to: $xmlPath\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 2: Get PDF certificate
echo "2. Getting EU-Import certificate PDF\n";
echo "-------------------------------------\n";

try {
    echo "Fetching PDF for: $importReference\n";
    
    $pdfContent = $client->getEuImportPdfCertificate($importReference, ['fr']); // With French translation
    
    echo "✓ PDF retrieved successfully!\n";
    echo "  PDF size: " . strlen($pdfContent) . " characters (base64)\n";
    
    // Save PDF to file
    $pdfData = base64_decode($pdfContent);
    if ($pdfData) {
        $pdfFilename = 'example_' . $importReference . '.pdf';
        file_put_contents($pdfFilename, $pdfData);
        echo "  PDF saved to: $pdfFilename\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 3: Search for EU-Import certificates
echo "3. Searching EU-Import certificates\n";
echo "------------------------------------\n";

try {
    echo "Searching for validated certificates from Morocco...\n";
    
    $searchCriteria = [
        'pageSize' => 10,
        'offset' => 0,
        'status' => [70], // Validated status
        'countryOfConsignor' => 'MR', // Morocco
        'updateDateTimeRange' => [
            'from' => '2020-01-01T00:00:00Z',
            'to' => '2020-12-31T23:59:59Z'
        ]
    ];
    
    $searchResults = $client->findEuImportCertificate($searchCriteria);
    
    echo "✓ Search completed successfully!\n";
    echo "  Page size: " . ($searchResults['page_size'] ?? 0) . "\n";
    echo "  Offset: " . ($searchResults['offset'] ?? 0) . "\n";
    echo "  Certificates found: " . count($searchResults['certificates'] ?? []) . "\n";
    
    if (isset($searchResults['certificates'])) {
        foreach ($searchResults['certificates'] as $index => $cert) {
            echo "    Certificate " . ($index + 1) . ":\n";
            echo "      ID: " . ($cert['id'] ?? 'N/A') . "\n";
            echo "      Status: " . ($cert['status'] ?? 'N/A') . "\n";
            echo "      Consignor Country: " . ($cert['country_of_consignor'] ?? 'N/A') . "\n";
            echo "      Consignee Country: " . ($cert['country_of_consignee'] ?? 'N/A') . "\n";
            echo "      Update Date: " . ($cert['update_date_time'] ?? 'N/A') . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Example 4: Get signed PDF (if available)
echo "4. Getting signed PDF certificate\n";
echo "----------------------------------\n";

try {
    echo "Fetching signed PDF for: $importReference\n";
    
    $signedPdfContent = $client->getEuImportSignedPdfCertificate($importReference);
    
    echo "✓ Signed PDF retrieved successfully!\n";
    echo "  PDF size: " . strlen($signedPdfContent) . " characters (base64)\n";
    
    // Save signed PDF to file
    $signedPdfData = base64_decode($signedPdfContent);
    if ($signedPdfData) {
        $signedPdfFilename = 'example_' . $importReference . '_signed.pdf';
        file_put_contents($signedPdfFilename, $signedPdfData);
        echo "  Signed PDF saved to: $signedPdfFilename\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "  Note: Signed PDFs may not be available for all certificates\n";
}

echo "\n";

echo "Examples completed!\n";
echo "===================\n";
echo "Check the generated files:\n";
echo "- XML responses in storage/app/traces_responses/\n";
echo "- PDF files in the current directory\n";
echo "\nFor more information, see EU_IMPORT_API_DOCUMENTATION.md\n";
