<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Providers\TracesNtChedClient;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Example Laravel Controller for CHED Certificate Management
 * 
 * This controller demonstrates how to use the TracesNtChedClient
 * to retrieve certificates by reference number in a Laravel application.
 */
class CertificateController extends Controller
{
    private $tracesClient;

    public function __construct()
    {
        // Initialize the TRACES NT client with configuration from <PERSON><PERSON> config
        $this->tracesClient = new TracesNtChedClient(
            config('services.traces.username'),
            config('services.traces.auth_key'),
            config('services.traces.client_id'),
            config('services.traces.use_production', false)
        );
    }

    /**
     * Get a CHED certificate by its reference number
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCertificateByReference(Request $request): JsonResponse
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'reference' => 'required|string|max:50|regex:/^CHED[A-Z]{1,2}\.[A-Z]{2}\.\d{4}\.\d+$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid CHED reference format',
                'errors' => $validator->errors()
            ], 400);
        }

        $chedReference = $request->input('reference');

        try {
            Log::info('Retrieving CHED certificate', ['reference' => $chedReference]);

            // Get the certificate from TRACES NT
            $certificate = $this->tracesClient->getChedCertificateByReference($chedReference);

            // Transform the data for API response
            $responseData = $this->transformCertificateData($certificate);

            Log::info('CHED certificate retrieved successfully', [
                'reference' => $chedReference,
                'certificate_id' => $certificate['id'] ?? null
            ]);

            return response()->json([
                'success' => true,
                'data' => $responseData
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve CHED certificate', [
                'reference' => $chedReference,
                'error' => $e->getMessage()
            ]);

            // Handle specific TRACES NT errors
            if (strpos($e->getMessage(), 'ChedCertificateNotFoundException') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certificate not found',
                    'error_code' => 'CERTIFICATE_NOT_FOUND'
                ], 404);
            }

            if (strpos($e->getMessage(), 'ChedCertificatePermissionDeniedException') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this certificate',
                    'error_code' => 'PERMISSION_DENIED'
                ], 403);
            }

            if (strpos($e->getMessage(), 'Authentication failed') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication error with TRACES NT',
                    'error_code' => 'AUTHENTICATION_ERROR'
                ], 401);
            }

            // Generic error
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve certificate',
                'error_code' => 'RETRIEVAL_ERROR'
            ], 500);
        }
    }

    /**
     * Get multiple certificates by their reference numbers
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCertificatesByReferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'references' => 'required|array|max:10',
            'references.*' => 'required|string|max:50|regex:/^CHED[A-Z]{1,2}\.[A-Z]{2}\.\d{4}\.\d+$/'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request format',
                'errors' => $validator->errors()
            ], 400);
        }

        $references = $request->input('references');
        $results = [];
        $errors = [];

        foreach ($references as $reference) {
            try {
                $certificate = $this->tracesClient->getChedCertificateByReference($reference);
                $results[] = [
                    'reference' => $reference,
                    'success' => true,
                    'data' => $this->transformCertificateData($certificate)
                ];
            } catch (\Exception $e) {
                $errors[] = [
                    'reference' => $reference,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'results' => $results,
            'errors' => $errors,
            'summary' => [
                'total' => count($references),
                'successful' => count($results),
                'failed' => count($errors)
            ]
        ]);
    }

    /**
     * Transform certificate data for API response
     * 
     * @param array $certificate
     * @return array
     */
    private function transformCertificateData(array $certificate): array
    {
        return [
            'certificate_id' => $certificate['id'] ?? null,
            'issue_date' => $certificate['issue_date_time'] ?? null,
            'type_code' => $certificate['type_code'] ?? null,
            'purpose_code' => $certificate['purpose_code'] ?? null,
            
            // Extract and format parties
            'parties' => collect($certificate['parties'] ?? [])->map(function ($party) {
                return [
                    'type' => $party['type'] ?? null,
                    'name' => $party['name'] ?? null,
                    'id' => $party['id'] ?? null,
                    'scheme_id' => $party['scheme_id'] ?? null,
                    'country' => $party['address']['country_id'] ?? null,
                    'city' => $party['address']['city_name'] ?? null,
                    'address_line' => $party['address']['line_one'] ?? null,
                    'postcode' => $party['address']['postcode'] ?? null
                ];
            })->toArray(),

            // Extract and format consignments
            'consignments' => collect($certificate['consignments'] ?? [])->map(function ($consignment) {
                return [
                    'id' => $consignment['id'] ?? null,
                    'items' => collect($consignment['consignment_items'] ?? [])->map(function ($item) {
                        return [
                            'sequence' => $item['sequence_numeric'] ?? null,
                            'classifications' => collect($item['classifications'] ?? [])->map(function ($classification) {
                                return [
                                    'system' => $classification['system_id'] ?? null,
                                    'code' => $classification['class_code'] ?? null,
                                    'description' => $classification['class_names']['en'] ?? null
                                ];
                            })->toArray()
                        ];
                    })->toArray()
                ];
            })->toArray(),

            // Extract and format locations
            'locations' => collect($certificate['locations'] ?? [])->map(function ($location) {
                return [
                    'type' => $location['type'] ?? null,
                    'name' => $location['name'] ?? null,
                    'id' => $location['id'] ?? null,
                    'scheme_id' => $location['scheme_id'] ?? null
                ];
            })->toArray(),

            // Extract and format referenced documents
            'referenced_documents' => collect($certificate['referenced_documents'] ?? [])->map(function ($doc) {
                return [
                    'type' => $doc['type_name'] ?? $doc['type_code'] ?? null,
                    'id' => $doc['id'] ?? null,
                    'relationship' => $doc['relationship_name'] ?? $doc['relationship_type'] ?? null,
                    'issue_date' => $doc['issue_date_time'] ?? null,
                    'attachment_filename' => $doc['attachment_filename'] ?? null,
                    'information' => $doc['information'] ?? null
                ];
            })->toArray()
        ];
    }

    /**
     * Validate CHED reference format
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function validateReference(Request $request): JsonResponse
    {
        $reference = $request->input('reference');
        
        $isValid = preg_match('/^CHED[A-Z]{1,2}\.[A-Z]{2}\.\d{4}\.\d+$/', $reference);
        
        return response()->json([
            'reference' => $reference,
            'is_valid' => (bool) $isValid,
            'format' => 'CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]',
            'examples' => [
                'CHEDPP.IT.2020.1000091',
                'CHEDP.MR.2025.0000001',
                'CHEDA.FR.2024.0500612'
            ]
        ]);
    }
}
