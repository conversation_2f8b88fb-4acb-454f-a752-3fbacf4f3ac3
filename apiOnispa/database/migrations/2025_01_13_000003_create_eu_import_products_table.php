<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eu_import_products', function (Blueprint $table) {
            $table->id();
            $table->string('import_id')->index(); // Reference to the certificate
            $table->integer('sequence_number')->nullable(); // Product sequence in the consignment
            $table->string('scientific_name')->nullable(); // Species scientific name
            $table->string('common_name')->nullable(); // Common name if available
            $table->string('production_batch_id')->nullable(); // Batch/lot number
            
            // Weight information
            $table->decimal('net_weight', 10, 3)->nullable();
            $table->string('net_weight_unit', 10)->nullable();
            $table->decimal('gross_weight', 10, 3)->nullable();
            $table->string('gross_weight_unit', 10)->nullable();
            
            // Classification codes
            $table->string('cn_code')->nullable(); // Combined Nomenclature code
            $table->text('cn_description')->nullable(); // CN code description
            $table->string('fao_code')->nullable(); // FAO ASFIS code
            $table->string('fao_description')->nullable(); // FAO description
            
            // Nature and treatment
            $table->string('nature_of_commodity')->nullable(); // WILD_STOCK, AQUACULTURE, etc.
            $table->string('nature_description')->nullable(); // Human readable description
            $table->string('treatment_type')->nullable(); // CHILLED, FROZEN, FRESH, etc.
            $table->string('treatment_description')->nullable(); // Human readable description
            $table->boolean('is_wild_stock')->default(false); // True if wild caught
            $table->boolean('for_final_consumer')->default(false); // True if for direct consumption
            
            // Packaging information
            $table->string('package_type_code')->nullable(); // QR, etc.
            $table->string('package_type_name')->nullable(); // Polystyrene Box, etc.
            $table->string('package_level_code')->nullable(); // Packaging hierarchy level
            $table->string('package_level_name')->nullable(); // No packaging hierarchy, etc.
            $table->decimal('package_quantity', 10, 2)->nullable(); // Number of packages
            
            // Processing information
            $table->json('processes')->nullable(); // Array of processing steps
            $table->datetime('collection_date')->nullable(); // Collection/production date
            $table->string('processing_plant_id')->nullable(); // Processing plant ID
            $table->string('processing_plant_name')->nullable(); // Processing plant name
            
            // Origin information
            $table->string('origin_country_id', 2)->nullable(); // Country of origin
            $table->string('origin_country_name')->nullable(); // Country name
            
            // Additional classifications
            $table->json('all_classifications')->nullable(); // All classification systems
            $table->json('raw_data')->nullable(); // Complete raw product data for analysis
            
            $table->timestamps();
            
            // Indexes
            $table->index(['import_id', 'sequence_number']);
            $table->index('scientific_name');
            $table->index('cn_code');
            $table->index('fao_code');
            $table->index('nature_of_commodity');
            $table->index('treatment_type');
            $table->index('origin_country_id');
            $table->index('is_wild_stock');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eu_import_products');
    }
};
