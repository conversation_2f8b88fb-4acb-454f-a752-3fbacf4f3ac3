<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
          Schema::create('certificates', function (Blueprint $table) {
        $table->id();
        $table->string('certificate_id')->unique();
        $table->string('type')->nullable();
        $table->string('local_reference')->nullable();
        $table->string('status')->nullable();
        $table->string('status_name')->nullable();
        $table->string('bcp_code')->nullable();
        $table->string('bcp_unlocode')->nullable();
        $table->string('country_of_issuance')->nullable();
        $table->string('country_of_entry')->nullable();
        $table->string('country_of_dispatch')->nullable();
        $table->string('country_of_origin')->nullable();
        $table->string('country_of_place_of_destination')->nullable();
        $table->string('country_of_consignor')->nullable();
        $table->string('consignor_name')->nullable();
        $table->string('country_of_consignee')->nullable();
        $table->string('consignee_name')->nullable();
        $table->timestamp('create_date_time')->nullable();
        $table->timestamp('update_date_time')->nullable();
        $table->timestamp('status_change_date_time')->nullable();
        $table->timestamp('declaration_date_time')->nullable();
        $table->timestamp('decision_date_time')->nullable();
        $table->timestamp('prior_notification_date_time')->nullable();
        $table->json('commodities')->nullable();
        $table->json('references')->nullable();
        $table->timestamps();
    });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
