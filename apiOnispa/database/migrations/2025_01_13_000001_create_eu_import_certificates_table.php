<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eu_import_certificates', function (Blueprint $table) {
            $table->id();
            $table->string('import_id', 50)->unique(); // e.g., IMPORT.EU.IT.2020.1000091
            $table->timestamp('issue_date_time')->nullable();
            $table->string('type_code', 10)->nullable(); // Certificate type code
            $table->string('purpose_code', 10)->nullable(); // Purpose code
            $table->integer('status')->nullable(); // Status code (47=Draft, 1=New, 42=In progress, 70=Validated, etc.)
            $table->string('status_name', 100)->nullable(); // Human readable status
            $table->string('local_id', 100)->nullable(); // Field I.21a
            
            // Authority information
            $table->string('central_competent_authority_code', 50)->nullable(); // Box I.3
            $table->string('central_competent_authority_unlocode', 10)->nullable();
            $table->string('local_competent_authority_code', 50)->nullable(); // Box I.4
            $table->string('local_competent_authority_unlocode', 10)->nullable();
            
            // Country information
            $table->string('country_of_issuance', 2)->nullable(); // Country where certificate was validated
            $table->string('country_of_consignor', 2)->nullable(); // Field I.1
            $table->string('consignor_name', 255)->nullable();
            $table->string('country_of_consignee', 2)->nullable(); // Field I.5
            $table->string('consignee_name', 255)->nullable();
            $table->json('country_of_origin')->nullable(); // Field I.25 (can be multiple)
            $table->string('country_of_destination', 2)->nullable(); // Field I.9
            $table->string('country_of_dispatch', 2)->nullable(); // Field I.11
            $table->string('country_of_place_of_loading', 2)->nullable(); // Field I.13
            $table->json('country_of_transit')->nullable(); // Fields I.21 (can be multiple)
            
            // Date/time information
            $table->timestamp('create_date_time')->nullable(); // Physical creation in DB
            $table->timestamp('update_date_time')->nullable(); // Last update
            $table->timestamp('status_change_date_time')->nullable(); // Last status change
            $table->timestamp('declaration_date_time')->nullable(); // Part I declaration
            $table->timestamp('certification_date_time')->nullable(); // Part II signature
            
            // Additional flags
            $table->boolean('has_controls')->default(false); // Has laboratory tests/controls
            
            // Raw data storage
            $table->json('raw_data')->nullable(); // Complete parsed certificate data
            $table->text('xml_file_path')->nullable(); // Path to saved XML file
            
            $table->timestamps();
            
            // Indexes for efficient searching
            $table->index(['country_of_dispatch', 'update_date_time']);
            $table->index(['status', 'update_date_time']);
            $table->index(['country_of_issuance', 'certification_date_time']);
            $table->index(['country_of_consignor', 'country_of_consignee']);
            $table->index('update_date_time');
            $table->index('certification_date_time');
            $table->index('local_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eu_import_certificates');
    }
};
