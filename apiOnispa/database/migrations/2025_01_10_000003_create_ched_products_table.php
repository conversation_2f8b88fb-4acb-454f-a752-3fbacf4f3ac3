<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ched_products', function (Blueprint $table) {
            $table->id();
            $table->string('ched_id', 50); // Foreign key to ched_certificates.ched_id
            $table->string('system_id', 20); // e.g., CN
            $table->string('system_name', 255)->nullable(); // e.g., CN Code (Combined Nomenclature)
            $table->string('class_code', 20); // e.g., 03028990
            $table->json('class_names')->nullable(); // Multilingual names
            $table->string('class_name_en', 500)->nullable(); // English name for easy searching
            $table->timestamps();
            
            // Indexes for product searching
            $table->index(['ched_id', 'system_id']);
            $table->index(['class_code', 'system_id']);
            $table->index('class_name_en');
            
            // Foreign key constraint
            $table->foreign('ched_id')->references('ched_id')->on('ched_certificates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ched_products');
    }
};
