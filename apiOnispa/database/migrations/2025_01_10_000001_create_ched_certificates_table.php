<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ched_certificates', function (Blueprint $table) {
            $table->id();
            $table->string('ched_id', 50)->unique(); // e.g., CHEDP.FR.2025.0093319
            $table->string('type', 10); // P, A, PP, D, N
            $table->string('local_reference', 100)->nullable();
            $table->integer('status');
            $table->string('status_name', 100);
            $table->string('bcp_code', 50)->nullable();
            $table->string('bcp_unlocode', 20)->nullable();
            $table->string('country_of_issuance', 2)->nullable();
            $table->string('country_of_entry', 2)->nullable();
            $table->string('country_of_dispatch', 2)->nullable();
            $table->string('country_of_origin', 2)->nullable();
            $table->string('country_of_place_of_destination', 2)->nullable();
            $table->string('country_of_consignor', 2)->nullable();
            $table->string('consignor_name', 255)->nullable();
            $table->string('country_of_consignee', 2)->nullable();
            $table->string('consignee_name', 255)->nullable();
            $table->timestamp('create_date_time')->nullable();
            $table->timestamp('update_date_time')->nullable();
            $table->timestamp('status_change_date_time')->nullable();
            $table->timestamp('declaration_date_time')->nullable();
            $table->timestamp('decision_date_time')->nullable();
            $table->timestamp('prior_notification_date_time')->nullable();
            $table->json('raw_data')->nullable(); // Store the complete raw certificate data
            $table->timestamps();
            
            // Indexes for efficient searching
            $table->index(['country_of_dispatch', 'update_date_time']);
            $table->index(['status', 'update_date_time']);
            $table->index(['type', 'country_of_dispatch']);
            $table->index('update_date_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ched_certificates');
    }
};
