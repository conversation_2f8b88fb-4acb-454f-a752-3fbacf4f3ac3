<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ched_sanitary_references', function (Blueprint $table) {
            $table->id();
            $table->string('ched_id', 50); // Foreign key to ched_certificates.ched_id
            $table->string('sanitary_reference', 100); // e.g., IMPORT.EU.MR.2025.0003344
            $table->string('type_code', 10)->nullable(); // e.g., 852
            $table->string('type_name', 255)->nullable(); // e.g., Sanitary certificate (EU Import certificate)
            $table->string('relationship_type', 10)->nullable(); // e.g., ACE
            $table->string('relationship_name', 255)->nullable(); // e.g., Related document number
            $table->string('scheme_agency', 10)->nullable(); // e.g., MR
            $table->text('attachment_uri')->nullable(); // URL to the certificate
            $table->timestamp('issue_date_time')->nullable();
            $table->timestamps();
            
            // Indexes for fast searching by sanitary reference
            $table->unique(['ched_id', 'sanitary_reference']); // Prevent duplicates
            $table->index('sanitary_reference'); // Primary search index
            $table->index(['sanitary_reference', 'scheme_agency']);
            
            // Foreign key constraint
            $table->foreign('ched_id')->references('ched_id')->on('ched_certificates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ched_sanitary_references');
    }
};
