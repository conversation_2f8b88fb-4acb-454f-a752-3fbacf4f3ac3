<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ched_sync_log', function (Blueprint $table) {
            $table->id();
            $table->timestamp('sync_start_time');
            $table->timestamp('sync_end_time')->nullable();
            $table->string('date_range_start', 20); // YYYY-MM-DD
            $table->string('date_range_end', 20); // YYYY-MM-DD
            $table->integer('total_certificates_fetched')->default(0);
            $table->integer('new_certificates_added')->default(0);
            $table->integer('certificates_updated')->default(0);
            $table->integer('sanitary_references_added')->default(0);
            $table->integer('products_added')->default(0);
            $table->string('status', 20); // running, completed, failed
            $table->text('error_message')->nullable();
            $table->json('api_calls_made')->nullable(); // Track pagination calls
            $table->timestamps();
            
            // Index for monitoring
            $table->index(['status', 'sync_start_time']);
            $table->index('sync_start_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ched_sync_log');
    }
};
