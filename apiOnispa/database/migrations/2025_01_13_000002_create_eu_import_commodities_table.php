<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eu_import_commodities', function (Blueprint $table) {
            $table->id();
            $table->string('import_id', 50); // Foreign key to eu_import_certificates.import_id
            $table->string('system_id', 20); // e.g., CN
            $table->string('system_name', 255)->nullable(); // e.g., CN Code (Combined Nomenclature)
            $table->string('class_code', 20); // e.g., 03028990
            $table->string('class_name', 500)->nullable(); // Commodity description
            $table->integer('sequence_numeric')->nullable(); // Sequence number in consignment
            $table->timestamps();
            
            // Indexes for commodity searching
            $table->index(['import_id', 'system_id']);
            $table->index(['class_code', 'system_id']);
            $table->index('class_name');
            
            // Foreign key constraint
            $table->foreign('import_id')->references('import_id')->on('eu_import_certificates')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eu_import_commodities');
    }
};
