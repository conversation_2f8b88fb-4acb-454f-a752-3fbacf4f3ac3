<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Inspection Form Mapping Test ===\n\n";

// Test references
$testReferences = [
    'IMPORT.EU.MR.2025.0000007',
    'CHEDP.FR.2025.0000038',
];

foreach ($testReferences as $reference) {
    echo "Testing reference: {$reference}\n";
    echo str_repeat("=", 50) . "\n";
    
    // Test the new inspection form endpoint
    $url = "http://localhost:8011/api/certificates/inspection-form?reference=" . urlencode($reference);
    echo "URL: {$url}\n\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ CURL Error: {$error}\n\n";
        continue;
    }
    
    echo "HTTP Status: {$httpCode}\n";
    
    $data = json_decode($response, true);
    if (!$data) {
        echo "❌ Invalid JSON response\n\n";
        continue;
    }
    
    if (!$data['success']) {
        echo "❌ API Error: " . ($data['message'] ?? 'Unknown error') . "\n\n";
        continue;
    }
    
    echo "✅ Success! Certificate data mapped for inspection form\n\n";
    
    // Display mapped data
    echo "📋 FORM MAPPING RESULTS:\n";
    echo str_repeat("-", 30) . "\n";
    
    if (isset($data['form_suggestions']['pre_filled_values'])) {
        $preFilledValues = $data['form_suggestions']['pre_filled_values'];
        
        echo "🔸 Produit: " . ($preFilledValues['produit'] ?? 'Non mappé') . "\n";
        echo "🔸 Origine de produit: " . ($preFilledValues['origine_de_produit'] ?? 'Non mappé') . "\n";
        echo "🔸 Lieu de stockage: " . ($preFilledValues['lieu_de_stockage'] ?? 'Non mappé') . "\n";
        echo "🔸 Nature de pièce: " . ($preFilledValues['nature_de_piece'] ?? 'Non mappé') . "\n";
        echo "🔸 Type de produits: " . ($preFilledValues['type_de_produits'] ?? 'Non mappé') . "\n";
        echo "🔸 Types d'emballage: " . ($preFilledValues['types_emballage'] ?? 'Non mappé') . "\n";
        echo "🔸 Unité de mesure: " . ($preFilledValues['unite_de_mesure'] ?? 'Non mappé') . "\n";
    }
    
    echo "\n📊 CERTIFICATE DETAILS:\n";
    echo str_repeat("-", 30) . "\n";
    
    if (isset($data['mapped_data']['certificate_details'])) {
        $certDetails = $data['mapped_data']['certificate_details'];
        
        echo "🔸 Expéditeur: " . ($certDetails['consignor']['name'] ?? 'N/A') . " (" . ($certDetails['consignor']['country'] ?? 'N/A') . ")\n";
        echo "🔸 Destinataire: " . ($certDetails['consignee']['name'] ?? 'N/A') . " (" . ($certDetails['consignee']['country'] ?? 'N/A') . ")\n";
        echo "🔸 Statut: " . ($certDetails['status'] ?? 'N/A') . "\n";
        echo "🔸 Date de mise à jour: " . ($certDetails['update_date'] ?? 'N/A') . "\n";
    }
    
    echo "\n🐟 PRODUCTS DETAILS:\n";
    echo str_repeat("-", 30) . "\n";
    
    if (isset($data['mapped_data']['products']) && is_array($data['mapped_data']['products'])) {
        foreach ($data['mapped_data']['products'] as $index => $product) {
            echo "Produit " . ($index + 1) . ":\n";
            echo "  • Code: " . ($product['produit']['code'] ?? 'N/A') . "\n";
            echo "  • Nom EN: " . ($product['produit']['name_en'] ?? 'N/A') . "\n";
            echo "  • Nom FR: " . ($product['produit']['name_fr'] ?? 'N/A') . "\n";
            echo "  • Système: " . ($product['produit']['system_name'] ?? 'N/A') . "\n";
            echo "\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n\n";
}

echo "=== INTEGRATION EXAMPLES ===\n\n";

echo "🌐 JavaScript/jQuery Integration:\n";
echo str_repeat("-", 40) . "\n";
echo "function loadCertificateData(reference) {\n";
echo "    $.ajax({\n";
echo "        url: 'http://localhost:8011/api/certificates/inspection-form',\n";
echo "        data: { reference: reference },\n";
echo "        success: function(response) {\n";
echo "            if (response.success) {\n";
echo "                const values = response.form_suggestions.pre_filled_values;\n";
echo "                $('#produit').val(values.produit);\n";
echo "                $('#origine_de_produit').val(values.origine_de_produit);\n";
echo "                $('#lieu_de_stockage').val(values.lieu_de_stockage);\n";
echo "                // ... autres champs\n";
echo "            }\n";
echo "        }\n";
echo "    });\n";
echo "}\n\n";

echo "🐘 PHP Integration:\n";
echo str_repeat("-", 40) . "\n";
echo "\$url = 'http://localhost:8011/api/certificates/inspection-form?reference=' . urlencode(\$reference);\n";
echo "\$data = json_decode(file_get_contents(\$url), true);\n";
echo "if (\$data['success']) {\n";
echo "    \$suggestions = \$data['form_suggestions']['pre_filled_values'];\n";
echo "    echo '<option value=\"' . \$suggestions['produit'] . '\" selected>' . \$suggestions['produit'] . '</option>';\n";
echo "}\n\n";

echo "=== FORM FIELD MAPPING ===\n\n";
echo "📋 Champs automatiquement remplis:\n";
echo "  ✅ Produit (depuis class_name_en traduit)\n";
echo "  ✅ Origine de produit (depuis country_of_dispatch)\n";
echo "  ✅ Lieu de stockage (depuis consignee_name mappé)\n";
echo "  ✅ Nature de pièce (fixe: 'Sauvage' pour poissons)\n";
echo "  ✅ Type de produits (basé sur nom produit)\n";
echo "  ✅ Types d'emballage (défaut: 'Carton Master')\n";
echo "  ✅ Unité de mesure (défaut: 'kg')\n\n";

echo "📝 Champs à remplir manuellement:\n";
echo "  ⚠️  Quantité (non disponible dans CHED)\n";
echo "  ⚠️  Nombre de colis (non disponible dans CHED)\n\n";

echo "=== CUSTOMIZATION ===\n\n";
echo "Pour personnaliser le mapping, modifiez:\n";
echo "📁 app/Services/ProductInspectionMappingService.php\n";
echo "  • mapToFrenchProductDropdown() - pour nouveaux produits\n";
echo "  • mapConsigneeToStorageLocation() - pour nouveaux lieux\n";
echo "  • mapCountryToDropdown() - pour nouveaux pays\n\n";

echo "=== Test Complete ===\n";
echo "🎉 Votre API est prête pour l'intégration avec le formulaire d'inspection!\n";
