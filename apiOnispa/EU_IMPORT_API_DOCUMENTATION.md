# EU-Import Certificate API Documentation

This document describes the new EU-Import certificate retrieval API that integrates with the TRACES NT system to fetch import documents by reference.

## Overview

The EU-Import API provides endpoints to:
- Retrieve EU-Import certificates from TRACES API and store them locally
- Get PDF versions of certificates (regular and electronically signed)
- Search certificates using various criteria
- Store XML responses for analysis and parsing

## API Endpoints

### 1. Fetch and Store Certificate

**Endpoint:** `POST /api/eu-import/fetch`

**Description:** Retrieves an EU-Import certificate from TRACES API by reference number and stores it in the local database.

**Request Body:**
```json
{
    "reference": "IMPORT.EU.IT.2020.1000091",
    "save_xml": true,
    "force_refresh": false
}
```

**Parameters:**
- `reference` (required): EU-Import TNT Reference number
- `save_xml` (optional, default: true): Whether to save the XML response to file
- `force_refresh` (optional, default: false): Whether to fetch from API even if certificate exists in database

**Response:**
```json
{
    "success": true,
    "message": "Certificate retrieved and stored successfully",
    "source": "traces_api",
    "certificate": {
        "id": 1,
        "import_id": "IMPORT.EU.IT.2020.1000091",
        "issue_date_time": "2020-10-15T10:30:00Z",
        "type_code": "851",
        "purpose_code": "1",
        "status": 70,
        "status_name": "Validated",
        "country_of_issuance": "IT",
        "country_of_consignor": "MR",
        "consignor_name": "Example Exporter Ltd",
        "country_of_consignee": "IT",
        "consignee_name": "Example Importer Srl",
        "commodities": [
            {
                "system_id": "CN",
                "system_name": "CN Code (Combined Nomenclature)",
                "class_code": "03028990",
                "class_name": "Fresh or chilled fish, other"
            }
        ]
    },
    "xml_file_path": "/path/to/storage/eu_import_certificates/eu_import_IMPORT.EU.IT.2020.1000091_2025-01-13_14-30-15.xml"
}
```

### 2. Get Certificate PDF

**Endpoint:** `POST /api/eu-import/pdf`

**Description:** Retrieves the PDF version of an EU-Import certificate.

**Request Body:**
```json
{
    "reference": "IMPORT.EU.IT.2020.1000091",
    "extra_languages": ["fr", "de"]
}
```

**Parameters:**
- `reference` (required): EU-Import TNT Reference number
- `extra_languages` (optional): Array of additional language codes for multilingual PDF

**Response:**
```json
{
    "success": true,
    "message": "PDF retrieved successfully",
    "reference": "IMPORT.EU.IT.2020.1000091",
    "pdf_content": "JVBERi0xLjQKJcOkw7zDtsO...", 
    "content_type": "application/pdf",
    "encoding": "base64"
}
```

### 3. Get Signed PDF

**Endpoint:** `POST /api/eu-import/signed-pdf`

**Description:** Retrieves the electronically signed PDF version of an EU-Import certificate.

**Request Body:**
```json
{
    "reference": "IMPORT.EU.IT.2020.1000091"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Signed PDF retrieved successfully",
    "reference": "IMPORT.EU.IT.2020.1000091",
    "pdf_content": "JVBERi0xLjQKJcOkw7zDtsO...",
    "content_type": "application/pdf",
    "encoding": "base64",
    "signed": true
}
```

### 4. Search Certificates

**Endpoint:** `POST /api/eu-import/search`

**Description:** Search for EU-Import certificates using various criteria. Can search in local database or via TRACES API.

**Request Body:**
```json
{
    "page_size": 50,
    "offset": 0,
    "status": [70, 42],
    "local_id": "IT2020001",
    "cn_code": ["03028990"],
    "cn_code_exact_match": false,
    "country_of_issuance": "IT",
    "country_of_consignor": "MR",
    "country_of_consignee": "IT",
    "country_of_dispatch": "MR",
    "create_date_from": "2020-01-01",
    "create_date_to": "2020-12-31",
    "update_date_from": "2020-01-01",
    "update_date_to": "2020-12-31",
    "certification_date_from": "2020-01-01",
    "certification_date_to": "2020-12-31",
    "use_api": false
}
```

**Parameters:**
- `page_size` (optional, default: 50): Number of results per page (1-200)
- `offset` (optional, default: 0): Number of results to skip
- `status` (optional): Array of status codes to filter by
- `local_id` (optional): Local reference ID to search for
- `cn_code` (optional): Array of CN codes to search for
- `cn_code_exact_match` (optional, default: false): Whether to match CN codes exactly
- `country_of_*` (optional): Various country filters (ISO 3166-1 alpha-2 codes)
- `*_date_from/to` (optional): Date range filters
- `use_api` (optional, default: false): Whether to search via TRACES API or local database

### 5. Get Certificate Commodities

**Endpoint:** `GET /api/eu-import/commodities`

**Description:** Get the commodity classifications for a specific EU-Import certificate.

**Query Parameters:**
- `reference` (required): EU-Import TNT Reference number

**Response:**
```json
{
    "success": true,
    "reference": "IMPORT.EU.IT.2020.1000091",
    "commodities": [
        {
            "id": 1,
            "import_id": "IMPORT.EU.IT.2020.1000091",
            "system_id": "CN",
            "system_name": "CN Code (Combined Nomenclature)",
            "class_code": "03028990",
            "class_name": "Fresh or chilled fish, other",
            "sequence_numeric": 1
        }
    ]
}
```

## Configuration

Make sure your TRACES API configuration is set up in `config/services.php`:

```php
'traces' => [
    'username' => env('TRACES_USERNAME'),
    'auth_key' => env('TRACES_AUTH_KEY'),
    'client_id' => env('TRACES_CLIENT_ID'),
    'use_production' => env('TRACES_USE_PRODUCTION', false),
    'timeout' => env('TRACES_TIMEOUT', 60),
    'verify_ssl' => env('TRACES_VERIFY_SSL', true),
],
```

## XML File Storage

When `save_xml` is enabled, XML responses are automatically saved to:
- `storage/app/eu_import_certificates/` for certificate XML files
- `storage/app/traces_responses/` for general API responses

Files are named with the pattern: `eu_import_{reference}_{timestamp}.xml`

## Error Handling

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description",
    "reference": "IMPORT.EU.IT.2020.1000091"
}
```

Common HTTP status codes:
- `200`: Success
- `404`: Certificate not found
- `422`: Validation error
- `500`: Server error

## Status Codes

EU-Import certificate status codes:
- `47`: Draft
- `1`: New
- `42`: In progress
- `70`: Validated
- `41`: Rejected
- `55`: Deleted
- `64`: Cancelled
- `44`: Replaced

## Usage Examples

### Fetch a certificate and save XML:
```bash
curl -X POST http://localhost:8000/api/eu-import/fetch \
  -H "Content-Type: application/json" \
  -d '{"reference": "IMPORT.EU.IT.2020.1000091", "save_xml": true}'
```

### Search for validated certificates from Morocco:
```bash
curl -X POST http://localhost:8000/api/eu-import/search \
  -H "Content-Type: application/json" \
  -d '{"status": [70], "country_of_consignor": "MR", "page_size": 10}'
```

### Get certificate PDF:
```bash
curl -X POST http://localhost:8000/api/eu-import/pdf \
  -H "Content-Type: application/json" \
  -d '{"reference": "IMPORT.EU.IT.2020.1000091"}'
```
