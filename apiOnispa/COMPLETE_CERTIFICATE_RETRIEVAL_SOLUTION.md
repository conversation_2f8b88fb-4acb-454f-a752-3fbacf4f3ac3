# Complete Certificate Retrieval Solution

This document describes the enhanced certificate retrieval methods that provide complete document information, including attachments and detailed certificate data.

## Problem Statement

The original `findChedCertificatesBySanitaryReference` method only returns basic certificate information from search results. It doesn't provide:
- Complete certificate details
- Attachment information and download URLs
- Full document metadata
- Referenced documents with binary attachments

## Solution Overview

I've implemented several new methods to get complete certificate information:

### 1. Direct Certificate URL Access (`testDirectCertificateUrl`)
Tests direct access to TRACES NT certificate web interface URLs like:
```
https://webgate.ec.europa.eu/tracesnt/certificate/eu-import/IMPORT.EU.MR.2025.0003940
```

**Purpose**: Explore if the web interface provides direct access to certificate data.

### 2. Complete Certificate Information (`getCompleteCertificateInfo`)
Combines search and detailed retrieval to get comprehensive certificate data:
- First finds certificates by sanitary reference
- Then retrieves complete details for each certificate found
- Includes attachment information and download URLs

### 3. Certificate Attachment Download (`getCertificateAttachment`)
Downloads binary attachments from certificates using the TRACES NT attachment service:
- Supports MTOM/XOP for binary optimization
- Handles PDF and other document formats
- Based on official TRACES NT documentation

### 4. Smart Certificate Lookup (`getCertificateByAnyReference`)
Auto-detects reference type and uses the most appropriate method:
- CHED ID format → Direct lookup
- Sanitary reference format → Search method
- Unknown format → Tries both methods

## New Methods Added

### TracesNtChedClient Methods

```php
// Test direct certificate URL access
public function testDirectCertificateUrl($sanitaryReference)

// Get complete certificate information
public function getCompleteCertificateInfo($sanitaryReference, $countryOfDispatch = null)

// Download certificate attachments
public function getCertificateAttachment($chedReference, $filename, $documentId)

// Smart certificate lookup with auto-detection
public function getCertificateByAnyReference($reference, $countryOfDispatch = null)
```

### Controller Methods

```php
// Test all certificate retrieval methods
public function testCompleteCertificateRetrieval(Request $request)
```

## Usage Examples

### 1. API Testing Endpoint

Test all methods at once:
```bash
curl "http://localhost:8011/api/certificates/test-complete?reference=IMPORT.EU.MR.2025.0003940"
```

### 2. Direct URL Testing

```php
$client = new TracesNtChedClient($username, $authKey, $clientId, true);
$result = $client->testDirectCertificateUrl('IMPORT.EU.MR.2025.0003940');

echo "URL: " . $result['url'] . "\n";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Requires Auth: " . ($result['requires_authentication'] ? 'Yes' : 'No') . "\n";
```

### 3. Complete Certificate Information

```php
$certificates = $client->getCompleteCertificateInfo('IMPORT.EU.MR.2025.0003940', 'MR');

foreach ($certificates as $cert) {
    echo "CHED ID: " . $cert['id'] . "\n";
    
    // Access attachment information
    if (isset($cert['referenced_documents'])) {
        foreach ($cert['referenced_documents'] as $doc) {
            if (!empty($doc['attachment_uri'])) {
                echo "Attachment: " . $doc['attachment_filename'] . "\n";
                echo "Download URL: " . $doc['attachment_uri'] . "\n";
            }
        }
    }
}
```

### 4. Attachment Download

```php
// Extract attachment info from certificate
$doc = $certificate['referenced_documents'][0];
$attachment = $client->getCertificateAttachment(
    $certificate['id'],
    $doc['attachment_filename'],
    $doc['id']
);

if ($attachment['success']) {
    file_put_contents($doc['attachment_filename'], $attachment['binary_data']);
}
```

## Test Scripts

### 1. Complete Test Script
```bash
php test_complete_certificate_retrieval.php
```
Tests all methods and provides detailed output.

### 2. Direct URL Test Script
```bash
php test_direct_certificate_url.php
```
Focuses on testing direct URL access patterns.

## API Integration

The enhanced methods are now integrated into the main certificate retrieval flow:

1. **Smart Lookup First**: Uses `getCertificateByAnyReference` for direct lookup
2. **Complete Info Fallback**: Falls back to `getCompleteCertificateInfo` for comprehensive data
3. **Date Range Search**: Original method as final fallback

## Benefits

### ✅ Complete Information
- Full certificate details including parties, consignments, and locations
- Attachment metadata and download URLs
- Referenced documents with binary content access

### ✅ Better Performance
- Direct lookup methods are faster than date range searches
- Smart detection reduces unnecessary API calls

### ✅ Enhanced Functionality
- Access to certificate attachments (PDFs, documents)
- Web interface URL testing for alternative access methods
- Comprehensive error handling and logging

### ✅ Backward Compatibility
- Original methods still work as fallbacks
- Existing API endpoints continue to function
- Database integration remains unchanged

## Debug and Monitoring

The system creates debug files for troubleshooting:
- `storage/logs/direct_certificate_response.html` - Direct URL responses
- `storage/logs/traces_certificate_response.xml` - SOAP API responses
- `storage/logs/attachment_response.xml` - Attachment service responses

## Next Steps

1. **Test the new methods** with your sanitary references
2. **Monitor performance** and success rates
3. **Implement attachment storage** if needed
4. **Consider web scraping** if direct URLs provide better data access

## Testing

Run the test endpoint to see which methods work best for your use case:
```bash
curl "http://localhost:8011/api/certificates/test-complete?reference=IMPORT.EU.MR.2025.0003940"
```

This will test all methods and provide recommendations based on the results.
