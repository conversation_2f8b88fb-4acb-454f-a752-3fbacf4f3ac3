<?php

require_once 'app/Providers/TracesNtChedClient.php';

use App\Providers\TracesNtChedClient;

/**
 * Test script to demonstrate getting a CHED certificate by reference number
 * 
 * This script shows how to use the new getChedCertificateByReference method
 * to retrieve a specific certificate using its CHED reference number.
 */


 // Life is hard But did you ever coded While missing Aya
 
 

try {
    // Initialize the TRACES NT CHED client
    $client = new TracesNtChedClient(
        'n00385tm',                                          // Username
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',        // Auth key
        'onispa-mr',                                         // Client ID
        true                                                 // Use production (set to false for testing)
    );

    echo "=== TRACES NT CHED Certificate Retrieval Test ===\n\n";

    // Example CHED reference numbers (replace with actual ones)
    $testReferences = [
        'CHEDP.ES.2025.0044891',
    ];

    foreach ($testReferences as $chedReference) {
        echo "Attempting to retrieve certificate: {$chedReference}\n";
        echo str_repeat('-', 60) . "\n";

        try {
            // Get the certificate by reference
            $certificate = $client->getChedCertificateByReference($chedReference);

            echo "✓ SUCCESS! Certificate retrieved successfully.\n\n";

            // Display basic certificate information
            echo "Certificate Details:\n";
            echo "  ID: " . ($certificate['id'] ?? 'N/A') . "\n";
            echo "  Issue Date: " . ($certificate['issue_date_time'] ?? 'N/A') . "\n";
            echo "  Type Code: " . ($certificate['type_code'] ?? 'N/A') . "\n";
            echo "  Purpose Code: " . ($certificate['purpose_code'] ?? 'N/A') . "\n";

            // Display parties information
            if (!empty($certificate['parties'])) {
                echo "\nParties:\n";
                foreach ($certificate['parties'] as $party) {
                    echo "  - " . ($party['type'] ?? 'Unknown') . ": " . ($party['name'] ?? 'N/A') . "\n";
                    if (!empty($party['address']['country_id'])) {
                        echo "    Country: " . $party['address']['country_id'] . "\n";
                    }
                }
            }

            // Display consignment information
            if (!empty($certificate['consignments'])) {
                echo "\nConsignments: " . count($certificate['consignments']) . "\n";
                foreach ($certificate['consignments'] as $index => $consignment) {
                    echo "  Consignment " . ($index + 1) . ":\n";
                    echo "    ID: " . ($consignment['id'] ?? 'N/A') . "\n";
                    echo "    Items: " . count($consignment['consignment_items'] ?? []) . "\n";
                    
                    // Display commodity classifications
                    foreach ($consignment['consignment_items'] as $item) {
                        if (!empty($item['classifications'])) {
                            foreach ($item['classifications'] as $classification) {
                                if ($classification['system_id'] === 'CN') {
                                    echo "    CN Code: " . ($classification['class_code'] ?? 'N/A') . "\n";
                                    if (!empty($classification['class_names']['en'])) {
                                        echo "    Description: " . $classification['class_names']['en'] . "\n";
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Display locations
            if (!empty($certificate['locations'])) {
                echo "\nLocations:\n";
                foreach ($certificate['locations'] as $location) {
                    echo "  - " . ($location['type'] ?? 'Unknown') . ": " . ($location['name'] ?? 'N/A') . "\n";
                    echo "    ID: " . ($location['id'] ?? 'N/A') . " (Scheme: " . ($location['scheme_id'] ?? 'N/A') . ")\n";
                }
            }

            // Display referenced documents
            if (!empty($certificate['referenced_documents'])) {
                echo "\nReferenced Documents: " . count($certificate['referenced_documents']) . "\n";
                foreach ($certificate['referenced_documents'] as $doc) {
                    echo "  - Type: " . ($doc['type_name'] ?? $doc['type_code'] ?? 'N/A') . "\n";
                    echo "    ID: " . ($doc['id'] ?? 'N/A') . "\n";
                    echo "    Relationship: " . ($doc['relationship_name'] ?? $doc['relationship_type'] ?? 'N/A') . "\n";
                    if (!empty($doc['attachment_filename'])) {
                        echo "    Attachment: " . $doc['attachment_filename'] . "\n";
                    }
                }
            }

            echo "\n" . str_repeat('=', 80) . "\n\n";

        } catch (\Exception $e) {
            echo "✗ FAILED to retrieve certificate: " . $e->getMessage() . "\n";
            
            // Check for specific error types
            if (strpos($e->getMessage(), 'ChedCertificateNotFoundException') !== false) {
                echo "  → Certificate not found. Please check the reference number.\n";
            } elseif (strpos($e->getMessage(), 'ChedCertificatePermissionDeniedException') !== false) {
                echo "  → Permission denied. You may not have access to this certificate.\n";
            } elseif (strpos($e->getMessage(), 'Authentication failed') !== false) {
                echo "  → Authentication error. Please check your credentials.\n";
            }
            
            echo "\n" . str_repeat('=', 80) . "\n\n";
        }
    }

    echo "Test completed.\n";
    echo "\nNote: Check the following files for detailed logs:\n";
    echo "- certificate_response.xml (last response)\n";
    echo "- Laravel logs in storage/logs/\n";

} catch (\Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    echo "\nPlease check:\n";
    echo "1. Your TRACES NT credentials are correct\n";
    echo "2. Your WebService access is active\n";
    echo "3. The server time is synchronized\n";
    echo "4. Network connectivity to TRACES NT servers\n";
}

echo "\n=== Usage Instructions ===\n";
echo "To use this method in your Laravel application:\n\n";
echo "1. Include the TracesNtChedClient class:\n";
echo "   use App\\Providers\\TracesNtChedClient;\n\n";
echo "2. Initialize the client:\n";
echo "   \$client = new TracesNtChedClient(\$username, \$authKey, \$clientId, \$useProduction);\n\n";
echo "3. Get a certificate by reference:\n";
echo "   \$certificate = \$client->getChedCertificateByReference('CHEDPP.IT.2020.1000091');\n\n";
echo "4. Process the returned certificate data as needed.\n\n";
echo "The method returns a structured array with all certificate information\n";
echo "including parties, consignments, locations, and referenced documents.\n";
