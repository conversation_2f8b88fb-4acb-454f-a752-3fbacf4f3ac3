<?php

require_once 'vendor/autoload.php';

use App\Providers\TracesNtChedClient;

// Test script for complete certificate information retrieval
// This script demonstrates different methods to get complete certificate data

echo "=== TRACES NT Complete Certificate Retrieval Test ===\n\n";

try {
    // Initialize the client with production credentials
    $client = new TracesNtChedClient(
        'n00385tm',
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',
        'onispa-mr',
        true  // Use production
    );

    $sanitaryReference = 'IMPORT.EU.MR.2025.0003940';
    
    echo "Testing complete certificate retrieval for: {$sanitaryReference}\n";
    echo str_repeat('=', 80) . "\n\n";

    // Test 1: Direct URL access (web interface)
    echo "1. Testing direct certificate URL access...\n";
    echo str_repeat('-', 50) . "\n";
    
    try {
        $urlResult = $client->testDirectCertificateUrl($sanitaryReference);
        
        echo "URL: " . $urlResult['url'] . "\n";
        echo "HTTP Code: " . $urlResult['http_code'] . "\n";
        echo "Content Type: " . $urlResult['content_type'] . "\n";
        echo "Response Size: " . $urlResult['response_size'] . " bytes\n";
        echo "Requires Authentication: " . ($urlResult['requires_authentication'] ? 'Yes' : 'No') . "\n";
        echo "Contains Certificate Data: " . ($urlResult['contains_certificate_data'] ? 'Yes' : 'No') . "\n";
        
        if ($urlResult['http_code'] === 200) {
            echo "✓ Direct URL access successful\n";
        } else {
            echo "⚠ Direct URL access returned HTTP " . $urlResult['http_code'] . "\n";
        }
        
        echo "\nResponse Preview:\n";
        echo substr($urlResult['response_preview'], 0, 500) . "...\n";
        
    } catch (Exception $e) {
        echo "✗ Direct URL access failed: " . $e->getMessage() . "\n";
    }

    echo "\n" . str_repeat('=', 80) . "\n\n";

    // Test 2: Complete certificate information via API
    echo "2. Testing complete certificate information retrieval...\n";
    echo str_repeat('-', 50) . "\n";
    
    try {
        $completeCertificates = $client->getCompleteCertificateInfo($sanitaryReference, 'MR');
        
        echo "Found " . count($completeCertificates) . " certificate(s)\n\n";
        
        foreach ($completeCertificates as $index => $certificate) {
            echo "Certificate " . ($index + 1) . ":\n";
            echo "  CHED ID: " . ($certificate['id'] ?? 'N/A') . "\n";
            echo "  Status: " . ($certificate['status_name'] ?? $certificate['status'] ?? 'N/A') . "\n";
            echo "  Country of Dispatch: " . ($certificate['country_of_dispatch'] ?? 'N/A') . "\n";
            echo "  Consignor: " . ($certificate['consignor_name'] ?? 'N/A') . "\n";
            echo "  Update Date: " . ($certificate['update_date_time'] ?? 'N/A') . "\n";
            
            // Show referenced documents and attachments
            if (isset($certificate['referenced_documents']) && !empty($certificate['referenced_documents'])) {
                echo "  Referenced Documents: " . count($certificate['referenced_documents']) . "\n";
                foreach ($certificate['referenced_documents'] as $docIndex => $doc) {
                    echo "    Document " . ($docIndex + 1) . ":\n";
                    echo "      Type: " . ($doc['type_name'] ?? $doc['type_code'] ?? 'N/A') . "\n";
                    echo "      ID: " . ($doc['id'] ?? 'N/A') . "\n";
                    echo "      Filename: " . ($doc['attachment_filename'] ?? 'N/A') . "\n";
                    
                    if (!empty($doc['attachment_uri'])) {
                        echo "      Attachment URI: " . $doc['attachment_uri'] . "\n";
                        echo "      Can Download: " . (isset($doc['can_download_attachment']) && $doc['can_download_attachment'] ? 'Yes' : 'No') . "\n";
                    }
                }
            }
            
            // Show consignment information if available
            if (isset($certificate['consignments']) && !empty($certificate['consignments'])) {
                echo "  Consignments: " . count($certificate['consignments']) . "\n";
                foreach ($certificate['consignments'] as $consIndex => $consignment) {
                    if (isset($consignment['consignment_items']) && !empty($consignment['consignment_items'])) {
                        echo "    Consignment " . ($consIndex + 1) . " Items: " . count($consignment['consignment_items']) . "\n";
                        foreach ($consignment['consignment_items'] as $itemIndex => $item) {
                            echo "      Item " . ($itemIndex + 1) . ":\n";
                            echo "        Description: " . ($item['description'] ?? 'N/A') . "\n";
                            echo "        Scientific Name: " . ($item['scientific_name'] ?? 'N/A') . "\n";
                            echo "        Net Weight: " . ($item['net_weight'] ?? 'N/A') . "\n";
                            
                            if (isset($item['classifications']) && !empty($item['classifications'])) {
                                foreach ($item['classifications'] as $classification) {
                                    if ($classification['system_id'] === 'CN') {
                                        echo "        CN Code: " . ($classification['class_code'] ?? 'N/A') . "\n";
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            if (isset($certificate['detailed_retrieval_error'])) {
                echo "  ⚠ Detailed retrieval error: " . $certificate['detailed_retrieval_error'] . "\n";
            }
            
            echo "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Complete certificate retrieval failed: " . $e->getMessage() . "\n";
    }

    echo str_repeat('=', 80) . "\n\n";

    // Test 3: Basic search method (for comparison)
    echo "3. Testing basic search method (for comparison)...\n";
    echo str_repeat('-', 50) . "\n";
    
    try {
        $basicCertificates = $client->findChedCertificatesBySanitaryReference($sanitaryReference, 'MR');
        
        echo "Basic search found " . count($basicCertificates) . " certificate(s)\n";
        
        foreach ($basicCertificates as $index => $certificate) {
            echo "Certificate " . ($index + 1) . " (Basic Info):\n";
            echo "  CHED ID: " . ($certificate['id'] ?? 'N/A') . "\n";
            echo "  Status: " . ($certificate['status_name'] ?? $certificate['status'] ?? 'N/A') . "\n";
            echo "  References: " . (isset($certificate['references']) ? count($certificate['references']) : 0) . "\n";
            echo "  Commodities: " . (isset($certificate['commodities']) ? count($certificate['commodities']) : 0) . "\n";
            echo "\n";
        }
        
    } catch (Exception $e) {
        echo "✗ Basic search failed: " . $e->getMessage() . "\n";
    }

    echo str_repeat('=', 80) . "\n\n";

    // Test 4: Test attachment download (if we have attachment info)
    echo "4. Testing attachment download capability...\n";
    echo str_repeat('-', 50) . "\n";
    
    // This would require actual attachment information from a certificate
    echo "Note: Attachment download requires specific CHED ID, filename, and document ID\n";
    echo "This would be extracted from the complete certificate information above.\n";
    
    // Example of how to use attachment download:
    /*
    if (isset($completeCertificates[0]['referenced_documents'][0])) {
        $doc = $completeCertificates[0]['referenced_documents'][0];
        if (!empty($doc['attachment_filename']) && !empty($doc['id'])) {
            try {
                $attachment = $client->getCertificateAttachment(
                    $completeCertificates[0]['id'],
                    $doc['attachment_filename'],
                    $doc['id']
                );
                echo "Attachment download result:\n";
                print_r($attachment);
            } catch (Exception $e) {
                echo "Attachment download failed: " . $e->getMessage() . "\n";
            }
        }
    }
    */

    echo "\n=== Test Complete ===\n";
    echo "Check the logs directory for detailed response files:\n";
    echo "- storage/logs/direct_certificate_response.html\n";
    echo "- storage/logs/traces_certificate_response.xml\n";
    echo "- storage/logs/attachment_response.xml (if attachment test was run)\n";

} catch (Exception $e) {
    echo "✗ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
