<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\ChedCertificate;
use App\Models\ChedSanitaryReference;

echo "=== API Fallback Test ===\n\n";

// Test references - replace with actual references you want to test
$testReferences = [
    'IMPORT.EU.MR.2025.0000007',
    'CHEDP.FR.2025.0000038',
    // Add more test references here
];

foreach ($testReferences as $reference) {
    echo "Testing reference: {$reference}\n";
    echo "----------------------------------------\n";
    
    // Check if it exists in database
    $inDatabase = ChedCertificate::where('ched_id', $reference)->exists() ||
                  ChedSanitaryReference::where('sanitary_reference', $reference)->exists();
    
    echo "In database: " . ($inDatabase ? "YES" : "NO") . "\n";
    
    if (!$inDatabase) {
        echo "This reference will trigger API fallback when called via API\n";
    }
    
    // Test the API endpoint
    $url = "http://localhost:8011/api/certificates/products?reference=" . urlencode($reference);
    echo "Test URL: {$url}\n";
    
    // Make the API call
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "CURL Error: {$error}\n";
    } else {
        echo "HTTP Status: {$httpCode}\n";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "Success: " . ($data['success'] ? 'YES' : 'NO') . "\n";
            
            if ($data['success']) {
                echo "Source: " . ($data['source'] ?? 'unknown') . "\n";
                echo "CHED ID: " . ($data['ched_id'] ?? 'unknown') . "\n";
                echo "Products: " . ($data['products_count'] ?? 0) . "\n";
                
                if (isset($data['note']) && $data['note']) {
                    echo "Note: " . $data['note'] . "\n";
                }
            } else {
                echo "Error: " . ($data['message'] ?? 'Unknown error') . "\n";
                echo "Error Code: " . ($data['error_code'] ?? 'unknown') . "\n";
            }
        } else {
            echo "Invalid JSON response\n";
            echo "Response: " . substr($response, 0, 200) . "...\n";
        }
    }
    
    echo "\n";
}

echo "=== Database Check After Tests ===\n";
$certCount = ChedCertificate::count();
$refCount = ChedSanitaryReference::count();
echo "Certificates in database: {$certCount}\n";
echo "Sanitary references in database: {$refCount}\n";

echo "\n=== How to Test API Fallback ===\n";
echo "1. Find a reference that's NOT in your database\n";
echo "2. Call the API with that reference\n";
echo "3. The API should:\n";
echo "   - Try database first (not found)\n";
echo "   - Call TRACES API as fallback\n";
echo "   - Store the result in database\n";
echo "   - Return the data with source='api_fallback'\n";
echo "4. Call the same reference again - should now come from database\n";

echo "\n=== Manual Test Commands ===\n";
foreach ($testReferences as $reference) {
    echo "curl \"http://localhost:8011/api/certificates/products?reference={$reference}\"\n";
}

echo "\n=== Test Complete ===\n";
