<?php

require_once 'app/Providers/TracesNtChedClient.php';

use App\Providers\TracesNtChedClient;

try {
    // Initialize the TRACES NT CHED client
    $client = new TracesNtChedClient(
        'n00385tm',                                          // Username
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',        // Auth key
        'onispa-mr',                                         // Client ID
        true                                                 // Use production
    );

    echo "=== TRACES NT CHED Certificate Search by Sanitary Reference ===\n\n";

    // Test the sanitary reference from your example
    $sanitaryReference = 'IMPORT.EU.MR.2025.0000007';
    $countryOfDispatch = 'MR'; // Mauritania

    echo "Searching for CHED certificates containing sanitary reference: {$sanitaryReference}\n";
    echo "Country of dispatch filter: {$countryOfDispatch}\n";
    echo str_repeat('-', 80) . "\n";

    // Method 1: Use the specific sanitary reference search method
    echo "\n=== Method 1: Direct Sanitary Reference Search ===\n";
    try {
        $certificates = $client->findChedCertificatesBySanitaryReference(
            $sanitaryReference, 
            $countryOfDispatch,
            '2025-01-01',  // Start date
            '2025-01-03'   // End date
        );

        echo "SUCCESS! Found " . count($certificates) . " CHED certificate(s) containing the sanitary reference.\n\n";

        foreach ($certificates as $index => $certificate) {
            echo "Certificate " . ($index + 1) . ":\n";
            echo "  CHED ID: " . ($certificate['id'] ?? 'N/A') . "\n";
            echo "  Type: " . ($certificate['type'] ?? 'N/A') . "\n";
            echo "  Status: " . ($certificate['status'] ?? 'N/A') . " (" . ($certificate['status_name'] ?? 'N/A') . ")\n";
            echo "  Local Reference: " . ($certificate['local_reference'] ?? 'N/A') . "\n";
            echo "  Country of Dispatch: " . ($certificate['country_of_dispatch'] ?? 'N/A') . "\n";
            echo "  Consignor: " . ($certificate['consignor_name'] ?? 'N/A') . "\n";
            echo "  Consignee: " . ($certificate['consignee_name'] ?? 'N/A') . "\n";
            echo "  Update Date: " . ($certificate['update_date_time'] ?? 'N/A') . "\n";
            
            // Show the matching sanitary references
            if (isset($certificate['references']) && is_array($certificate['references'])) {
                echo "  References containing '{$sanitaryReference}':\n";
                foreach ($certificate['references'] as $ref) {
                    if (isset($ref['id']) && $ref['id'] === $sanitaryReference) {
                        echo "    - Type: " . ($ref['type_name'] ?? 'N/A') . "\n";
                        echo "    - ID: " . ($ref['id'] ?? 'N/A') . "\n";
                        echo "    - Relationship: " . ($ref['relationship_name'] ?? 'N/A') . "\n";
                        echo "    - Attachment: " . ($ref['attachment'] ?? 'N/A') . "\n";
                    }
                }
            }
            echo "\n";
        }

    } catch (Exception $e) {
        echo "ERROR in Method 1: " . $e->getMessage() . "\n\n";
    }

    // Method 2: Use the smart auto-detection method
    echo "\n=== Method 2: Smart Auto-Detection Method ===\n";
    try {
        $result = $client->getCertificateByAnyReference($sanitaryReference, $countryOfDispatch);

        if (isset($result['id'])) {
            // Single certificate returned
            echo "SUCCESS! Found 1 CHED certificate (auto-detected as sanitary reference).\n";
            echo "CHED ID: " . $result['id'] . "\n";
            echo "Status: " . ($result['status_name'] ?? 'N/A') . "\n";
        } else {
            // Multiple certificates returned
            echo "SUCCESS! Found " . count($result) . " CHED certificates (auto-detected as sanitary reference).\n";
            foreach ($result as $index => $cert) {
                echo "Certificate " . ($index + 1) . ": " . ($cert['id'] ?? 'N/A') . "\n";
            }
        }

    } catch (Exception $e) {
        echo "ERROR in Method 2: " . $e->getMessage() . "\n\n";
    }

    // Method 3: Test with a CHED ID for comparison
    echo "\n=== Method 3: Test with CHED ID (for comparison) ===\n";
    try {
        // Use the CHED ID from your example response
        $chedId = 'CHEDP.FR.2025.0000038';
        echo "Testing with CHED ID: {$chedId}\n";
        
        $certificate = $client->getCertificateByAnyReference($chedId);
        
        echo "SUCCESS! Retrieved certificate (auto-detected as CHED ID).\n";
        echo "Certificate ID: " . ($certificate['id'] ?? 'N/A') . "\n";
        echo "Type: " . ($certificate['type_code'] ?? 'N/A') . "\n";
        
    } catch (Exception $e) {
        echo "ERROR in Method 3: " . $e->getMessage() . "\n\n";
    }

    echo "\n=== Usage Summary ===\n";
    echo "1. Use findChedCertificatesBySanitaryReference() for specific sanitary reference searches\n";
    echo "2. Use getCertificateByAnyReference() for automatic detection of reference type\n";
    echo "3. Use getChedCertificateByReference() for direct CHED ID lookups\n\n";

    echo "The sanitary reference '{$sanitaryReference}' should now be searchable!\n";

} catch (Exception $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
