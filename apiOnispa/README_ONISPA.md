# ONISPA Mauritania - TRACES API Integration System

## Overview

This is a modern web application for ONISPA Mauritania that automates the process of managing fish export certificates through integration with the EU's TRACES NT (New Technology) system. The application eliminates manual data entry and provides a streamlined interface for certificate management.

## Features

### 🔐 Authentication System
- Secure login with default credentials (admin/password)
- Session-based authentication
- Protected routes and middleware

### 📊 Dashboard
- Real-time statistics on certificates
- Quick action buttons
- Recent certificates overview
- Visual status indicators

### 📋 Certificate Management
- **Automated Fetching**: Connect to TRACES EU API to retrieve validated certificates
- **Date Range Filtering**: Specify time periods for certificate retrieval
- **Certificate Listing**: Display all fetched certificates in an organized table
- **Detailed View**: Popup modals showing complete certificate details
- **One-click Fetch**: Simple button to initiate API data retrieval
- **Certificate Selection**: Select single or multiple certificates
- **Batch Operations**: Bulk selection and processing capabilities
- **Send to API**: Submit selected certificates back to TRACES API

### ⚙️ API Configuration
- Manage TRACES API connection parameters
- Environment switching (Production/Training)
- Secure credential storage
- Connection testing capabilities

### 📝 System Logs
- Monitor API calls and system activities
- Error tracking and debugging
- Filterable log entries
- Real-time statistics

## Technology Stack

- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: Tailwind CSS, Alpine.js
- **Database**: PostgreSQL with PostGIS
- **Authentication**: Custom session-based auth
- **API Integration**: SOAP with WS-Security
- **Icons**: Font Awesome 6

## Installation & Setup

### Prerequisites
- PHP 8.2 or higher
- Composer
- Docker and Docker Compose
- PostgreSQL

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd apiOnispa
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   php artisan migrate
   ```

5. **Start the application**
   ```bash
   # Using Docker (recommended)
   docker-compose up -d
   
   # Or using Laravel's built-in server
   php artisan serve
   ```

6. **Access the application**
   - URL: `http://localhost:8011` (Docker) or `http://localhost:8000` (Laravel serve)
   - Default credentials: `admin` / `password`

## Docker Setup

The application includes a complete Docker setup with:

- **Laravel Application**: Port 8011
- **PostgreSQL Database**: Port 5411
- **pgAdmin**: Port 5011

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## API Integration

### TRACES NT Client

The application uses a custom `TracesNtChedClient` class that handles:

- SOAP communication with EU TRACES NT system
- WS-Security authentication
- Certificate retrieval and submission
- Error handling and logging

### Configuration

API parameters can be configured through the web interface:

- **Username**: TRACES API username
- **Authentication Key**: API authentication key
- **Client ID**: Client identifier
- **Environment**: Production or Training mode

## Database Schema

### Certificates Table

The main `certificates` table stores:

- Certificate ID and local reference
- Status and status name
- BCP codes and UNLOCODE
- Country information (issuance, entry, dispatch, origin, destination)
- Consignor and consignee details
- Timestamps (create, update, status change, declaration, decision)
- Commodities and references (JSON fields)

## Usage Guide

### 1. Login
- Navigate to the application URL
- Use default credentials: `admin` / `password`

### 2. Configure API Parameters
- Go to "API Parameters" page
- Enter your TRACES API credentials
- Select environment (Production/Training)
- Save configuration

### 3. Fetch Certificates
- Navigate to "Certificates" page
- Set date range for certificate retrieval
- Click "Fetch Certificates" button
- Monitor progress and results

### 4. Manage Certificates
- View certificate list with pagination
- Use filters and search functionality
- Select certificates for batch operations
- View detailed certificate information in modals
- Send selected certificates back to TRACES API

### 5. Monitor System
- Check "Logs" page for system activity
- Monitor API call success/failure rates
- Review error messages and debugging information

## Security Features

- Session-based authentication
- CSRF protection
- Input validation and sanitization
- Secure API credential handling
- Logging of all system activities

## Development

### Project Structure

```
apiOnispa/
├── app/
│   ├── Http/Controllers/     # Application controllers
│   ├── Models/              # Eloquent models
│   └── Http/Middleware/     # Custom middleware
├── resources/views/         # Blade templates
├── routes/                  # Application routes
├── database/migrations/     # Database migrations
├── TracesNtChedClient.php  # TRACES API client
└── docker-compose.yml      # Docker configuration
```

### Key Files

- `TracesNtChedClient.php`: Main API integration class
- `CertificateController.php`: Certificate management logic
- `app.blade.php`: Main application layout
- `certificates/index.blade.php`: Certificate management interface

## Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Verify API credentials in "API Parameters"
   - Check network connectivity
   - Review logs for detailed error messages

2. **Database Connection**
   - Ensure PostgreSQL is running
   - Verify database credentials in `.env`
   - Run migrations: `php artisan migrate`

3. **Authentication Issues**
   - Clear browser cache and cookies
   - Restart application server
   - Check session configuration

### Logs and Debugging

- System logs are available in the "Logs" page
- Laravel logs: `storage/logs/laravel.log`
- Docker logs: `docker-compose logs -f`

## Support

For technical support or questions about the ONISPA TRACES API integration system, please contact the development team.

## License

This project is proprietary software developed for ONISPA Mauritania.

---

**ONISPA Mauritania** - Official authority for fish and seafood products exported to the EU 