<?php

return [

    /*
    |--------------------------------------------------------------------------
    | TRACES NT WebService Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration options for connecting to the
    | TRACES NT (Trade Control and Expert System) WebService API for
    | retrieving CHED (Common Health Entry Document) certificates.
    |
    | Add this configuration to your config/services.php file under the
    | 'traces' key, or create a separate config/traces.php file.
    |
    */

    'traces' => [

        /*
        |--------------------------------------------------------------------------
        | Authentication Credentials
        |--------------------------------------------------------------------------
        |
        | These are your TRACES NT WebService authentication credentials.
        | You can obtain these from your TRACES NT user profile under
        | the WebService section.
        |
        */

        'username' => env('TRACES_USERNAME', 'your_traces_username'),
        'auth_key' => env('TRACES_AUTH_KEY', 'your_webservice_auth_key'),
        'client_id' => env('TRACES_CLIENT_ID', 'your_client_id'),

        /*
        |--------------------------------------------------------------------------
        | Environment Configuration
        |--------------------------------------------------------------------------
        |
        | Set whether to use the production environment or testing environment.
        | 
        | Production: https://webgate.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2
        | Training: https://webgate.training.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2
        | Acceptance: https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2
        |
        */

        'use_production' => env('TRACES_USE_PRODUCTION', false),

        /*
        |--------------------------------------------------------------------------
        | Connection Settings
        |--------------------------------------------------------------------------
        |
        | Configure timeout and SSL verification settings for the SOAP requests.
        |
        */

        'timeout' => env('TRACES_TIMEOUT', 60),
        'connect_timeout' => env('TRACES_CONNECT_TIMEOUT', 30),
        'verify_ssl' => env('TRACES_VERIFY_SSL', true),

        /*
        |--------------------------------------------------------------------------
        | Logging Configuration
        |--------------------------------------------------------------------------
        |
        | Configure logging levels and options for TRACES NT API calls.
        |
        */

        'log_requests' => env('TRACES_LOG_REQUESTS', true),
        'log_responses' => env('TRACES_LOG_RESPONSES', true),
        'log_level' => env('TRACES_LOG_LEVEL', 'info'),

        /*
        |--------------------------------------------------------------------------
        | Rate Limiting
        |--------------------------------------------------------------------------
        |
        | Configure rate limiting to avoid overwhelming the TRACES NT servers.
        |
        */

        'rate_limit' => [
            'max_requests_per_minute' => env('TRACES_RATE_LIMIT', 60),
            'max_concurrent_requests' => env('TRACES_MAX_CONCURRENT', 5),
        ],

        /*
        |--------------------------------------------------------------------------
        | Retry Configuration
        |--------------------------------------------------------------------------
        |
        | Configure automatic retry behavior for failed requests.
        |
        */

        'retry' => [
            'max_attempts' => env('TRACES_RETRY_ATTEMPTS', 3),
            'delay_seconds' => env('TRACES_RETRY_DELAY', 2),
            'exponential_backoff' => env('TRACES_RETRY_EXPONENTIAL', true),
        ],

        /*
        |--------------------------------------------------------------------------
        | Cache Configuration
        |--------------------------------------------------------------------------
        |
        | Configure caching for certificate data to reduce API calls.
        |
        */

        'cache' => [
            'enabled' => env('TRACES_CACHE_ENABLED', true),
            'ttl_minutes' => env('TRACES_CACHE_TTL', 60),
            'prefix' => env('TRACES_CACHE_PREFIX', 'traces_ched_'),
        ],

        /*
        |--------------------------------------------------------------------------
        | Default Search Parameters
        |--------------------------------------------------------------------------
        |
        | Default parameters for certificate searches.
        |
        */

        'defaults' => [
            'page_size' => env('TRACES_DEFAULT_PAGE_SIZE', 50),
            'language_code' => env('TRACES_DEFAULT_LANGUAGE', 'en'),
            'date_format' => env('TRACES_DATE_FORMAT', 'Y-m-d'),
        ],

    ],

];

/*
|--------------------------------------------------------------------------
| Environment Variables (.env file)
|--------------------------------------------------------------------------
|
| Add these variables to your .env file:
|
| # TRACES NT WebService Configuration
| TRACES_USERNAME=your_traces_username
| TRACES_AUTH_KEY=your_webservice_auth_key_here
| TRACES_CLIENT_ID=your_client_id
| TRACES_USE_PRODUCTION=false
| 
| # Connection Settings
| TRACES_TIMEOUT=60
| TRACES_CONNECT_TIMEOUT=30
| TRACES_VERIFY_SSL=true
| 
| # Logging
| TRACES_LOG_REQUESTS=true
| TRACES_LOG_RESPONSES=true
| TRACES_LOG_LEVEL=info
| 
| # Rate Limiting
| TRACES_RATE_LIMIT=60
| TRACES_MAX_CONCURRENT=5
| 
| # Retry Configuration
| TRACES_RETRY_ATTEMPTS=3
| TRACES_RETRY_DELAY=2
| TRACES_RETRY_EXPONENTIAL=true
| 
| # Cache Configuration
| TRACES_CACHE_ENABLED=true
| TRACES_CACHE_TTL=60
| TRACES_CACHE_PREFIX=traces_ched_
| 
| # Defaults
| TRACES_DEFAULT_PAGE_SIZE=50
| TRACES_DEFAULT_LANGUAGE=en
| TRACES_DATE_FORMAT=Y-m-d
|
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| Usage Examples
|--------------------------------------------------------------------------
|
| // In your controller or service class:
| 
| use App\Providers\TracesNtChedClient;
| 
| // Initialize with config values
| $client = new TracesNtChedClient(
|     config('services.traces.username'),
|     config('services.traces.auth_key'),
|     config('services.traces.client_id'),
|     config('services.traces.use_production')
| );
| 
| // Get a certificate by reference
| $certificate = $client->getChedCertificateByReference('CHEDPP.IT.2020.1000091');
| 
| // Get validated fish certificates for a date range
| $certificates = $client->getValidatedFishCertificates('2025-01-01', '2025-01-31');
|
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| Security Notes
|--------------------------------------------------------------------------
|
| 1. Never commit your actual TRACES NT credentials to version control
| 2. Use environment variables for all sensitive configuration
| 3. Rotate your WebService auth key regularly
| 4. Monitor API usage to detect unauthorized access
| 5. Use HTTPS for all API endpoints in production
| 6. Implement proper authentication and authorization in your application
| 7. Log all certificate access for audit purposes
|
|--------------------------------------------------------------------------
*/
