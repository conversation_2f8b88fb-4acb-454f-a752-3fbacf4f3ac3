# Certificate Products API

This document describes the new API endpoint that retrieves all products linked to a certificate by its reference number.

## Overview

The Certificate Products API allows you to retrieve detailed information about all products (commodities) associated with a specific CHED certificate. The API will first check the local database for cached certificate data, and if not found, will fetch the information directly from the TRACES NT API.

## Endpoint

```
GET /api/certificates/products
```

## Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `reference` | string | Yes | CHED certificate reference number |

### Reference Format

The certificate reference must follow the CHED format:
```
CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]
```

**Examples:**
- `CHEDPP.IT.2020.1000091` (Plants/Plant Products, Italy, 2020)
- `CHEDP.MR.2025.0000001` (Products, Mauritania, 2025)
- `CHEDA.FR.2024.0500612` (Animals, France, 2024)

**CHED Types:**
- `CHEDPP` - Plants and Plant Products
- `CHEDP` - Products of Animal Origin
- `CHEDA` - Animals
- `CHEDD` - Article D (Feed and Food)

## Response Format

### Success Response (200 OK)

```json
{
  "success": true,
  "certificate_reference": "CHEDPP.IT.2020.1000091",
  "source": "traces_api",
  "products_count": 2,
  "products": [
    {
      "sequence": 1,
      "system_id": "CN",
      "system_name": "CN Code (Combined Nomenclature)",
      "classification_code": "0908",
      "description": "Hazelnuts or filberts (Corylus spp.)",
      "descriptions": {
        "en": "Hazelnuts or filberts (Corylus spp.)",
        "fr": "Noisettes (Corylus spp.)"
      },
      "source": "traces_api"
    },
    {
      "sequence": 2,
      "system_id": "CN",
      "system_name": "CN Code (Combined Nomenclature)",
      "classification_code": "0802",
      "description": "Other nuts, fresh or dried",
      "descriptions": {
        "en": "Other nuts, fresh or dried"
      },
      "source": "traces_api"
    }
  ]
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE"
}
```

## Response Fields

### Main Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Whether the request was successful |
| `certificate_reference` | string | The requested certificate reference |
| `source` | string | Data source: `"traces_api"` or `"local_database"` |
| `products_count` | integer | Number of products found |
| `products` | array | Array of product objects |

### Product Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `sequence` | integer | Product sequence number in the certificate |
| `system_id` | string | Classification system ID (e.g., "CN", "HS") |
| `system_name` | string | Full name of the classification system |
| `classification_code` | string | Product classification code |
| `description` | string | Product description in English |
| `descriptions` | object | Product descriptions in multiple languages |
| `source` | string | Source of this product data |

## HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success - products retrieved |
| 400 | Bad Request - invalid reference format or missing parameter |
| 401 | Unauthorized - authentication error with TRACES API |
| 403 | Forbidden - access denied to certificate |
| 404 | Not Found - certificate not found |
| 500 | Internal Server Error - configuration missing or retrieval error |

## Error Codes

| Error Code | Description |
|------------|-------------|
| `CERTIFICATE_NOT_FOUND` | Certificate reference not found |
| `PERMISSION_DENIED` | Access denied to certificate |
| `AUTHENTICATION_ERROR` | TRACES API authentication failed |
| `CONFIGURATION_MISSING` | API configuration not set up |
| `RETRIEVAL_ERROR` | Generic retrieval error |

## Usage Examples

### Using curl

```bash
# Get products for a certificate
curl -H "Accept: application/json" \
     "http://localhost:8000/api/certificates/products?reference=CHEDPP.IT.2020.1000091"

# Test with invalid reference (should return 400)
curl -H "Accept: application/json" \
     "http://localhost:8000/api/certificates/products?reference=INVALID-REF"
```

### Using PHP

```php
$reference = 'CHEDPP.IT.2020.1000091';
$url = "http://localhost:8000/api/certificates/products?reference=" . urlencode($reference);

$response = file_get_contents($url, false, stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Accept: application/json'
    ]
]));

$data = json_decode($response, true);

if ($data['success']) {
    echo "Found {$data['products_count']} products:\n";
    foreach ($data['products'] as $product) {
        echo "- {$product['classification_code']}: {$product['description']}\n";
    }
} else {
    echo "Error: {$data['message']}\n";
}
```

### Using JavaScript (fetch)

```javascript
const reference = 'CHEDPP.IT.2020.1000091';
const url = `http://localhost:8000/api/certificates/products?reference=${encodeURIComponent(reference)}`;

fetch(url, {
    headers: {
        'Accept': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log(`Found ${data.products_count} products:`);
        data.products.forEach(product => {
            console.log(`- ${product.classification_code}: ${product.description}`);
        });
    } else {
        console.error(`Error: ${data.message}`);
    }
})
.catch(error => console.error('Request failed:', error));
```

## Testing

Two test scripts are provided:

### PHP Test Script
```bash
php test_certificate_products_api.php
```

### Shell/curl Test Script
```bash
./test_products_api_curl.sh
```

Both scripts will test various scenarios including:
- Valid certificate references
- Invalid reference formats
- Missing parameters
- Error handling

## Data Sources

The API uses two data sources in order of preference:

1. **Local Database** (`local_database`): Cached certificate data from previous TRACES API calls
2. **TRACES API** (`traces_api`): Live data fetched directly from the TRACES NT system

The `source` field in the response indicates which source was used for the data.

## Configuration

The API requires TRACES NT configuration to be set up in the application. This includes:
- Username
- Authentication key
- Client ID
- Environment (production/training)

If configuration is missing, the API will return a 500 error with `CONFIGURATION_MISSING` error code.

## Rate Limiting

The API endpoint may be subject to rate limiting. Check your application's rate limiting configuration for details.

## Support

For issues or questions about this API, please check:
1. Application logs for detailed error information
2. TRACES NT connectivity and authentication
3. Certificate reference format and validity
