# EU Import Legacy Service

A standalone PHP class for fetching EU import certificates from TRACES API and parsing products without any database dependencies. Perfect for integration with legacy systems.

## Features

- **Standalone**: No Laravel dependencies, works in any PHP environment
- **Hardcoded Credentials**: TRACES API credentials are built-in for immediate use
- **Complete Product Parsing**: Extracts all product details from TRACES XML responses
- **Form-Ready Data**: Returns data formatted for easy form population
- **No Database**: Doesn't store anything, just fetches and parses
- **Error Handling**: Comprehensive error handling with meaningful messages
- **Multiple References**: Can process multiple certificates at once

## Quick Start

```php
<?php
require_once 'app/Services/EuImportLegacyService.php';

use App\Services\EuImportLegacyService;

$service = new EuImportLegacyService();

// Fetch products for a reference
$result = $service->getProductsForForms('IMPORT.EU.MR.2025.0003940');

if ($result['success']) {
    foreach ($result['products'] as $product) {
        echo "Product: {$product['product_name']}\n";
        echo "Weight: {$product['quantity']} {$product['unit']}\n";
        echo "Origin: {$product['origin_country']}\n";
        // ... populate your forms
    }
}
```

## Main Methods

### `getProductsForForms($reference)`

Returns products formatted for form population.

**Parameters:**
- `$reference` (string): EU-Import reference (e.g., "IMPORT.EU.MR.2025.0003940")

**Returns:**
```php
[
    'success' => true,
    'reference' => 'IMPORT.EU.MR.2025.0003940',
    'products' => [
        [
            'id' => 1,
            'product_name' => 'White grouper',
            'scientific_name' => 'Epinephelus aeneus',
            'common_name' => 'White grouper',
            'origin_country' => 'Mauritania',
            'storage_location' => 'M.F.C Sarl',
            'nature' => 'Fishery products, Wild stock',
            'product_type' => 'Chilled',
            'packaging_type' => 'Polystyrene Box',
            'unit' => 'KGM',
            'quantity' => 181.0,
            'package_count' => 10.0,
            'batch_id' => '57/25',
            'fao_code' => 'GPW',
            'cn_code' => '03028990',
            'is_wild' => true,
            'collection_date' => '2025-09-09T20:07:00.000+02:00',
            'processing_plant' => [
                'id' => '01.061',
                'name' => 'M.F.C Sarl'
            ],
            'processes' => [...]
        ],
        // ... more products
    ],
    'summary' => [
        'total_products' => 8,
        'total_weight' => 544.4,
        'total_packages' => 43.0
    ]
]
```

### `fetchAndParseProducts($reference)`

Returns complete certificate data with parsed products.

### `searchMultipleReferences($references)`

Process multiple references at once.

**Parameters:**
- `$references` (array): Array of EU-Import references

### `parseProductsFromXml($xmlContent, $importId)`

Parse products from raw XML content (if you already have the XML).

## Product Data Structure

Each product contains:

- **Basic Info**: `id`, `product_name`, `scientific_name`, `common_name`
- **Origin**: `origin_country`, `storage_location`
- **Classification**: `nature`, `product_type`, `fao_code`, `cn_code`
- **Packaging**: `packaging_type`, `unit`, `quantity`, `package_count`
- **Traceability**: `batch_id`, `collection_date`, `processing_plant`, `processes`
- **Flags**: `is_wild` (true for wild stock, false for aquaculture)

## Form Integration Example

```php
// Get products for a reference
$result = $service->getProductsForForms('IMPORT.EU.MR.2025.0003940');

if ($result['success']) {
    foreach ($result['products'] as $product) {
        // Populate your legacy form fields
        $_POST['product_' . $product['id'] . '_name'] = $product['product_name'];
        $_POST['product_' . $product['id'] . '_origin'] = $product['origin_country'];
        $_POST['product_' . $product['id'] . '_storage'] = $product['storage_location'];
        $_POST['product_' . $product['id'] . '_nature'] = $product['nature'];
        $_POST['product_' . $product['id'] . '_type'] = $product['product_type'];
        $_POST['product_' . $product['id'] . '_packaging'] = $product['packaging_type'];
        $_POST['product_' . $product['id'] . '_unit'] = $product['unit'];
        $_POST['product_' . $product['id'] . '_quantity'] = $product['quantity'];
        $_POST['product_' . $product['id'] . '_packages'] = $product['package_count'];
        
        // Add more fields as needed...
    }
}
```

## Error Handling

```php
try {
    $result = $service->getProductsForForms($reference);
    
    if (!$result['success']) {
        echo "Error: " . $result['error'];
        return;
    }
    
    // Process products...
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage();
}
```

## Configuration

The class has hardcoded TRACES API credentials:

- **Username**: `n00385tm`
- **Auth Key**: `7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh`
- **Client ID**: `onispa-mr`
- **Environment**: Production (`USE_PRODUCTION = true`)

## Requirements

- PHP 7.4 or higher
- cURL extension
- SimpleXML extension
- OpenSSL extension

## Example Output

For reference `IMPORT.EU.MR.2025.0003940`, you'll get 8 products:

1. **White grouper** (Epinephelus aeneus) - 181.0 KGM, 10 packages
2. **Dusky grouper** (Epinephelus marginatus) - 3.3 KGM, 1 package
3. **Redbanded seabream** (Pagrus auriga) - 61.5 KGM, 4 packages
4. **Goldblotch grouper** (Epinephelus costae) - 17.3 KGM, 2 packages
5. **Bluespotted seabream** (Pagrus caeruleostictus) - 75.7 KGM, 5 packages
6. **Mottled grouper** (Mycteroperca rubra) - 7.5 KGM, 1 package
7. **Senegalese sole** (Solea senegalensis) - 99.6 KGM, 10 packages
8. **West African goatfish** (Pseudupeneus prayensis) - 98.5 KGM, 10 packages

All from Mauritania, processed by M.F.C Sarl (01.061), wild stock, chilled.

## Files

- `app/Services/EuImportLegacyService.php` - Main service class
- `legacy_usage_example.php` - Complete usage examples
- `EU_IMPORT_LEGACY_SERVICE_README.md` - This documentation
