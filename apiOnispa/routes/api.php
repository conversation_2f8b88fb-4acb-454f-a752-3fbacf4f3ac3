<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\EuImportController;


Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::post('/certificates/fetch', [CertificateController::class, 'fetchAndStore']);

Route::get('/certificates/products', [CertificateController::class, 'getCertificateProducts']);

// New endpoint for inspection form mapping
Route::get('/certificates/inspection-form', [CertificateController::class, 'getCertificateForInspectionForm']);

// New endpoint for testing complete certificate retrieval methods
Route::get('/certificates/test-complete', [CertificateController::class, 'testCompleteCertificateRetrieval']);


// testEuleagacy
Route::get('/certificates/test-eu-legacy', [EuImportController::class, 'testEuleagacy']);


// EU-Import Certificate Routes
Route::prefix('eu-import')->group(function () {
    // Fetch and store EU-Import certificate from TRACES API
    Route::post('/fetch', [EuImportController::class, 'fetchAndStore']);

    // Get EU-Import certificate PDF
    Route::post('/pdf', [EuImportController::class, 'getPdf']);

    // Get EU-Import certificate signed PDF
    Route::post('/signed-pdf', [EuImportController::class, 'getSignedPdf']);

    // Search EU-Import certificates
    Route::post('/search', [EuImportController::class, 'search']);

    // Get certificate commodities
    Route::get('/commodities', [EuImportController::class, 'getCommodities']);
});