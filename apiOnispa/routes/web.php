<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\ApiParameterController;
use App\Http\Controllers\LogController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserController;

// Authentication routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware(['auth'])->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/certificates', [CertificateController::class, 'index'])->name('certificates');
    Route::post('/certificates/fetch', [CertificateController::class, 'fetchAndStore'])->name('certificates.fetch');
    Route::post('/certificates/send', [CertificateController::class, 'sendSelected'])->name('certificates.send');
    Route::get('/certificates/{id}', [CertificateController::class, 'show'])->name('certificates.show');
    Route::get('/api-parameters', [ApiParameterController::class, 'index'])->name('api-parameters');
    Route::post('/api-parameters', [ApiParameterController::class, 'update'])->name('api-parameters.update');
    Route::get('/logs', [LogController::class, 'index'])->name('logs');
    Route::get('/api/logs/{id}', [LogController::class, 'show'])->name('api.logs.show');
});

// Admin-only routes
Route::middleware(['auth', 'admin'])->group(function () {
    Route::resource('users', UserController::class);
});
