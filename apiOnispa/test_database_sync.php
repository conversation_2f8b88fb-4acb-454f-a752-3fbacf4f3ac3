<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Services\ChedSyncService;
use App\Models\ChedCertificate;
use App\Models\ChedSanitaryReference;
use App\Models\ChedProduct;
use App\Models\ChedSyncLog;

echo "=== CHED Database Sync Test ===\n\n";

try {
    // Test 1: Check if tables exist
    echo "1. Checking database tables...\n";
    
    $tables = [
        'ched_certificates',
        'ched_sanitary_references', 
        'ched_products',
        'ched_sync_log'
    ];
    
    foreach ($tables as $table) {
        try {
            DB::table($table)->count();
            echo "   ✓ Table '{$table}' exists\n";
        } catch (Exception $e) {
            echo "   ✗ Table '{$table}' missing - run: php artisan migrate\n";
        }
    }
    
    // Test 2: Check current data
    echo "\n2. Checking current data...\n";
    $certCount = ChedCertificate::count();
    $refCount = ChedSanitaryReference::count();
    $productCount = ChedProduct::count();
    $syncCount = ChedSyncLog::count();
    
    echo "   Certificates: {$certCount}\n";
    echo "   Sanitary References: {$refCount}\n";
    echo "   Products: {$productCount}\n";
    echo "   Sync Logs: {$syncCount}\n";
    
    // Test 3: Show sample data if available
    if ($certCount > 0) {
        echo "\n3. Sample certificate data...\n";
        $sampleCert = ChedCertificate::with(['sanitaryReferences', 'products'])->first();
        echo "   CHED ID: {$sampleCert->ched_id}\n";
        echo "   Type: {$sampleCert->type}\n";
        echo "   Country of Dispatch: {$sampleCert->country_of_dispatch}\n";
        echo "   Sanitary References: " . $sampleCert->sanitaryReferences->count() . "\n";
        echo "   Products: " . $sampleCert->products->count() . "\n";
        
        if ($sampleCert->sanitaryReferences->count() > 0) {
            echo "   Sample Sanitary Reference: " . $sampleCert->sanitaryReferences->first()->sanitary_reference . "\n";
        }
    }
    
    // Test 4: Test search functionality
    echo "\n4. Testing search functionality...\n";
    
    // Test search by CHED ID
    if ($certCount > 0) {
        $sampleChedId = ChedCertificate::first()->ched_id;
        $foundByChedId = ChedCertificate::where('ched_id', $sampleChedId)->first();
        echo "   ✓ Search by CHED ID works: " . ($foundByChedId ? "Found" : "Not found") . "\n";
    }
    
    // Test search by sanitary reference
    if ($refCount > 0) {
        $sampleSanitaryRef = ChedSanitaryReference::first()->sanitary_reference;
        $foundBySanitaryRef = ChedCertificate::findBySanitaryReference($sampleSanitaryRef);
        echo "   ✓ Search by sanitary reference works: " . ($foundBySanitaryRef ? "Found" : "Not found") . "\n";
        
        if ($foundBySanitaryRef) {
            echo "     Sample sanitary reference: {$sampleSanitaryRef}\n";
            echo "     Found CHED ID: {$foundBySanitaryRef->ched_id}\n";
        }
    }
    
    // Test 5: Show sync history
    echo "\n5. Sync history...\n";
    $recentSyncs = ChedSyncLog::orderBy('sync_start_time', 'desc')->limit(3)->get();
    
    if ($recentSyncs->count() > 0) {
        foreach ($recentSyncs as $sync) {
            $status = $sync->status;
            $duration = $sync->sync_end_time ? 
                $sync->sync_start_time->diffInSeconds($sync->sync_end_time) . 's' : 
                'running';
            echo "   {$sync->sync_start_time->format('Y-m-d H:i:s')} - {$status} ({$duration})\n";
            echo "     Certificates: {$sync->total_certificates_fetched}, New: {$sync->new_certificates_added}\n";
        }
    } else {
        echo "   No sync history found. Run: php artisan ched:sync --recent\n";
    }
    
    // Test 6: Recommendations
    echo "\n6. Recommendations...\n";
    
    if ($certCount == 0) {
        echo "   📋 Run initial sync: php artisan ched:sync --recent\n";
    }
    
    $lastSync = ChedSyncLog::getLatestSuccessfulSync();
    if (!$lastSync) {
        echo "   📋 No successful sync found. Run: php artisan ched:sync --recent\n";
    } elseif ($lastSync->sync_end_time->lt(now()->subHours(1))) {
        echo "   ⏰ Last sync was " . $lastSync->sync_end_time->diffForHumans() . "\n";
        echo "   📋 Consider setting up cron job: */10 * * * * php artisan ched:sync --recent\n";
    } else {
        echo "   ✅ Data is up to date (last sync: " . $lastSync->sync_end_time->diffForHumans() . ")\n";
    }
    
    // Test 7: API endpoint test
    echo "\n7. API endpoint test...\n";
    echo "   Test your API with:\n";
    
    if ($refCount > 0) {
        $sampleRef = ChedSanitaryReference::first()->sanitary_reference;
        echo "   curl \"http://localhost:8000/api/certificates/products?reference={$sampleRef}\"\n";
    }
    
    if ($certCount > 0) {
        $sampleChedId = ChedCertificate::first()->ched_id;
        echo "   curl \"http://localhost:8000/api/certificates/products?reference={$sampleChedId}\"\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "✅ Database sync system is ready!\n";
    
    if ($certCount == 0) {
        echo "\n🚀 Next steps:\n";
        echo "1. Run: php artisan ched:sync --recent\n";
        echo "2. Set up cron job for automatic sync\n";
        echo "3. Test API endpoints\n";
    }

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
