DG Sante
Unit G.3

UN/CEFACT WebService for EU-Import retrieve V01
Trade Control and Expert System (TRACES) NT

Date:
Doc. Version:
Doc. Reference:

29/07/2021
1.2
TNT-WS-EUIMPORT-RET-V01

This template is based on PM² V2.5
For the latest version of this template please visit the PM² Wiki

Commission européenne, B-1049 Bruxelles / Europese Commissie, B-1049 Brussel - Belgium. Telephone: (32-2) 299 11 11.
Office: 05/45. Telephone: direct line (32-2) 2999659.
Commission européenne, L-2920 Luxembourg. Telephone: (352) 43 01-1.

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

Document Control Information
Settings
Document Title:
Project Title:
Document Author:
Project Owner:
Project Manager:
Doc. Version:
Sensitivity:
Date:

Value
UN/CEFACT WebService for EU-Import retrieve V01
Trade Control and Expert System (TRACES) NT
Traces Team
DG Sante G.3
SKARINGER Lars, MOESEKE Cedric
1.2
Basic
29/07/2021

Document Approver(s) and Reviewer(s):
NOTE: All Approvers are required. Records of each approver must be maintained. All Reviewers in the list are
considered required unless explicitly listed as Optional.
Name

Role

Action

Date

Document history:
The Document Author is authorized to make the following types of changes to the document without requiring
that the document be re-approved:
 Editorial, formatting, and spelling
 Clarification
To request a change to this document, contact the Document Author or Owner.
Changes to this document are summarized in the following table in reverse chronological order (latest version
first).
Revision Date
Created by
Short Description of Changes
1.0
20/11/2019 AMELIO Francesco
Document Creation
1.1
25/09/2020 AMELO Francesco
Added method getEuImportSignedPdfCertificate to
download the electronically signed PDF (if available)
1.2
20/07/2021 AMELO Francesco
Added methods:
getEuImportCertificate
findEuImportCertificate
Configuration Management: Document Location
The latest version of this document is stored in CIRCA BC.

Page 2 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

TABLE OF CONTENTS
1 INTRODUCTION ...................................................................................................................................4
1.1 Purpose and scope of this document ................................................................................................4
Definitions used in this document .............................................................................................................4
1.2 Standards and versions ......................................................................................................................4
2 AUTHENTICATION ...............................................................................................................................5
2.1 SOAP Headers ....................................................................................................................................5
3 SERVICE DESCRIPTION .........................................................................................................................6
3.1 Endpoints ...........................................................................................................................................6
3.2 WebService methods.........................................................................................................................6
3.2.1 Get EU-Import XML .....................................................................................................................6
3.2.2 Get EU-Import PDF .....................................................................................................................6
3.2.3 Get EU-Import signed PDF ..........................................................................................................6
3.2.4 Find EU-Import............................................................................................................................6
4 RESPONSE ...........................................................................................................................................7
4.1 Operators, authorities, veterinary offices and controlled locations identifiers ................................7
4.1.1 Override default returned identifier ...........................................................................................8
4.2 EU-Import official certification (Part II) .............................................................................................8
4.2.1 Override default format for the official certification ..................................................................9
5 ACCOMPANYING DOCUMENTS DOWNLOAD SERVICE .........................................................................9
5.1 Endpoints .........................................................................................................................................10
6 ERRORS .............................................................................................................................................10
7 APPENDIX: DATA DICTIONARY ..........................................................................................................11
7.1 List of parameters for the find EU-import method .........................................................................11
7.2 Details returned by the find EU-Import methods ............................................................................13
8 APPENDIX: REFERENCES AND RELATED DOCUMENTS ........................................................................15

Page 3 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

1

INTRODUCTION

1.1

Purpose and scope of this document

This document is intended to provide some details for EU-Import retrieval service to connect via Web Services
to the TRACES NT system as well as information on Acceptance and Production environments.
Note:

Please refer to the "TNT–WebServices-authentication" on how to configure the WebService
authentication.

Definitions used in this document
 TRACES NT is the EU information system used to manage veterinary certificates to and from the EU.
 UN/CEFACT is the United Nations Centre for Trade Facilitation and Electronic Business.
 EU-Import is the certificate for import of goods to the EU
 EU-Import Part I refers to the consignment part of the document. Typically submitted by a third country
competent authority.
 EU-Import Part II refers to the certification part of the document.

1.2

Standards and versions

The EU-Import interface uses the
https://www.unece.org/cefact.html.

UN/CEFACT

standards

described

on

the

UNECE

website

The XML Schema of the UN/CEFACT standard could be downloaded on the UNECE website
https://www.unece.org/cefact/xml_schemas/index. The version used for this release is the 17A.
Anyhow, the UN/CEFACT XML Schemas needed for the EU-Import interface are encapsulated into the WSDL
available on the TNT environments, so there is no need to download the UN/CEFACT XML Schemas from the
UNECE website.

Page 4 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

2

AUTHENTICATION

As indicated in the document "TNT–WebServices-Authentication” the SOAP message should include WSSecurity elements and additional other elements in the header part in order for the client to correctly
authenticate with TRACES NT Web Services.
This is a sample header:
<soapenv:Envelope>
<soapenv:Header>
<wsse:Security>
<wsse:UsernameToken wsu:Id="A3380A376973ABE6A6150840400087524">
<wsse:Username>username</wsse:Username>
<wsse:Password
Type="wsse:PasswordDigest">nuQbmp5F6bWH5AlQRLRRxWonQ=</wsse:Password>
<wsse:Nonce>aaa3aSJqORu6x62BdNNczw==</wsse:Nonce>
<wsu:Created>2017-10-19T08:44:51Z</wsu:Created>
</wsse:UsernameToken>
<wsu:Timestamp wsu:Id="TS-A3380A376973ABE6A6150840400087423">
<wsu:Created>2017-10-19T09:10:40.874Z</wsu:Created>
<wsu:Expires>2017-10-19T09:12:40.874Z</wsu:Expires>
</wsu:Timestamp>
</wsse:Security>
<v3:LanguageCode>en</v3:LanguageCode>
<v3:WebServiceClientId>clientId</v3:WebServiceClientId>
</soap:Header>
<soap:Body>
...
</soap:Body>
</soap:Envelope>

2.1

SOAP Headers

There is no extra SOAP header needed to retrieve or find an EU-Import document. Anyhow the optional
LanguageCode header can be used if the client wants to have the translatable text or PDF in another language
other than English.

Page 5 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

3

SERVICE DESCRIPTION

This Web Service will be used by operators, competent authorities or responsible for load to retrieve a EU-Import
certificate. The client can retrieve the EU-Import certificate in PDF format.

3.1

Endpoints

Below the endpoints for the EU-Import Web service available in TRACES NT:
Environment

Endpoint

Acceptance

https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01?wsdl

Production

https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01?wsdl

3.2

WebService methods

The following web methods are available in the interface

3.2.1 Get EU-Import XML
Method name: getEuImportCertificate
Description: Retrieves the XML of Part I (details of dispatched consignment) and Part II (Certification) of an EUImport certificate.
Input: EU-Import TNT Reference number.
Output: The EU-import certificate in the SPSCertificate format.
Faults: EuImportCertificateNotFoundException, EuImportCertificatePermissionDeniedException

3.2.2 Get EU-Import PDF
Method name: getEuImportPdfCertificate
Description: Retrieves the PDF of Part I (details of dispatched consignment) and Part II (Certification) of an EUImport certificate.
Input: EU-Import TNT Reference number and a List of ExtraLanguageCode to generate the PDF in more than one
language. The first language code will be always the one set in the SOAP Header.
Output: The PDF file in form of binary object (in Base64 format).
Faults: EuImportCertificateNotFoundException, EuImportCertificatePermissionDeniedException,
IllegalLanguageCodeException

3.2.3 Get EU-Import signed PDF
Method name: getEuImportSignedPdfCertificate
Description: Retrieves the electronically signed PDF of an EU-Import document.
Input: EU-Import TNT Reference
Output: The PDF file in form of binary object (in Base64 format).
Faults: EuImportCertificateNotFoundException, EuImportCertificatePermissionDeniedException,
EuImportSignedPdfNotAvailableException

3.2.4 Find EU-Import
Method name: findEuImportCertificate
Description: Search for EU-Import certificates using a set of criteria
Input: a set of criteria (See appendix “List of parameters for the find” for more info).

Page 6 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

Output: A subset of the basic EU-Import information (See appendix: “Details returned by the find EU-Import”
for more info).
Faults: IllegalFindImportCertificateException

4

RESPONSE

If the request is successfully processed, the system will reply with a SPSCertificate element. The XML will be
returned with elements according to the mapping described in the document “UNCEFACT_Mappings_TNT.IMPORT”.

4.1

Operators, authorities, veterinary offices and controlled locations identifiers

All operators (i.e. a Responsible for load, Exporter, Importer), Authorities (i.e. the Border control post) and
Controlled locations (i.e. control points, onward transportation facilities etc) are mapped into two types of
elements: SPSPartyType and SPSLocationType as in the following examples.
The type of ID is specified in the schemeID attribute.
<ConsigneeSPSParty>
<ID schemeID="eori_number">FR12345678</ID>
<Name/>
<SpecifiedSPSAddress>
<CountryID>FR</CountryID>
</SpecifiedSPSAddress>
</ConsigneeSPSParty>

Code 1 Reference to a Consignee using the EORI Number “FR12345678”
<ActivityAuthorizedSPSParty>
<ID schemeID="controlled_location_id">892245</ID>
<Name/>
<SpecifiedSPSAddress>
<CountryID>DK</CountryID>
</SpecifiedSPSAddress>
</ActivityAuthorizedSPSParty>

Code 2 Reference to a Controlled location using the controlled location id “892245”
<UnloadingBaseportSPSLocation>
<ID schemeID="un_locode">BEBRU</ID>
<Name>BE</Name>
</UnloadingBaseportSPSLocation>

Code 3 Example of a Border control post using the UN Locode ”BEBRU”
The system will normally returns the first available ID type (specified in the schemeID attribute), depending on
the type of entity, following the order specified in the table below:
Table 1 Order of returned identifiers by type
ID.schemeID
Operators

operator_activity_id
eori_number
national_registry_number
vat
operator_internal_activity_id

Border control posts

un_locode
authority_activity_id

Authority activities

authority_activity_id
un_locode

Veterinary offices

veterinary_office_id
veterinary_office_internal_id

Page 7 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

Controlled locations

controlled_location_id
un_locode

4.1.1 Override default returned identifier
It is possible to override the priority of the returned identifier and force the system to return a specific type of
identifier by setting a list of attributes in the SOAP Header.
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
xmlns:v4="http://ec.europa.eu/sanco/tracesnt/base/v4" xmlns:oas="http://docs.oasisopen.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
xmlns:v1="http://ec.europa.eu/tracesnt/certificate/import/v01">
<soapenv:Header>
<v3:LanguageCode>en</v3:LanguageCode>
<v3:WebServiceClientId>ws-client-id</v3:WebServiceClientId>
<v3:Attributes>
<v3:Attribute key="operator_activity_preferred_scheme_id">eori_number</v3:Attribute>
<v3:Attribute key="bcp_preferred_scheme_id">authority_activity_id</v3:Attribute>
<v3:Attribute key="authority_activity_preferred_scheme_id">un_locode</v3:Attribute>
<v3:Attribute key="controlled_location_preferred_scheme_id">un_locode</v3:Attribute>
</v3:Attributes>
<oas:Security/>
</soapenv:Header>
<soapenv:Body>
<v1:GetEuImportCertificateRequest>
<v1:ID>IMPORT.EU.IT.2020.1000091</v1:ID>
</v1:GetEuImportCertificateRequest>
</soapenv:Body>
</soapenv:Envelope>

Code 4 Example of attributes to override the default returned identifiers
The following tables described the attribute key and the possible values
Attribute.key

Attribute possible values

Operators

operator_activity_preferred_scheme_id

operator_activity_id
eori_number
national_registry_number
vat
operator_internal_activity_id

Border
control
posts

bcp_preferred_scheme_id

un_locode
authority_activity_id

Authority
activities

authority_activity_preferred_scheme_id

authority_activity_id
un_locode

Controlled
locations

controlled_location_preferred_scheme_id

controlled_location_id
un_locode

4.2

EU-Import official certification (Part II)

The official certification statements are by default returned in the plain-text Markdown format. The full text is
returned in the element SPSCertificate / SPSExchangedDocument / SignatorySPSAuthentication /
IncludedSPSClause with ID = “CERTIFICATION_CLAUSES” as in the following exemple.
<SignatorySPSAuthentication>
<TypeCode name="Clearance">1</TypeCode>
...
<IncludedSPSClause>
<ID schemeID="eu_import_certification_clause">CERTIFICATION_CLAUSES</ID>
<Content languageID="en">(1)either - [x] [I, the undersigned official veterinarian,
hereby certify, that all applicable provisions of Directive 64/432/EEC are fulfilled and
that in particular the animals described in Part I meet the following requirements:]
(1)(2)or - [ ] ~~[Based on the information provided either in an official document or a
certificate in which Sections A and B were completed by the official veterinarian or the
approved veterinarian responsible for the holding of origin, I, the undersigned official

Page 8 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

veterinarian, hereby certify, that all applicable provisions of Directive 64/432/EEC are
fulfilled and that in particular the animals described in Part I meet the following
requirements:]~~
II.1. Section A
II.1.1. The animals come from holding/s of origin and area/s which, in conformity with
Union or national legislation, is/are not subject to any prohibition or restriction for
reasons of animal diseases affecting bovine animals. …. </Content>
</IncludedSPSClause>
...

</SignatorySPSAuthentication>
Code 5 Example official certification.

4.2.1 Override default format for the official certification
It is possible to override the format of the official certification by setting a specific attribute in the SOAP Header.
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
xmlns:v4="http://ec.europa.eu/sanco/tracesnt/base/v4" xmlns:oas="http://docs.oasisopen.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
xmlns:v1="http://ec.europa.eu/tracesnt/certificate/import/v01">
<soapenv:Header>
<v3:LanguageCode>en</v3:LanguageCode>
<v3:WebServiceClientId>ws-client-id</v3:WebServiceClientId>
<v3:Attributes>
<v3:Attribute key="official_form_output_format">HTML5</v3:Attribute>
</v3:Attributes>
<oas:Security/>
</soapenv:Header>
<soapenv:Body>
<v1:GetEuImportCertificateRequest>
<v1:ID>IMPORT.EU.IT.2020.1000091</v1:ID>
</v1:GetEuImportCertificateRequest>
</soapenv:Body>
</soapenv:Envelope>

Code 6 Example of attributes to override the default returned identifiers
The following tables described the attribute key and the possible values:
Attribute.key

Attribute possible values

official_form_output_format

MARKDOWN (default)
HTML5

5

ACCOMPANYING DOCUMENTS DOWNLOAD SERVICE

The accompanying documents attached to an IMPORT are mapped to the element SPSCertificate /
SPSExchangedDocument / ReferenceSPSReferencedDocument. This element will never contain a binary content
but only meta-data of the attachment.
The accompanying document download web service can be used to retrieve binary attachments.
In order to download the attachment, the client will need to specify the following parameters:
-

EU-Import reference number
Filename
Document ID

Given an IMPORT with reference number IMPORT.EU.IT.2019.0000001, containing an accompanying document
with filename "FV-000262.pdf" and the documentId “123456”:
<ReferenceSPSReferencedDocument>
<IssueDateTime>2016-12-01T00:00:00.000+01:00</IssueDateTime>
<TypeCode name="Dangerous goods declaration">890</TypeCode>
<RelationshipTypeCode name="Mutually defined reference number (Supporting
document)">ZZZ</RelationshipTypeCode>
<ID schemeAgencyID="PK">LT-123898</ID>
<AttachmentBinaryObject uri="uri:documentid:123456" filename="FV-000262.pdf"/>

Page 9 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

<Information>Brussels</Information>
</ReferenceSPSReferencedDocument>

The request to retrieve the attachment will look like this:
<GetCertificateAttachmentRequest>
<CertificateReference>IMPORT.EU.IT.2019.0000001</CertificateReference>
<FileName>FV-000262.pdf</FileName>
<DocumentId>123456</DocumentId>
<GetCertificateAttachmentRequest>

The endpoint is using SOAP 1.2, and supports the MTOM with XOP attachments. In order to enable the binary
transmission optimization the client will need to add an additional HTTP header “Accept: application/xop+xml;”

5.1

Endpoints

Below the endpoints for the Certificate attachments web service available in TRACES NT:

6

Environment

Endpoint

Acceptance

https://webgate.acceptance.ec.europa.eu/tracesnt/ws/CertificateAttachmentsServiceV1?wsdl

Production

https://webgate.ec.europa.eu/tracesnt/ws/CertificateAttachmentsServiceV1?wsdl

ERRORS

In case of permission denied or EU-Import not found errors, the system will always return a SOAP Fault.





EuImportCertificateNotFoundException
Fault returned when the system cannot find the certificate specified.
EuImportCertificatePermissionDeniedException
Fault returned in case the user does not have the sufficient permissions to retrieve the data.
IllegalLanguageCodeException
In case the language code specified in the get PDF is not valid
EuImportSignedPdfNotAvailableException
In case the EU-Import certificate exists, but there is no electronically signed PDF available.

Page 10 of 15

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

7

APPENDIX: DATA DICTIONARY

7.1

List of parameters for the find EU-import method

Name

Content

Cardinality

Note

pageSize

Integer

1..1

pageSize > 1 and <= 200
Number of results returned in a page.
The client should stop requesting new pages when the
results retrieved in the page are less than the pageSize
specified.

offset

Integer

1..1

offset > 0
Number of results to be skipped. To implement pagination,
and retrieve the next page, set an offset as the pageSize
multiplied by the number of page to be retrieved.

Status

47

Draft

1

New

42

In progress

70

Validated

41

Rejected

55
64

Deleted
Cancelled

44

Replaced

0..20

LocalID

String (80)

0..1

Field I.21a

CNCode

String (regular expression: [0-9][0-9]){1,10})

0..10

CN Code (See I.25).

CNCode.exactMatch

Boolean (true,false)

0..1

If set to true, the search will return only EU-Import with exact
match of the commodity’s CN Code. If the attribute is not

Date: 29/07/2021

11 / 15

Version: 1.2

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

specified or set to false, the search is performed wildcard-wise
(starts with).
CreateDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of physical creation in the DB

UpdateDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the last update to the EU-Import. The update
can concern Part I, Part II, any status change, any laboratory
test and control.

StatusChangeDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the last change of status (i.e. the EU-Import
changed from “In progress” to “Validated”)

DeclarationDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the declaration of the Part I (consignment)

CertificationDateTimeRange

The couple (From, To) as DateTime

0..1

Date and time of the signature of the Part II (certification)

CentralCompetentAuthorityActivityCode

String

0..1

Central competent authority activity code or UN/Locode of
the Box I.3. The search is performed with an approximate
string matching (fuzzy) so it might find codes that are not 100%
matching the parameter specified.

LocalCompetentAuthorityActivityCode

String

0..1

Local competent authority activity code or UN/Locode of the
Box I.4. The search is performed with an approximate string
matching (fuzzy) so it might find codes that are not 100%
matching the parameter specified.

CountryOfIssuance

Country code (ISO 3166-1 alpha-2)

0..1

Country where the certificate have been validated (issued). Is
the country of the authority that signed the Part II.
The field is searchable only when the EU-Import certification
is signed.

CountryOfConsignor

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the country specified in field I.1

CountryOfConsignee

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the country specified in field I.5

CountryOfOrigin

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the countries specified in field I.25

CountryOfDestination

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the country specified in field I.9

Date: 29/07/2021

12 / 15

Version: 1.2

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

CountryOfDispatch

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the country specified in field I.11

CountryOfPlaceOfLoading

Country code (ISO 3166-1 alpha-2)

0..1

Searches in the country specified in field I.13

CountryOfTransit

Country code (ISO 3166-1 alpha-2)

0..1

Searches in countries specified in fields I.21

7.2

Details returned by the find EU-Import methods

Name

Content

Cardinality

Note

pageSize

Integer

1..1

Number of results returned in a current page.
The client should stop requesting new pages when the
results retrieved in the page are less than the pageSize
specified.

offset

Integer

1..1

Number of results skipped.

ID

String(30)

1..1

EU-Import ID (i.e. IMPORT.EU.FR.2021.0500612)

Status

47

Draft

1..1

1

New

42

In progress

70

Validated

41

Rejected

55
64

Deleted
Cancelled

44

Replaced

HasControls

Boolean (true)

0..1

LocalID

String (80)

0..1

Field I.2a

CommodityApplicableSPSClassification

List of SPSClassificationType (see paragraph Error!
Reference source not found.)

1..n

List of commodity classifications.
Only SPSClassificationType with SystemID = ‘CN’ will be

Date: 29/07/2021

13 / 15

Version: 1.2

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

returned.
For the format see the document: UNCEFACT_Mappings_TNT.IMPORT
CreateDateTime

Date time

1..1

UpdateDateTime

Date time

1..1

StatusChangeDateTime

Date time

0..1

DeclarationDateTime

Date time

0..1

CertificationDateTime

Date time

0..1

CentralCompetentAuthorityCode

String (40)

0..1

CentralCompetentAuthorityUnLocode

String (5)

0..1

LocalCompetentAuthorityCode

String (40)

0..1

LocalCompetentAuthorityUnLocode

String (5)

0..1

CountryOfIssuance

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfConsignor

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfConsignee

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfOrigin

List of Country code (ISO 3166-1 alpha-2)

0..n

CountryOfDestination

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfDispatch

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfPlaceOfLoading

Country code (ISO 3166-1 alpha-2)

0..1

CountryOfTransit

List of Country code (ISO 3166-1 alpha-2)

0..n

ConsignorName

String (200)

0..1

ConsigneeName

String (200)

0..1

ReferenceSPSReferencedDocument

List of SPSReferencedDocumentType

0..n

Date: 29/07/2021

14 / 15

Version: 1.2

List of references to other documents and certificates,
including the references to replaced EU-Import and to

Trade Control and Expert System (TRACES) NT UN/CEFACT WebService for EU-Import retrieve V01

other related EU-Import and the list of accompanying
documents. For the format see paragraph Error!
Reference source not found. and the document: UNCEFACT_Mappings_TNT.IMPORT

APPENDIX: REFERENCES AND RELATED DOCUMENTS

8
ID

Reference or Related Document

Source or Link/Location

TNT-WS-AUTH

TNT-WebServices-Authentication.docx

CIRCA-BC

TNT-WS-SOAPUI

TNT-SoapUI-WS-Security-Configuration.docx

CIRCA-BC

Date: 29/07/2021

15 / 15

Version: 1.2

