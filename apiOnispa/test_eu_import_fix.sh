#!/bin/bash

echo "Testing EU-Import API with Namespace Fix"
echo "========================================"
echo ""

# Test the fetch endpoint with the reference that was failing
echo "Testing fetch endpoint with reference: IMPORT.EU.MR.2025.0003940"
echo "----------------------------------------------------------------"

curl -X POST http://localhost:8000/api/eu-import/fetch \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -d '{
    "reference": "IMPORT.EU.MR.2025.0003940",
    "save_xml": true,
    "force_refresh": true
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo ""
echo "Check the Laravel logs for any errors:"
echo "tail -f storage/logs/laravel-$(date +%Y-%m-%d).log"
