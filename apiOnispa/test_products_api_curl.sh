#!/bin/bash

# Test script for Certificate Products API using curl
# Usage: ./test_products_api_curl.sh [base_url]

BASE_URL=${1:-"http://localhost:8000"}
API_ENDPOINT="$BASE_URL/api/certificates/products"

echo "=== Certificate Products API Test (curl) ==="
echo "Base URL: $BASE_URL"
echo "API Endpoint: $API_ENDPOINT"
echo "Test started at: $(date)"
echo ""

# Test 1: Valid certificate reference
echo "TEST 1: Valid Certificate Reference"
echo "----------------------------------------"
VALID_REFS=("CHEDP.ES.2025.0044891" "CHEDPP.IT.2020.1000091" "CHEDP.MR.2025.0000001")

for ref in "${VALID_REFS[@]}"; do
    echo "Testing reference: $ref"
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -H "Accept: application/json" \
        -H "User-Agent: CertificateProductsApiTester-curl/1.0" \
        "$API_ENDPOINT?reference=$ref")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    json_response=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Status: $http_code"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ SUCCESS: API responded with 200"
        
        # Parse JSON response (basic parsing)
        success=$(echo "$json_response" | grep -o '"success":[^,]*' | cut -d: -f2)
        products_count=$(echo "$json_response" | grep -o '"products_count":[^,]*' | cut -d: -f2)
        source=$(echo "$json_response" | grep -o '"source":"[^"]*"' | cut -d: -f2 | tr -d '"')
        
        echo "   - Success: $success"
        echo "   - Products count: $products_count"
        echo "   - Source: $source"
        
        # Pretty print JSON if jq is available
        if command -v jq &> /dev/null; then
            echo "   - Full response (formatted):"
            echo "$json_response" | jq '.'
        else
            echo "   - Raw response:"
            echo "$json_response"
        fi
    else
        echo "❌ FAILED: HTTP $http_code"
        echo "Response: $json_response"
    fi
    
    echo ""
done

# Test 2: Invalid certificate reference
echo "TEST 2: Invalid Certificate Reference"
echo "----------------------------------------"
INVALID_REFS=("INVALID-REF" "CHED.IT.2020.123" "CHEDPP.ITALY.2020.123" "")

for ref in "${INVALID_REFS[@]}"; do
    echo "Testing invalid reference: '$ref'"
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -H "Accept: application/json" \
        -H "User-Agent: CertificateProductsApiTester-curl/1.0" \
        "$API_ENDPOINT?reference=$ref")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
    json_response=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    echo "HTTP Status: $http_code"
    
    if [ "$http_code" = "400" ]; then
        echo "✅ SUCCESS: Correctly rejected with 400"
        
        # Extract error message
        message=$(echo "$json_response" | grep -o '"message":"[^"]*"' | cut -d: -f2 | tr -d '"')
        echo "   - Message: $message"
    else
        echo "❌ FAILED: Expected 400, got $http_code"
        echo "Response: $json_response"
    fi
    
    echo ""
done

# Test 3: Missing reference parameter
echo "TEST 3: Missing Reference Parameter"
echo "----------------------------------------"

response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -H "Accept: application/json" \
    -H "User-Agent: CertificateProductsApiTester-curl/1.0" \
    "$API_ENDPOINT")

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
json_response=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "HTTP Status: $http_code"

if [ "$http_code" = "400" ]; then
    echo "✅ SUCCESS: Correctly handled missing parameter with 400"
    
    # Extract error message
    message=$(echo "$json_response" | grep -o '"message":"[^"]*"' | cut -d: -f2 | tr -d '"')
    echo "   - Message: $message"
else
    echo "❌ FAILED: Expected 400, got $http_code"
    echo "Response: $json_response"
fi

echo ""

# Test 4: API Documentation
echo "TEST 4: API Documentation"
echo "----------------------------------------"
echo "Endpoint: GET $API_ENDPOINT"
echo ""
echo "Parameters:"
echo "  - reference (required): CHED certificate reference number"
echo "    Format: CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]"
echo "    Examples: CHEDPP.IT.2020.1000091, CHEDP.MR.2025.0000001"
echo ""
echo "Example usage:"
echo "  curl -H 'Accept: application/json' '$API_ENDPOINT?reference=CHEDPP.IT.2020.1000091'"
echo ""
echo "Response codes:"
echo "  - 200: Success - products retrieved"
echo "  - 400: Bad Request - invalid reference format or missing parameter"
echo "  - 401: Unauthorized - authentication error with TRACES API"
echo "  - 403: Forbidden - access denied to certificate"
echo "  - 404: Not Found - certificate not found"
echo "  - 500: Internal Server Error - configuration missing or retrieval error"
echo ""

echo "=== Test Suite Completed ==="
echo "Completed at: $(date)"
