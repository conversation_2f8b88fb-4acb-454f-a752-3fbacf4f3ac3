<?php

require_once 'vendor/autoload.php';

use App\Providers\TracesNtChedClient;

// Simple test script for direct certificate URL access
// This tests accessing the TRACES NT certificate web interface directly

echo "=== TRACES NT Direct Certificate URL Test ===\n\n";

try {
    // Initialize the client
    $client = new TracesNtChedClient(
        'n00385tm',
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',
        'onispa-mr',
        true  // Use production
    );

    // Test different sanitary references
    $testReferences = [
        'IMPORT.EU.MR.2025.0003940',
        'IMPORT.EU.MR.2025.0000007',
        'IMPORT.EU.MR.2025.0003344'
    ];

    foreach ($testReferences as $reference) {
        echo "Testing direct URL access for: {$reference}\n";
        echo str_repeat('-', 60) . "\n";
        
        try {
            $result = $client->testDirectCertificateUrl($reference);
            
            echo "URL: {$result['url']}\n";
            echo "HTTP Status: {$result['http_code']}\n";
            echo "Content Type: {$result['content_type']}\n";
            echo "Response Size: {$result['response_size']} bytes\n";
            echo "Requires Authentication: " . ($result['requires_authentication'] ? 'YES' : 'NO') . "\n";
            echo "Contains Certificate Data: " . ($result['contains_certificate_data'] ? 'YES' : 'NO') . "\n";
            
            // Analyze the response
            if ($result['http_code'] === 200) {
                echo "✓ SUCCESS: Direct access worked!\n";
                
                // Look for specific content in the response
                $preview = strtolower($result['response_preview']);
                if (strpos($preview, 'certificate') !== false) {
                    echo "✓ Response contains certificate information\n";
                }
                if (strpos($preview, 'ched') !== false) {
                    echo "✓ Response contains CHED information\n";
                }
                if (strpos($preview, 'json') !== false) {
                    echo "✓ Response might be JSON format\n";
                }
                if (strpos($preview, 'xml') !== false) {
                    echo "✓ Response might be XML format\n";
                }
                
            } elseif ($result['http_code'] === 302 || $result['http_code'] === 301) {
                echo "⚠ REDIRECT: Server redirected the request\n";
            } elseif ($result['http_code'] === 401 || $result['http_code'] === 403) {
                echo "⚠ AUTHENTICATION: Access requires authentication\n";
            } elseif ($result['http_code'] === 404) {
                echo "⚠ NOT FOUND: Certificate not found at this URL\n";
            } else {
                echo "⚠ UNEXPECTED: HTTP {$result['http_code']}\n";
            }
            
            echo "\nResponse Preview (first 300 chars):\n";
            echo str_repeat('-', 40) . "\n";
            echo substr($result['response_preview'], 0, 300) . "\n";
            echo str_repeat('-', 40) . "\n";
            
        } catch (Exception $e) {
            echo "✗ ERROR: " . $e->getMessage() . "\n";
        }
        
        echo "\n" . str_repeat('=', 80) . "\n\n";
    }

    echo "=== Alternative URL Patterns Test ===\n\n";
    
    // Test different URL patterns that might work
    $baseReference = 'IMPORT.EU.MR.2025.0003940';
    $urlPatterns = [
        "https://webgate.ec.europa.eu/tracesnt/certificate/eu-import/{$baseReference}",
        "https://webgate.ec.europa.eu/tracesnt/certificate/{$baseReference}",
        "https://webgate.ec.europa.eu/tracesnt/api/certificate/{$baseReference}",
        "https://webgate.ec.europa.eu/tracesnt/rest/certificate/{$baseReference}",
        "https://webgate.ec.europa.eu/tracesnt/certificate/import/{$baseReference}",
    ];

    foreach ($urlPatterns as $url) {
        echo "Testing URL pattern: {$url}\n";
        
        try {
            $ch = curl_init($url);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_SSL_VERIFYHOST => 2,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_USERAGENT => 'ONISPA-Laravel-Client/1.0',
                CURLOPT_HTTPHEADER => [
                    'Accept: application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language: en-US,en;q=0.5',
                ]
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            curl_close($ch);

            echo "  HTTP: {$httpCode} | Content-Type: {$contentType} | Size: " . strlen($response) . " bytes\n";
            
            if ($httpCode === 200) {
                echo "  ✓ SUCCESS!\n";
                $preview = substr($response, 0, 200);
                echo "  Preview: " . str_replace(["\n", "\r", "\t"], " ", $preview) . "...\n";
            }
            
        } catch (Exception $e) {
            echo "  ✗ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    echo "\n=== Summary ===\n";
    echo "This test explores different ways to access TRACES NT certificate information:\n";
    echo "1. Direct web interface URLs\n";
    echo "2. Alternative URL patterns\n";
    echo "3. Different content type requests\n\n";
    echo "Check storage/logs/direct_certificate_response.html for the full response content.\n";

} catch (Exception $e) {
    echo "✗ Test failed: " . $e->getMessage() . "\n";
}
