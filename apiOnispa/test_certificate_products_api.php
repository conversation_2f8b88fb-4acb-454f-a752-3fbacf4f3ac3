<?php

/**
 * Test script for the Certificate Products API endpoint
 * 
 * This script tests the new API endpoint that retrieves all products
 * linked to a certificate by its reference number.
 * 
 * Usage: php test_certificate_products_api.php
 */

require_once __DIR__ . '/vendor/autoload.php';

class CertificateProductsApiTester
{
    private $baseUrl;
    private $testReferences;

    public function __construct($baseUrl = 'http://localhost:8000')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        
        // Test certificate references (replace with actual ones from your system)
        $this->testReferences = [
            'CHEDP.ES.2025.0044891',
            'CHEDPP.IT.2020.1000091',
            'CHEDP.MR.2025.0000001'
        ];
    }

    /**
     * Run all tests
     */
    public function runTests()
    {
        echo "=== Certificate Products API Test Suite ===\n\n";
        echo "Base URL: {$this->baseUrl}\n";
        echo "Test started at: " . date('Y-m-d H:i:s') . "\n\n";

        $this->testValidCertificateReference();
        $this->testInvalidCertificateReference();
        $this->testMissingReference();
        $this->testMultipleCertificates();

        echo "\n=== Test Suite Completed ===\n";
    }

    /**
     * Test with valid certificate reference
     */
    private function testValidCertificateReference()
    {
        echo "TEST 1: Valid Certificate Reference\n";
        echo str_repeat('-', 50) . "\n";

        foreach ($this->testReferences as $reference) {
            echo "Testing reference: {$reference}\n";
            
            $url = $this->baseUrl . '/api/certificates/products?reference=' . urlencode($reference);
            $response = $this->makeRequest($url);
            
            if ($response) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['success'])) {
                    if ($data['success']) {
                        echo "✅ SUCCESS: Certificate products retrieved\n";
                        echo "   - Products count: " . ($data['products_count'] ?? 0) . "\n";
                        echo "   - Source: " . ($data['source'] ?? 'unknown') . "\n";
                        
                        if (!empty($data['products'])) {
                            echo "   - First product details:\n";
                            $firstProduct = $data['products'][0];
                            echo "     * Sequence: " . ($firstProduct['sequence'] ?? 'N/A') . "\n";
                            echo "     * System: " . ($firstProduct['system_id'] ?? 'N/A') . "\n";
                            echo "     * Code: " . ($firstProduct['classification_code'] ?? 'N/A') . "\n";
                            echo "     * Description: " . ($firstProduct['description'] ?? 'N/A') . "\n";
                        }
                    } else {
                        echo "❌ FAILED: " . ($data['message'] ?? 'Unknown error') . "\n";
                        echo "   - Error code: " . ($data['error_code'] ?? 'N/A') . "\n";
                    }
                } else {
                    echo "❌ FAILED: Invalid JSON response\n";
                }
            } else {
                echo "❌ FAILED: No response received\n";
            }
            
            echo "\n";
        }
    }

    /**
     * Test with invalid certificate reference format
     */
    private function testInvalidCertificateReference()
    {
        echo "TEST 2: Invalid Certificate Reference Format\n";
        echo str_repeat('-', 50) . "\n";

        $invalidReferences = [
            'INVALID-REF',
            'CHED.IT.2020.123',
            'CHEDPP.ITALY.2020.123',
            'CHEDPP.IT.20.123',
            ''
        ];

        foreach ($invalidReferences as $reference) {
            echo "Testing invalid reference: '{$reference}'\n";
            
            $url = $this->baseUrl . '/api/certificates/products?reference=' . urlencode($reference);
            $response = $this->makeRequest($url);
            
            if ($response) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['success']) && !$data['success']) {
                    echo "✅ SUCCESS: Correctly rejected invalid format\n";
                    echo "   - Message: " . ($data['message'] ?? 'N/A') . "\n";
                } else {
                    echo "❌ FAILED: Should have rejected invalid format\n";
                }
            } else {
                echo "❌ FAILED: No response received\n";
            }
            
            echo "\n";
        }
    }

    /**
     * Test with missing reference parameter
     */
    private function testMissingReference()
    {
        echo "TEST 3: Missing Reference Parameter\n";
        echo str_repeat('-', 50) . "\n";

        $url = $this->baseUrl . '/api/certificates/products';
        $response = $this->makeRequest($url);
        
        if ($response) {
            $data = json_decode($response, true);
            
            if ($data && isset($data['success']) && !$data['success']) {
                echo "✅ SUCCESS: Correctly handled missing parameter\n";
                echo "   - Message: " . ($data['message'] ?? 'N/A') . "\n";
            } else {
                echo "❌ FAILED: Should have required reference parameter\n";
            }
        } else {
            echo "❌ FAILED: No response received\n";
        }
        
        echo "\n";
    }

    /**
     * Test multiple certificates to check consistency
     */
    private function testMultipleCertificates()
    {
        echo "TEST 4: Multiple Certificates Consistency\n";
        echo str_repeat('-', 50) . "\n";

        $results = [];
        
        foreach ($this->testReferences as $reference) {
            $url = $this->baseUrl . '/api/certificates/products?reference=' . urlencode($reference);
            $response = $this->makeRequest($url);
            
            if ($response) {
                $data = json_decode($response, true);
                $results[] = [
                    'reference' => $reference,
                    'success' => $data['success'] ?? false,
                    'products_count' => $data['products_count'] ?? 0,
                    'source' => $data['source'] ?? 'unknown'
                ];
            }
        }

        echo "Summary of all test certificates:\n";
        foreach ($results as $result) {
            $status = $result['success'] ? '✅' : '❌';
            echo "{$status} {$result['reference']}: {$result['products_count']} products ({$result['source']})\n";
        }
        
        echo "\n";
    }

    /**
     * Make HTTP request to the API
     */
    private function makeRequest($url)
    {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'Accept: application/json',
                    'User-Agent: CertificateProductsApiTester/1.0'
                ],
                'timeout' => 30
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            echo "Request failed: " . ($error['message'] ?? 'Unknown error') . "\n";
            return null;
        }

        return $response;
    }

    /**
     * Display API endpoint documentation
     */
    public function showDocumentation()
    {
        echo "=== Certificate Products API Documentation ===\n\n";
        
        echo "Endpoint: GET /api/certificates/products\n\n";
        
        echo "Parameters:\n";
        echo "  - reference (required): CHED certificate reference number\n";
        echo "    Format: CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]\n";
        echo "    Examples: CHEDPP.IT.2020.1000091, CHEDP.MR.2025.0000001\n\n";
        
        echo "Response Format:\n";
        echo "{\n";
        echo "  \"success\": true,\n";
        echo "  \"certificate_reference\": \"CHEDPP.IT.2020.1000091\",\n";
        echo "  \"source\": \"traces_api|local_database\",\n";
        echo "  \"products_count\": 2,\n";
        echo "  \"products\": [\n";
        echo "    {\n";
        echo "      \"sequence\": 1,\n";
        echo "      \"system_id\": \"CN\",\n";
        echo "      \"system_name\": \"CN Code (Combined Nomenclature)\",\n";
        echo "      \"classification_code\": \"0908\",\n";
        echo "      \"description\": \"Hazelnuts or filberts (Corylus spp.)\",\n";
        echo "      \"descriptions\": {\n";
        echo "        \"en\": \"Hazelnuts or filberts (Corylus spp.)\",\n";
        echo "        \"fr\": \"Noisettes (Corylus spp.)\"\n";
        echo "      },\n";
        echo "      \"source\": \"traces_api\"\n";
        echo "    }\n";
        echo "  ]\n";
        echo "}\n\n";
        
        echo "Error Response Format:\n";
        echo "{\n";
        echo "  \"success\": false,\n";
        echo "  \"message\": \"Error description\",\n";
        echo "  \"error_code\": \"ERROR_CODE\"\n";
        echo "}\n\n";
        
        echo "Error Codes:\n";
        echo "  - CERTIFICATE_NOT_FOUND: Certificate reference not found\n";
        echo "  - PERMISSION_DENIED: Access denied to certificate\n";
        echo "  - AUTHENTICATION_ERROR: TRACES API authentication failed\n";
        echo "  - CONFIGURATION_MISSING: API configuration not set up\n";
        echo "  - RETRIEVAL_ERROR: Generic retrieval error\n\n";
    }
}

// Run the tests if script is executed directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $tester = new CertificateProductsApiTester();
    
    // Show documentation first
    $tester->showDocumentation();
    
    // Run the tests
    $tester->runTests();
}
