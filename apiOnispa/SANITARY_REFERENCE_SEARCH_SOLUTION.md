# Solution: Search CHED Certificates by Sanitary Reference

## Problem
The original `getChedCertificateByReference` method was not working for searching by sanitary certificate references like "IMPORT.EU.MR.2025.0000007". This is because:

1. The method was designed to search by CHED certificate ID (e.g., "CHEDP.FR.2025.0000038")
2. Sanitary certificate references appear in the `references` array within CHED certificates
3. The TRACES API doesn't have a direct search by sanitary certificate reference

## Solution Implemented

### 1. Fixed the Original Method
- **Fixed**: Changed `<ched:LocalReference>` to `<ched:ID>` in the SOAP request
- **File**: `app/Providers/TracesNtChedClient.php` line 44

### 2. Added New Method: `findChedCertificatesBySanitaryReference()`
This method searches for CHED certificates that contain a specific sanitary certificate reference.

**Parameters:**
- `$sanitaryReference` - The sanitary certificate reference (e.g., "IMPORT.EU.MR.2025.0000007")
- `$countryOfDispatch` - Optional country filter (e.g., "MR")
- `$startDate` - Optional start date (YYYY-MM-DD)
- `$endDate` - Optional end date (YYYY-MM-DD)

**How it works:**
1. Uses `findChedCertificate` API to get CHED certificates within date range
2. Filters results to find certificates containing the sanitary reference
3. Returns array of matching CHED certificates

### 3. Added Smart Method: `getCertificateByAnyReference()`
This method automatically detects the reference type and uses the appropriate search method.

**Auto-detection logic:**
- CHED ID pattern: `CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]` → Uses direct lookup
- Sanitary reference pattern: `IMPORT.EU.[COUNTRY].[YEAR].[NUMBER]` → Uses search
- Unknown pattern: Tries both methods

### 4. Updated Controller
The `CertificateController::getCertificateProducts()` method now uses the smart detection method.

## Usage Examples

### Method 1: Direct Sanitary Reference Search
```php
use App\Providers\TracesNtChedClient;

$client = new TracesNtChedClient($username, $authKey, $clientId, true);

$certificates = $client->findChedCertificatesBySanitaryReference(
    'IMPORT.EU.MR.2025.0000007',  // Sanitary reference
    'MR',                         // Country filter
    '2025-01-01',                 // Start date
    '2025-01-03'                  // End date
);

foreach ($certificates as $certificate) {
    echo "Found CHED: " . $certificate['id'] . "\n";
    // Access references array to see sanitary certificates
    foreach ($certificate['references'] as $ref) {
        if ($ref['id'] === 'IMPORT.EU.MR.2025.0000007') {
            echo "Sanitary cert found: " . $ref['attachment'] . "\n";
        }
    }
}
```

### Method 2: Smart Auto-Detection
```php
// Works with both CHED IDs and sanitary references
$result = $client->getCertificateByAnyReference('IMPORT.EU.MR.2025.0000007', 'MR');

if (isset($result['id'])) {
    // Single certificate
    echo "Certificate: " . $result['id'];
} else {
    // Multiple certificates
    echo "Found " . count($result) . " certificates";
}
```

### Method 3: API Endpoint
The existing API endpoint now supports both reference types:

```bash
# Works with CHED ID
curl "http://your-domain/api/certificates/products?reference=CHEDP.FR.2025.0000038"

# Now also works with sanitary reference
curl "http://your-domain/api/certificates/products?reference=IMPORT.EU.MR.2025.0000007"
```

## Testing

Run the test script to verify functionality:
```bash
php test_sanitary_reference_search.php
```

This will test:
1. Direct sanitary reference search
2. Smart auto-detection method
3. CHED ID lookup for comparison

## Response Format

When searching by sanitary reference, you get the full CHED certificate data including:

```php
Array(
    [id] => CHEDP.FR.2025.0000038
    [type] => P
    [status] => 70
    [status_name] => Issued (Validated)
    [consignor_name] => SARL FM2P MAURITANIE
    [consignee_name] => ETABLISSEMENT REYNAUD
    [references] => Array(
        [0] => Array(
            [type_code] => 852
            [type_name] => Sanitary certificate (EU Import certificate)
            [id] => IMPORT.EU.MR.2025.0000007  // Your search term
            [attachment] => https://webgate.ec.europa.eu/tracesnt/certificate/eu-import/IMPORT.EU.MR.2025.0000007
        )
        // ... other references
    )
    // ... other certificate data
)
```

## Key Benefits

1. **Backward Compatible**: Original CHED ID searches still work
2. **Flexible**: Can search by either reference type
3. **Smart Detection**: Automatically determines reference type
4. **Comprehensive**: Returns full CHED certificate data
5. **Filtered**: Can filter by country and date range
6. **Robust**: Includes alternative authentication methods

## Files Modified

1. `app/Providers/TracesNtChedClient.php` - Added new methods
2. `app/Http/Controllers/CertificateController.php` - Updated to use smart detection
3. `test_sanitary_reference_search.php` - Test script (new)
4. `SANITARY_REFERENCE_SEARCH_SOLUTION.md` - This documentation (new)

The sanitary reference "IMPORT.EU.MR.2025.0000007" should now be fully searchable!
