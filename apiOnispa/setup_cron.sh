#!/bin/bash

# CHED Certificate Sync - Cron Setup Script
# This script helps set up the cron job for automatic certificate synchronization

echo "=== CHED Certificate Sync - Cron Setup ==="
echo ""

# Get the current directory
CURRENT_DIR=$(pwd)
PROJECT_DIR="$CURRENT_DIR"

echo "Project directory: $PROJECT_DIR"
echo ""

# Check if we're in the right directory
if [ ! -f "artisan" ]; then
    echo "❌ Error: artisan file not found. Please run this script from your Laravel project root."
    exit 1
fi

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "❌ Error: PHP is not installed or not in PATH."
    exit 1
fi

echo "✅ PHP found: $(php --version | head -n 1)"

# Test the sync command
echo ""
echo "Testing the sync command..."
php artisan list | grep "ched:sync" > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ ched:sync command is available"
else
    echo "❌ Error: ched:sync command not found. Please ensure the command is registered."
    exit 1
fi

# Create the cron entry
CRON_ENTRY="*/10 * * * * cd $PROJECT_DIR && php artisan ched:sync --recent >> $PROJECT_DIR/storage/logs/ched-sync-cron.log 2>&1"

echo ""
echo "Recommended cron entry:"
echo "----------------------------------------"
echo "$CRON_ENTRY"
echo "----------------------------------------"
echo ""

# Ask user if they want to add it automatically
read -p "Do you want to add this cron entry automatically? (y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Backup current crontab
    echo "Backing up current crontab..."
    crontab -l > "$PROJECT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "No existing crontab found"
    
    # Add the new cron entry
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    
    if [ $? -eq 0 ]; then
        echo "✅ Cron entry added successfully!"
    else
        echo "❌ Failed to add cron entry. Please add it manually."
    fi
else
    echo "Manual setup required. Please add the cron entry manually:"
    echo "1. Run: crontab -e"
    echo "2. Add this line:"
    echo "   $CRON_ENTRY"
fi

echo ""
echo "=== Additional Setup Steps ==="
echo ""

# Check if migrations have been run
echo "1. Database Setup:"
php artisan migrate:status 2>/dev/null | grep -q "ched_certificates"
if [ $? -eq 0 ]; then
    echo "   ✅ Database migrations appear to be run"
else
    echo "   📋 Run database migrations: php artisan migrate"
fi

# Check if initial sync has been done
echo ""
echo "2. Initial Data Sync:"
echo "   📋 Run initial sync: php artisan ched:sync --recent"
echo "   📋 Or sync specific dates: php artisan ched:sync --start-date=2025-01-01 --end-date=2025-01-10"

echo ""
echo "3. Test the Setup:"
echo "   📋 Run test script: php test_database_sync.php"
echo "   📋 Test API endpoint: curl \"http://localhost:8000/api/certificates/products?reference=IMPORT.EU.MR.2025.0000007\""

echo ""
echo "4. Monitor Sync:"
echo "   📋 Check sync logs: tail -f storage/logs/ched-sync-cron.log"
echo "   📋 Check Laravel logs: tail -f storage/logs/laravel.log"

echo ""
echo "=== Cron Job Details ==="
echo "Frequency: Every 10 minutes"
echo "Command: php artisan ched:sync --recent"
echo "Log file: storage/logs/ched-sync-cron.log"
echo "What it does:"
echo "  - Fetches certificates from last 2 days"
echo "  - Updates existing certificates if changed"
echo "  - Adds new certificates and their sanitary references"
echo "  - Extracts and stores product information"
echo "  - Handles API pagination automatically"
echo ""

echo "=== Troubleshooting ==="
echo "If sync fails:"
echo "  - Check TRACES API credentials in .env"
echo "  - Verify internet connection"
echo "  - Check storage/logs/laravel.log for errors"
echo "  - Run manually: php artisan ched:sync --recent --force"
echo ""

echo "✅ Setup complete! Your CHED certificate sync system is ready."
echo ""
echo "🎉 You can now search by sanitary references like 'IMPORT.EU.MR.2025.0000007'!"
