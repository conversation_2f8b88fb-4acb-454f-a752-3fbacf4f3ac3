<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== getValidatedFishCertificates API Fallback Test ===\n\n";

// Test the API endpoint with a reference that's likely not in database
$testReference = 'IMPORT.EU.MR.2025.0000007';

echo "Testing API fallback with reference: {$testReference}\n";
echo "========================================================\n\n";

// Check if it exists in database first
echo "1. Checking database...\n";
$inDatabase = \App\Models\ChedCertificate::where('ched_id', $testReference)->exists() ||
              \App\Models\ChedSanitaryReference::where('sanitary_reference', $testReference)->exists();

echo "   Reference in database: " . ($inDatabase ? "YES" : "NO") . "\n";

if (!$inDatabase) {
    echo "   ✓ Perfect! This will trigger API fallback\n";
} else {
    echo "   ℹ This reference exists in database, so it will return from DB\n";
}

echo "\n2. Testing API endpoint...\n";

// Test the API endpoint
$url = "http://localhost:8011/api/certificates/products?reference=" . urlencode($testReference);
echo "   URL: {$url}\n";

// Make the API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Longer timeout for API fallback
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json'
]);

echo "   Making API call (this may take a while for API fallback)...\n";
$startTime = microtime(true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$endTime = microtime(true);
$duration = round($endTime - $startTime, 2);

echo "   Response time: {$duration} seconds\n";

if ($error) {
    echo "   ❌ CURL Error: {$error}\n";
} else {
    echo "   HTTP Status: {$httpCode}\n";
    
    $data = json_decode($response, true);
    if ($data) {
        echo "   Success: " . ($data['success'] ? 'YES' : 'NO') . "\n";
        
        if ($data['success']) {
            echo "   Source: " . ($data['source'] ?? 'unknown') . "\n";
            echo "   CHED ID: " . ($data['ched_id'] ?? 'unknown') . "\n";
            echo "   Products: " . ($data['products_count'] ?? 0) . "\n";
            
            if (isset($data['note']) && $data['note']) {
                echo "   📝 Note: " . $data['note'] . "\n";
            }
            
            if ($data['source'] === 'api_fallback') {
                echo "   🎉 SUCCESS! API fallback worked - certificate fetched from TRACES API\n";
            } else {
                echo "   ℹ Certificate was found in database\n";
            }
        } else {
            echo "   ❌ Error: " . ($data['message'] ?? 'Unknown error') . "\n";
            echo "   Error Code: " . ($data['error_code'] ?? 'unknown') . "\n";
            
            if (isset($data['suggestion'])) {
                echo "   Suggestion: " . $data['suggestion'] . "\n";
            }
        }
    } else {
        echo "   ❌ Invalid JSON response\n";
        echo "   Response preview: " . substr($response, 0, 200) . "...\n";
    }
}

echo "\n3. Checking database after API call...\n";
$certCountAfter = \App\Models\ChedCertificate::count();
$refCountAfter = \App\Models\ChedSanitaryReference::count();
echo "   Certificates in database: {$certCountAfter}\n";
echo "   Sanitary references in database: {$refCountAfter}\n";

// Check if our specific reference is now in database
$nowInDatabase = \App\Models\ChedCertificate::where('ched_id', $testReference)->exists() ||
                 \App\Models\ChedSanitaryReference::where('sanitary_reference', $testReference)->exists();

if (!$inDatabase && $nowInDatabase) {
    echo "   🎉 Certificate was successfully stored in database!\n";
} elseif ($inDatabase && $nowInDatabase) {
    echo "   ✓ Certificate was already in database\n";
} else {
    echo "   ℹ Certificate not found/stored\n";
}

echo "\n4. Testing second request (should be fast from database)...\n";
if ($nowInDatabase) {
    echo "   Making second API call to same reference...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $startTime2 = microtime(true);
    $response2 = curl_exec($ch);
    $endTime2 = microtime(true);
    $duration2 = round($endTime2 - $startTime2, 2);
    curl_close($ch);
    
    echo "   Second response time: {$duration2} seconds\n";
    
    $data2 = json_decode($response2, true);
    if ($data2 && $data2['success']) {
        echo "   Source: " . ($data2['source'] ?? 'unknown') . "\n";
        
        if ($data2['source'] === 'database') {
            echo "   🎉 SUCCESS! Second request served from database (fast)\n";
        }
    }
} else {
    echo "   Skipping second request test (certificate not in database)\n";
}

echo "\n=== Test Summary ===\n";
echo "✅ API fallback system tested\n";
echo "✅ Uses getValidatedFishCertificates with multiple date ranges\n";
echo "✅ Searches through certificates to find matching reference\n";
echo "✅ Stores found certificates in database for future use\n";
echo "✅ Subsequent requests served from database\n";

echo "\n=== How It Works ===\n";
echo "1. API receives request for certificate reference\n";
echo "2. Checks database first (fast)\n";
echo "3. If not found, calls getValidatedFishCertificates with date ranges:\n";
echo "   - Last 30 days\n";
echo "   - Last 90 days  \n";
echo "   - Last 6 months\n";
echo "   - This year\n";
echo "4. Searches through all returned certificates for matching reference\n";
echo "5. Stores found certificate in database\n";
echo "6. Returns certificate data with source='api_fallback'\n";
echo "7. Future requests = instant database lookup\n";

echo "\n=== Manual Test Commands ===\n";
echo "curl \"http://localhost:8011/api/certificates/products?reference=IMPORT.EU.MR.2025.0000007\"\n";
echo "curl \"http://localhost:8011/api/certificates/products?reference=CHEDP.FR.2025.0000038\"\n";

echo "\n=== Test Complete ===\n";
