# CHED Certificate Retrieval by Reference

This implementation adds the ability to retrieve CHED (Common Health Entry Document) certificates by their reference number using the TRACES NT WebService API.

## Overview

Based on the TNT-UN-CEFACT-WebService-for-CHED-retrieve-V2 documentation, this implementation provides:

- **getChedCertificateByReference()** method to retrieve individual certificates
- Full UN/CEFACT SPSCertificate format parsing
- Comprehensive error handling for TRACES NT specific errors
- Laravel integration examples with controllers and routes

## New Method Added

### `getChedCertificateByReference($chedReference)`

Retrieves a complete CHED certificate by its reference number.

**Parameters:**
- `$chedReference` (string): CHED reference number (e.g., "CHEDPP.IT.2020.1000091")

**Returns:**
- Array containing the complete certificate data in structured format

**Throws:**
- `ChedCertificateNotFoundException` - Certificate not found
- `ChedCertificatePermissionDeniedException` - Access denied
- `Exception` - Authentication or network errors

## Usage Examples

### Basic Usage

```php
use App\Providers\TracesNtChedClient;

$client = new TracesNtChedClient(
    'your_username',
    'your_auth_key', 
    'your_client_id',
    true // use production
);

try {
    $certificate = $client->getChedCertificateByReference('CHEDPP.IT.2020.1000091');
    
    echo "Certificate ID: " . $certificate['id'];
    echo "Issue Date: " . $certificate['issue_date_time'];
    
    // Access parties (consignor, consignee, etc.)
    foreach ($certificate['parties'] as $party) {
        echo $party['type'] . ": " . $party['name'];
    }
    
    // Access consignment items and classifications
    foreach ($certificate['consignments'] as $consignment) {
        foreach ($consignment['consignment_items'] as $item) {
            foreach ($item['classifications'] as $classification) {
                if ($classification['system_id'] === 'CN') {
                    echo "CN Code: " . $classification['class_code'];
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
```

### Laravel Controller Integration

```php
public function getCertificate(Request $request)
{
    $reference = $request->input('reference');
    
    try {
        $certificate = $this->tracesClient->getChedCertificateByReference($reference);
        
        return response()->json([
            'success' => true,
            'data' => $certificate
        ]);
        
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'message' => $e->getMessage()
        ], 500);
    }
}
```

## Certificate Data Structure

The method returns a structured array with the following main sections:

```php
[
    'id' => 'Certificate ID',
    'issue_date_time' => 'ISO 8601 datetime',
    'type_code' => 'Certificate type code',
    'purpose_code' => 'Purpose code',
    
    'parties' => [
        [
            'type' => 'ConsignorSPSParty|ConsigneeSPSParty|etc',
            'name' => 'Party name',
            'id' => 'Party identifier',
            'scheme_id' => 'ID scheme (eori_number, etc)',
            'address' => [
                'country_id' => 'Country code',
                'city_name' => 'City',
                'line_one' => 'Address line',
                'postcode' => 'Postal code'
            ]
        ]
    ],
    
    'consignments' => [
        [
            'id' => 'Consignment ID',
            'consignment_items' => [
                [
                    'sequence_numeric' => 'Item sequence',
                    'classifications' => [
                        [
                            'system_id' => 'CN|HS|etc',
                            'class_code' => 'Classification code',
                            'class_names' => ['en' => 'Description']
                        ]
                    ]
                ]
            ]
        ]
    ],
    
    'locations' => [
        [
            'type' => 'UnloadingBaseportSPSLocation|etc',
            'name' => 'Location name',
            'id' => 'Location identifier',
            'scheme_id' => 'ID scheme (un_locode, etc)'
        ]
    ],
    
    'referenced_documents' => [
        [
            'type_name' => 'Document type',
            'id' => 'Document ID',
            'relationship_name' => 'Relationship type',
            'attachment_filename' => 'Attachment filename',
            'information' => 'Additional information'
        ]
    ]
]
```

## CHED Reference Format

CHED references follow the format: `CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]`

Examples:
- `CHEDPP.IT.2020.1000091` - Plant/Plant Product certificate from Italy
- `CHEDP.MR.2025.0000001` - Product certificate from Mauritania  
- `CHEDA.FR.2024.0500612` - Animal certificate from France

Types:
- `CHEDPP` - Plants and Plant Products
- `CHEDP` - Products of animal origin
- `CHEDA` - Animals
- `CHEDD` - Article D (feed and food)

## Error Handling

The method handles specific TRACES NT errors:

- **ChedCertificateNotFoundException**: Certificate doesn't exist
- **ChedCertificatePermissionDeniedException**: No access rights
- **Authentication errors**: Invalid credentials or expired session
- **Network errors**: Connection timeouts or SSL issues

## Configuration

Add to your `config/services.php`:

```php
'traces' => [
    'username' => env('TRACES_USERNAME'),
    'auth_key' => env('TRACES_AUTH_KEY'),
    'client_id' => env('TRACES_CLIENT_ID'),
    'use_production' => env('TRACES_USE_PRODUCTION', false),
    'timeout' => env('TRACES_TIMEOUT', 60),
    'verify_ssl' => env('TRACES_VERIFY_SSL', true),
],
```

## Testing

Use the provided test script:

```bash
php test_get_certificate_by_reference.php
```

This will test the certificate retrieval with example reference numbers and show the complete data structure.

## API Integration

The example controller and routes show how to create REST API endpoints:

- `GET /api/certificates/by-reference?reference=CHEDPP.IT.2020.1000091`
- `POST /api/certificates/by-references` (for multiple certificates)
- `GET /api/certificates/validate-reference` (format validation)

## Files Added/Modified

1. **TracesNtChedClient.php** - Added `getChedCertificateByReference()` method
2. **test_get_certificate_by_reference.php** - Test script
3. **example_certificate_controller.php** - Laravel controller example
4. **example_api_routes.php** - API routes example
5. **example_traces_config.php** - Configuration example
6. **README_CHED_Certificate_Retrieval.md** - This documentation

## Next Steps

1. Test the implementation with your TRACES NT credentials
2. Integrate the controller into your Laravel application
3. Add the routes to your `routes/api.php` file
4. Configure your TRACES NT credentials in the environment
5. Implement any additional business logic for certificate processing

## Support

For issues with TRACES NT API access or credentials, contact the TRACES NT support team. For implementation questions, refer to the TNT-UN-CEFACT-WebService-for-CHED-retrieve-V2 documentation.
