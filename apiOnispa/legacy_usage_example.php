<?php

/**
 * Example usage of EuImportLegacyService for legacy code integration
 * 
 * This file demonstrates how to use the standalone EuImportLegacyService
 * in your legacy code to fetch and parse EU import certificates from TRACES API.
 */

require_once 'app/Services/EuImportLegacyService.php';

use App\Services\EuImportLegacyService;

// Initialize the service
$euImportService = new EuImportLegacyService();

// Example 1: Fetch and parse products for a single reference
try {
    $reference = 'IMPORT.EU.MR.2025.0003940';
    
    echo "Fetching products for reference: {$reference}\n";
    
    $result = $euImportService->getProductsForForms($reference);
    
    if ($result['success']) {
        echo "Success! Found {$result['summary']['total_products']} products\n";
        echo "Total weight: {$result['summary']['total_weight']} kg\n";
        echo "Total packages: {$result['summary']['total_packages']}\n\n";
        
        // Display products for form population
        foreach ($result['products'] as $product) {
            echo "Product ID: {$product['id']}\n";
            echo "Product Name: {$product['product_name']}\n";
            echo "Scientific Name: {$product['scientific_name']}\n";
            echo "Origin Country: {$product['origin_country']}\n";
            echo "Storage Location: {$product['storage_location']}\n";
            echo "Nature: {$product['nature']}\n";
            echo "Product Type: {$product['product_type']}\n";
            echo "Packaging Type: {$product['packaging_type']}\n";
            echo "Unit: {$product['unit']}\n";
            echo "Quantity: {$product['quantity']}\n";
            echo "Package Count: {$product['package_count']}\n";
            echo "Batch ID: {$product['batch_id']}\n";
            echo "FAO Code: {$product['fao_code']}\n";
            echo "CN Code: {$product['cn_code']}\n";
            echo "Is Wild: " . ($product['is_wild'] ? 'Yes' : 'No') . "\n";
            echo "Collection Date: {$product['collection_date']}\n";
            echo "Processing Plant: {$product['processing_plant']['name']} ({$product['processing_plant']['id']})\n";
            echo "---\n";
        }
    } else {
        echo "Failed to fetch products: {$result['error']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 80) . "\n\n";

// Example 2: Search multiple references at once
try {
    $references = [
        'IMPORT.EU.MR.2025.0003940',
        'IMPORT.EU.MR.2025.0003941', // This might not exist
        'IMPORT.EU.MR.2025.0003942'  // This might not exist
    ];
    
    echo "Searching multiple references...\n";
    
    $results = $euImportService->searchMultipleReferences($references);
    
    foreach ($results as $reference => $result) {
        echo "Reference: {$reference}\n";
        if ($result['success']) {
            echo "  Status: Success\n";
            echo "  Products: {$result['summary']['total_products']}\n";
            echo "  Total Weight: {$result['summary']['total_weight']} kg\n";
        } else {
            echo "  Status: Failed\n";
            echo "  Error: {$result['error']}\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Error in multiple search: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 80) . "\n\n";

// Example 3: Raw product parsing (if you already have XML)
try {
    $reference = 'IMPORT.EU.MR.2025.0003940';
    
    // First get the certificate data
    $certificateResult = $euImportService->fetchAndParseProducts($reference);
    
    if ($certificateResult['success'] && isset($certificateResult['certificate']['raw_xml'])) {
        echo "Parsing products from raw XML...\n";
        
        $products = $euImportService->parseProductsFromXml(
            $certificateResult['certificate']['raw_xml'], 
            $reference
        );
        
        echo "Parsed " . count($products) . " products from XML\n";
        
        // Display first product details
        if (!empty($products)) {
            $firstProduct = $products[0];
            echo "First product details:\n";
            echo "  Scientific Name: {$firstProduct['scientific_name']}\n";
            echo "  Common Name: {$firstProduct['common_name']}\n";
            echo "  Net Weight: {$firstProduct['net_weight']} {$firstProduct['net_weight_unit']}\n";
            echo "  CN Code: {$firstProduct['cn_code']}\n";
            echo "  FAO Code: {$firstProduct['fao_code']}\n";
            echo "  Nature: {$firstProduct['nature_description']}\n";
            echo "  Treatment: {$firstProduct['treatment_description']}\n";
            echo "  Package Type: {$firstProduct['package_type_name']}\n";
            echo "  Package Quantity: {$firstProduct['package_quantity']}\n";
            echo "  Processing Plant: {$firstProduct['processing_plant_name']}\n";
            echo "  Origin Country: {$firstProduct['origin_country_name']}\n";
            echo "  Is Wild Stock: " . ($firstProduct['is_wild_stock'] ? 'Yes' : 'No') . "\n";
            echo "  Collection Date: {$firstProduct['collection_date']}\n";
            
            // Display processes
            if (!empty($firstProduct['processes'])) {
                echo "  Processes:\n";
                foreach ($firstProduct['processes'] as $process) {
                    echo "    - {$process['type_name']} ({$process['type_code']})\n";
                    if ($process['start_date_time']) {
                        echo "      Start: {$process['start_date_time']}\n";
                    }
                    if ($process['operator']) {
                        echo "      Operator: {$process['operator']['name']} ({$process['operator']['id']})\n";
                    }
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error in raw parsing: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 80) . "\n\n";

// Example 4: Integration with form population (pseudo-code)
function populateProductForms($reference) {
    $euImportService = new EuImportLegacyService();
    
    try {
        $result = $euImportService->getProductsForForms($reference);
        
        if ($result['success']) {
            // This is how you would populate your legacy forms
            foreach ($result['products'] as $product) {
                // Populate form fields - adjust field names to match your forms
                /*
                $_POST['product_' . $product['id'] . '_name'] = $product['product_name'];
                $_POST['product_' . $product['id'] . '_origin'] = $product['origin_country'];
                $_POST['product_' . $product['id'] . '_storage'] = $product['storage_location'];
                $_POST['product_' . $product['id'] . '_nature'] = $product['nature'];
                $_POST['product_' . $product['id'] . '_type'] = $product['product_type'];
                $_POST['product_' . $product['id'] . '_packaging'] = $product['packaging_type'];
                $_POST['product_' . $product['id'] . '_unit'] = $product['unit'];
                $_POST['product_' . $product['id'] . '_quantity'] = $product['quantity'];
                $_POST['product_' . $product['id'] . '_packages'] = $product['package_count'];
                */
                
                echo "Would populate form for product: {$product['product_name']}\n";
            }
            
            return $result['products'];
        } else {
            throw new Exception($result['error']);
        }
        
    } catch (Exception $e) {
        echo "Error populating forms: " . $e->getMessage() . "\n";
        return [];
    }
}

echo "Example form population:\n";
$formProducts = populateProductForms('IMPORT.EU.MR.2025.0003940');
echo "Would populate " . count($formProducts) . " product forms\n";

echo "\nUsage complete!\n";
