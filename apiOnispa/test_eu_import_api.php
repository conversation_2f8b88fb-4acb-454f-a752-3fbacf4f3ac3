<?php

/**
 * Test script for EU-Import Certificate API
 * 
 * This script demonstrates how to use the new EU-Import API endpoints
 * to retrieve import documents from TRACES and store them locally.
 */

require_once __DIR__ . '/vendor/autoload.php';

class EuImportApiTester
{
    private $baseUrl;
    private $testReference;

    public function __construct($baseUrl = 'http://localhost:8000', $testReference = 'IMPORT.EU.IT.2020.1000091')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->testReference = $testReference;
    }

    /**
     * Test fetching and storing a certificate
     */
    public function testFetchAndStore()
    {
        echo "=== Testing Fetch and Store Certificate ===\n";
        
        $url = $this->baseUrl . '/api/eu-import/fetch';
        $data = [
            'reference' => $this->testReference,
            'save_xml' => true,
            'force_refresh' => false
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response['success']) {
            echo "✓ Certificate fetched successfully\n";
            echo "  Source: " . $response['source'] . "\n";
            echo "  Import ID: " . $response['certificate']['import_id'] . "\n";
            echo "  Status: " . $response['certificate']['status_name'] . "\n";
            echo "  Country of Consignor: " . $response['certificate']['country_of_consignor'] . "\n";
            echo "  Country of Consignee: " . $response['certificate']['country_of_consignee'] . "\n";
            
            if (isset($response['xml_file_path'])) {
                echo "  XML saved to: " . $response['xml_file_path'] . "\n";
            }
            
            if (isset($response['certificate']['commodities'])) {
                echo "  Commodities found: " . count($response['certificate']['commodities']) . "\n";
            }
        } else {
            echo "✗ Failed to fetch certificate: " . $response['message'] . "\n";
        }
        
        echo "\n";
        return $response['success'] ?? false;
    }

    /**
     * Test getting certificate PDF
     */
    public function testGetPdf()
    {
        echo "=== Testing Get Certificate PDF ===\n";
        
        $url = $this->baseUrl . '/api/eu-import/pdf';
        $data = [
            'reference' => $this->testReference,
            'extra_languages' => ['fr']
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response['success']) {
            echo "✓ PDF retrieved successfully\n";
            echo "  Content type: " . $response['content_type'] . "\n";
            echo "  Encoding: " . $response['encoding'] . "\n";
            echo "  PDF size: " . strlen($response['pdf_content']) . " characters (base64)\n";
            
            // Optionally save PDF to file for testing
            $pdfData = base64_decode($response['pdf_content']);
            if ($pdfData) {
                $filename = 'test_certificate_' . date('Y-m-d_H-i-s') . '.pdf';
                file_put_contents($filename, $pdfData);
                echo "  PDF saved to: " . $filename . "\n";
            }
        } else {
            echo "✗ Failed to get PDF: " . $response['message'] . "\n";
        }
        
        echo "\n";
        return $response['success'] ?? false;
    }

    /**
     * Test getting signed PDF
     */
    public function testGetSignedPdf()
    {
        echo "=== Testing Get Signed PDF ===\n";
        
        $url = $this->baseUrl . '/api/eu-import/signed-pdf';
        $data = [
            'reference' => $this->testReference
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response['success']) {
            echo "✓ Signed PDF retrieved successfully\n";
            echo "  Content type: " . $response['content_type'] . "\n";
            echo "  Signed: " . ($response['signed'] ? 'Yes' : 'No') . "\n";
            echo "  PDF size: " . strlen($response['pdf_content']) . " characters (base64)\n";
        } else {
            echo "✗ Failed to get signed PDF: " . $response['message'] . "\n";
            echo "  Note: Signed PDFs may not be available for all certificates\n";
        }
        
        echo "\n";
        return $response['success'] ?? false;
    }

    /**
     * Test searching certificates
     */
    public function testSearch()
    {
        echo "=== Testing Certificate Search ===\n";
        
        $url = $this->baseUrl . '/api/eu-import/search';
        $data = [
            'page_size' => 10,
            'offset' => 0,
            'status' => [70], // Validated certificates
            'country_of_consignor' => 'MR', // Morocco
            'use_api' => false // Search in local database
        ];

        $response = $this->makeRequest('POST', $url, $data);
        
        if ($response['success']) {
            echo "✓ Search completed successfully\n";
            echo "  Source: " . $response['source'] . "\n";
            
            if (isset($response['results']['certificates'])) {
                $certificates = $response['results']['certificates'];
                echo "  Found: " . count($certificates) . " certificates\n";
                echo "  Total: " . ($response['results']['total'] ?? 'unknown') . "\n";
                
                foreach ($certificates as $index => $cert) {
                    echo "  Certificate " . ($index + 1) . ":\n";
                    echo "    ID: " . $cert['import_id'] . "\n";
                    echo "    Status: " . $cert['status_name'] . "\n";
                    echo "    Consignor: " . $cert['country_of_consignor'] . "\n";
                    echo "    Consignee: " . $cert['country_of_consignee'] . "\n";
                }
            }
        } else {
            echo "✗ Search failed: " . $response['message'] . "\n";
        }
        
        echo "\n";
        return $response['success'] ?? false;
    }

    /**
     * Test getting certificate commodities
     */
    public function testGetCommodities()
    {
        echo "=== Testing Get Certificate Commodities ===\n";
        
        $url = $this->baseUrl . '/api/eu-import/commodities?reference=' . urlencode($this->testReference);

        $response = $this->makeRequest('GET', $url);
        
        if ($response['success']) {
            echo "✓ Commodities retrieved successfully\n";
            echo "  Reference: " . $response['reference'] . "\n";
            echo "  Commodities found: " . count($response['commodities']) . "\n";
            
            foreach ($response['commodities'] as $index => $commodity) {
                echo "  Commodity " . ($index + 1) . ":\n";
                echo "    System: " . $commodity['system_id'] . " (" . $commodity['system_name'] . ")\n";
                echo "    Code: " . $commodity['class_code'] . "\n";
                echo "    Name: " . $commodity['class_name'] . "\n";
            }
        } else {
            echo "✗ Failed to get commodities: " . $response['message'] . "\n";
        }
        
        echo "\n";
        return $response['success'] ?? false;
    }

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "EU-Import Certificate API Test Suite\n";
        echo "====================================\n";
        echo "Base URL: " . $this->baseUrl . "\n";
        echo "Test Reference: " . $this->testReference . "\n\n";

        $results = [];
        
        $results['fetch'] = $this->testFetchAndStore();
        $results['pdf'] = $this->testGetPdf();
        $results['signed_pdf'] = $this->testGetSignedPdf();
        $results['search'] = $this->testSearch();
        $results['commodities'] = $this->testGetCommodities();

        echo "=== Test Results Summary ===\n";
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $success) {
            $status = $success ? '✓ PASS' : '✗ FAIL';
            echo "$status: $test\n";
            if ($success) $passed++;
        }
        
        echo "\nOverall: $passed/$total tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All tests passed!\n";
        } else {
            echo "⚠️  Some tests failed. Check the output above for details.\n";
        }
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $url, $data = null)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]);

        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return ['success' => false, 'message' => 'CURL Error: ' . $error];
        }

        $decoded = json_decode($response, true);
        if (!$decoded) {
            return ['success' => false, 'message' => 'Invalid JSON response', 'raw_response' => $response];
        }

        return $decoded;
    }
}

// Run the tests if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $tester = new EuImportApiTester();
    
    // You can customize the base URL and test reference here
    if (isset($argv[1])) {
        $tester = new EuImportApiTester($argv[1]);
    }
    if (isset($argv[2])) {
        $tester = new EuImportApiTester($argv[1] ?? 'http://localhost:8000', $argv[2]);
    }
    
    $tester->runAllTests();
}
