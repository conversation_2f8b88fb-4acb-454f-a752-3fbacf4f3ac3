CLIENT_DEV_URL=Laravel
APP_ENV=local
APP_KEY=base64:6ZAWkYXOZIazaalSd2WcasD1hpYJ4urfVnidIXKIJFY=
APP_DEBUG=true
APP_URL=http://localhost
LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=onispa_db
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=adminonispa


BROADCAST_DRIVER=redis
REDIS_CLIENT=predis
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379


AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_HOST=localhost
PUSHER_APP_ID=local
PUSHER_APP_KEY=local
PUSHER_APP_SECRET=local
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
SUPER_ADMIN_SLUG=super_administrateur

ELASTIC_HOST=elasticsearch:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=adminsdtm



SCOUT_DRIVER=elastic
SCOUT_DRIVER=collection

JWT_SECRET=9A2unjjZL6G2sSEs7V0PqR2xtCVsoSJuR6jlnVJNwMBzXrIPMIGGTcVE2Dk0EjP8


FIREBASE_CREDENTIALS=firebase.json


MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=e07d4a8a7482e9
MAIL_PASSWORD=aca7786ef25392
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=SDTM

CLIENT_DEV=http://localhost:4200
CLIENT_PROD_URL=https://sdtm-preprod-client.devcorp.fr



BO_DEV_URL=http://localhost:60907
BO_PROD_URL=https://sdtm-preprod-bo.devcorp.fr


FLEET_MAP_API_URL=https://api.pinme.io/api
FLEET_MAP_API_USERNAME=<EMAIL>
FLEET_MAP_API_PASSWORD=N@bil_2024Scx

# TRACES API Configuration
TRACES_USERNAME=n00385tm
TRACES_AUTH_KEY=7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh
TRACES_CLIENT_ID=onispa-mr
TRACES_USE_PRODUCTION=true
TRACES_TIMEOUT=60
TRACES_VERIFY_SSL=true

