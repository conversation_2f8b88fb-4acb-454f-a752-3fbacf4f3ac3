<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use App\Models\ApiLog;
use App\Services\ApiLogService;

class LogController extends Controller
{
    public function index(Request $request)
    {
        $type = $request->get('type', 'all');
        $days = $request->get('days', 7);
        
        // Get API logs
        $query = ApiLog::query();
        
        if ($type === 'traces') {
            $query->tracesOutgoing();
        } elseif ($type === 'incoming') {
            $query->ourIncoming();
        }
        
        $apiLogs = $query->recent($days)->orderBy('created_at', 'desc')->paginate(20);
        
        // Get statistics
        $tracesStats = ApiLogService::getTracesApiStats($days);
        $incomingStats = ApiLogService::getIncomingApiStats($days);
        
        // Get system logs (legacy)
        $logFile = storage_path('logs/laravel.log');
        $systemLogs = [];
        
        if (File::exists($logFile)) {
            $logContent = File::get($logFile);
            $logLines = explode("\n", $logContent);
            
            // Get the last 50 log entries
            $logLines = array_slice($logLines, -50);
            
            foreach ($logLines as $line) {
                if (!empty(trim($line))) {
                    $systemLogs[] = $line;
                }
            }
            
            $systemLogs = array_reverse($systemLogs);
        }
        
        return view('logs.index', compact('apiLogs', 'tracesStats', 'incomingStats', 'systemLogs', 'type', 'days'));
    }

    public function show($id)
    {
        $log = ApiLog::findOrFail($id);
        return response()->json($log);
    }
} 