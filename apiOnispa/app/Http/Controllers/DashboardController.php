<?php

namespace App\Http\Controllers;

use App\Models\Certificate;

class DashboardController extends Controller
{
    public function index()
    {
        $totalCertificates = Certificate::count();
        $recentCertificates = Certificate::orderBy('created_at', 'desc')->take(5)->get();
        $pendingCertificates = Certificate::where('status', '!=', 'sent')->count();
        
        return view('dashboard', compact('totalCertificates', 'recentCertificates', 'pendingCertificates'));
    }
} 