<?php
namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use App\Providers\TracesNtChedClient;
use App\Services\ApiLogService;

class CertificateController extends Controller
{
    public function index()
    {
        $certificates = Certificate::with('validator')->orderBy('created_at', 'desc')->paginate(20);
        
        // Get the last certificate date for guidance
        $lastCertificate = Certificate::orderBy('created_at', 'desc')->first();
        $lastCertificateDate = $lastCertificate ? $lastCertificate->created_at->format('Y-m-d') : null;
        
        // Get the earliest certificate date
        $earliestCertificate = Certificate::orderBy('created_at', 'asc')->first();
        $earliestCertificateDate = $earliestCertificate ? $earliestCertificate->created_at->format('Y-m-d') : null;
        
        // Get certificate count by date range
        $todayCount = Certificate::whereDate('created_at', today())->count();
        $yesterdayCount = Certificate::whereDate('created_at', today()->subDay())->count();
        $lastWeekCount = Certificate::whereBetween('created_at', [today()->subWeek(), today()])->count();
        
        return view('certificates.index', compact(
            'certificates', 
            'lastCertificateDate', 
            'earliestCertificateDate',
            'todayCount',
            'yesterdayCount',
            'lastWeekCount'
        ));
    }

    public function show($id)
    {
        $certificate = Certificate::findOrFail($id);
        return response()->json($certificate);
    }

    public function fetchAndStore(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $startTime = microtime(true);

        try {
            // Get TRACES configuration from environment
            $tracesConfig = config('services.traces');

            // Log configuration for debugging (without sensitive data)
            \Log::info('TRACES API Configuration', [
                'username' => $tracesConfig['username'],
                'client_id' => $tracesConfig['client_id'],
                'use_production' => $tracesConfig['use_production'],
                'timeout' => $tracesConfig['timeout'],
                'verify_ssl' => $tracesConfig['verify_ssl'],
                'auth_key_length' => strlen($tracesConfig['auth_key']),
                'server_timezone' => date_default_timezone_get(),
                'server_time' => date('Y-m-d H:i:s T'),
                'correct_time_should_be' => date('Y-m-d H:i:s T', time() - (365 * 24 * 60 * 60)), // Subtract 1 year
                'php_version' => PHP_VERSION,
                'curl_version' => curl_version()['version'] ?? 'unknown',
                'system_date_command' => trim(shell_exec('date') ?? 'unavailable'),
                'ntp_status' => trim(shell_exec('timedatectl status 2>/dev/null | grep "NTP synchronized"') ?? 'unavailable')
            ]);

            // Create TracesNtChedClient with environment configuration
            $client = new TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $certificates = $client->getValidatedFishCertificates($startDate, $endDate);
            $duration = round((microtime(true) - $startTime) * 1000, 2); // Convert to milliseconds

            $storedCount = 0;
            foreach ($certificates as $cert) {
                Certificate::updateOrCreate(
                    ['certificate_id' => $cert['id']],
                    [
                        'type' => $cert['type'],
                        'local_reference' => $cert['local_reference'],
                        'status' => $cert['status'],
                        'status_name' => $cert['status_name'],
                        'bcp_code' => $cert['bcp_code'],
                        'bcp_unlocode' => $cert['bcp_unlocode'],
                        'country_of_issuance' => $cert['country_of_issuance'],
                        'country_of_entry' => $cert['country_of_entry'],
                        'country_of_dispatch' => $cert['country_of_dispatch'],
                        'country_of_origin' => $cert['country_of_origin'],
                        'country_of_place_of_destination' => $cert['country_of_place_of_destination'],
                        'country_of_consignor' => $cert['country_of_consignor'],
                        'consignor_name' => $cert['consignor_name'],
                        'country_of_consignee' => $cert['country_of_consignee'],
                        'consignee_name' => $cert['consignee_name'],
                        'create_date_time' => $cert['create_date_time'],
                        'update_date_time' => $cert['update_date_time'],
                        'status_change_date_time' => $cert['status_change_date_time'],
                        'declaration_date_time' => $cert['declaration_date_time'],
                        'decision_date_time' => $cert['decision_date_time'],
                        'prior_notification_date_time' => $cert['prior_notification_date_time'],
                        'commodities' => $cert['commodities'],
                        'references' => $cert['references'],
                    ]
                );
                $storedCount++;
            }

            // Log the successful TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                $certificates, 
                $duration, 
                true, 
                null, 
                200
            );

            // Log the successful fetch
            \Log::info("Successfully fetched and stored {$storedCount} certificates from TRACES API", [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'stored_count' => $storedCount,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['stored' => $storedCount, 'success' => true]);
            }

            return redirect()->route('certificates')->with('success', "Successfully fetched and stored {$storedCount} certificates.");

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log the failed TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                [], 
                $duration, 
                false, 
                $e->getMessage(), 
                500
            );

            \Log::error('Error fetching certificates from TRACES API', [
                'error' => $e->getMessage(),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return redirect()->route('certificates')->with('error', 'Error fetching certificates: ' . $e->getMessage());
        }
    }

    public function sendSelected(Request $request)
    {
        $selectedIds = $request->input('selected_certificates', []);
        
        if (empty($selectedIds)) {
            return redirect()->route('certificates')->with('error', 'No certificates selected.');
        }



        $selectedIds = json_decode($selectedIds, true);
        
        $certificates = Certificate::whereIn('id', $selectedIds)->get();
        
        try {
            // Here you would implement the logic to send certificates back to TRACES API
            // For now, we'll just log the action
            \Log::info("Sending selected certificates back to TRACES API", [
                'certificate_ids' => $selectedIds,
                'count' => count($certificates)
            ]);

            // Update status to indicate they've been sent and track who validated them
            Certificate::whereIn('id', $selectedIds)->update([
                'status' => 'sent',
                'validated_by' => auth()->id(),
                'validated_at' => now()
            ]);

            return redirect()->route('certificates')->with('success', "Successfully sent " . count($certificates) . " certificates to TRACES API.");

        } catch (\Exception $e) {
            \Log::error('Error sending certificates to TRACES API', [
                'error' => $e->getMessage(),
                'certificate_ids' => $selectedIds
            ]);

            return redirect()->route('certificates')->with('error', 'Error sending certificates: ' . $e->getMessage());
        }
    }

    /**
     * Get all products linked to a certificate by its reference number
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCertificateProducts(Request $request)
    {
        $reference = $request->input('reference');

        if (!$reference) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate reference is required',
                'error_code' => 'MISSING_REFERENCE'
            ], 400);
        }

        // Validate reference format (basic validation)
        if (strlen($reference) < 5) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid certificate reference format',
                'error_code' => 'INVALID_REFERENCE_FORMAT'
            ], 400);
        }

        try {
            \Log::info('Retrieving certificate products from database', [
                'reference' => $reference
            ]);

            // Try to find certificate by CHED ID first
            $certificate = \App\Models\ChedCertificate::where('ched_id', $reference)
                ->with(['products', 'sanitaryReferences'])
                ->first();

            // If not found by CHED ID, try to find by sanitary reference
            if (!$certificate) {
                $certificate = \App\Models\ChedCertificate::findBySanitaryReference($reference);
            }

            // If not found in database, try to fetch from TRACES API and store it
            if (!$certificate) {
                \Log::info('Certificate not found in database, trying TRACES API fallback', [
                    'reference' => $reference
                ]);

                try {
                    $certificate = $this->fetchAndStoreCertificateFromApi($reference);

                    if (!$certificate) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Certificate not found in database or TRACES API.',
                            'error_code' => 'CERTIFICATE_NOT_FOUND',
                            'suggestion' => 'Verify the reference number is correct'
                        ], 404);
                    }

                    \Log::info('Certificate fetched from TRACES API and stored in database', [
                        'reference' => $reference,
                        'ched_id' => $certificate->ched_id
                    ]);

                } catch (\Exception $e) {
                    \Log::error('Failed to fetch certificate from TRACES API', [
                        'reference' => $reference,
                        'error' => $e->getMessage()
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Certificate not found in database. TRACES API fallback failed: ' . $e->getMessage(),
                        'error_code' => 'API_FALLBACK_FAILED',
                        'suggestion' => 'Try running: php artisan ched:sync --recent'
                    ], 404);
                }
            }

            // Extract products from the certificate
            $products = $certificate->products->map(function ($product) {
                return [
                    'system_id' => $product->system_id,
                    'system_name' => $product->system_name,
                    'class_code' => $product->class_code,
                    'class_names' => $product->class_names,
                    'class_name_en' => $product->class_name_en,
                ];
            })->toArray();

            // Get sanitary references for additional context
            $sanitaryReferences = $certificate->sanitaryReferences->map(function ($ref) {
                return [
                    'sanitary_reference' => $ref->sanitary_reference,
                    'type_name' => $ref->type_name,
                    'attachment_uri' => $ref->attachment_uri,
                ];
            })->toArray();

            // Determine data source
            $dataSource = $certificate->wasRecentlyCreated ? 'api_fallback' : 'database';

            \Log::info('Certificate products retrieved successfully', [
                'reference' => $reference,
                'ched_id' => $certificate->ched_id,
                'source' => $dataSource,
                'products_count' => count($products),
                'sanitary_references_count' => count($sanitaryReferences)
            ]);

            return response()->json([
                'success' => true,
                'certificate_reference' => $reference,
                'ched_id' => $certificate->ched_id,
                'source' => $dataSource,
                'certificate_info' => [
                    'type' => $certificate->type,
                    'status' => $certificate->status_name,
                    'country_of_dispatch' => $certificate->country_of_dispatch,
                    'consignor_name' => $certificate->consignor_name,
                    'consignee_name' => $certificate->consignee_name,
                    'update_date_time' => $certificate->update_date_time?->toISOString(),
                ],
                'products_count' => count($products),
                'products' => $products,
                'sanitary_references' => $sanitaryReferences,
                'last_sync_info' => $this->getLastSyncInfo(),
                'note' => $dataSource === 'api_fallback' ? 'Certificate was fetched from TRACES API and stored in database for future use.' : null
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to retrieve certificate products from database', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve certificate products: ' . $e->getMessage(),
                'error_code' => 'RETRIEVAL_FAILED'
            ], 500);
        }
    }

    /**
     * Fetch certificate from TRACES API and store in database
     */
    private function fetchAndStoreCertificateFromApi(string $reference): ?\App\Models\ChedCertificate
    {
        try {
            // Get TRACES API configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                throw new \Exception('TRACES API configuration not found');
            }

            // Create TracesNtChedClient
            $client = new \App\Providers\TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            \Log::info('Searching for certificate in TRACES API', [
                'reference' => $reference
            ]);

            // First try the smart detection method for direct lookup
            try {
                \Log::info('Trying smart certificate lookup by reference', [
                    'reference' => $reference
                ]);

                $certificateData = $client->getCertificateByAnyReference($reference);

                // If we get an array of certificates, take the first one
                if (is_array($certificateData) && isset($certificateData[0]) && is_array($certificateData[0])) {
                    $certificateData = $certificateData[0];
                }

                \Log::info('Smart lookup successful', [
                    'reference' => $reference,
                    'found_ched_id' => $certificateData['id'] ?? 'unknown'
                ]);

            } catch (\Exception $e) {
                \Log::info('Smart lookup failed, trying complete certificate info method', [
                    'reference' => $reference,
                    'error' => $e->getMessage()
                ]);

                // Try the complete certificate info method
                try {
                    $completeCertificates = $client->getCompleteCertificateInfo($reference);
                    if (!empty($completeCertificates)) {
                        $certificateData = $completeCertificates[0]; // Take the first match
                        \Log::info('Complete certificate info method successful', [
                            'reference' => $reference,
                            'found_ched_id' => $certificateData['id'] ?? 'unknown'
                        ]);
                    } else {
                        $certificateData = null;
                    }
                } catch (\Exception $e2) {
                    \Log::info('Complete certificate info method failed, falling back to date range search', [
                        'reference' => $reference,
                        'error' => $e2->getMessage()
                    ]);

                    // Fall back to the original date range search method
                    $certificateData = $this->searchCertificateInDateRanges($client, $reference);
                }
            }

            if (!$certificateData) {
                \Log::warning('Certificate not found in TRACES API after trying all methods', [
                    'reference' => $reference
                ]);
                return null;
            }

            \Log::info('Certificate found in TRACES API', [
                'reference' => $reference,
                'found_ched_id' => $certificateData['id'] ?? 'unknown'
            ]);

            // Store the certificate in database using the sync service logic
            return $this->storeCertificateFromApiData($certificateData);

        } catch (\Exception $e) {
            \Log::error('Failed to fetch certificate from TRACES API', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Search for certificate in multiple date ranges
     */
    private function searchCertificateInDateRanges($client, string $reference): ?array
    {
        // Define search date ranges (most recent first)
        $dateRanges = [
            // Last 30 days
            [now()->subDays(30)->format('Y-m-d'), now()->format('Y-m-d')],
            // Last 90 days
            [now()->subDays(90)->format('Y-m-d'), now()->subDays(30)->format('Y-m-d')],
            // Last 6 months
            [now()->subMonths(6)->format('Y-m-d'), now()->subDays(90)->format('Y-m-d')],
            // This year
            [now()->startOfYear()->format('Y-m-d'), now()->subMonths(6)->format('Y-m-d')],
        ];

        foreach ($dateRanges as $index => $dateRange) {
            [$startDate, $endDate] = $dateRange;

            \Log::info('Searching date range for certificate', [
                'reference' => $reference,
                'range_index' => $index + 1,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);

            try {
                $certificates = $client->getValidatedFishCertificates($startDate, $endDate);

                if (!is_array($certificates)) {
                    \Log::warning('Invalid response from getValidatedFishCertificates', [
                        'range_index' => $index + 1,
                        'response_type' => gettype($certificates)
                    ]);
                    continue;
                }

                \Log::info('Retrieved certificates from TRACES API', [
                    'range_index' => $index + 1,
                    'total_certificates' => count($certificates)
                ]);

                // Search for our certificate in the results
                $foundCertificate = $this->findCertificateInResults($certificates, $reference);

                if ($foundCertificate) {
                    \Log::info('Certificate found in date range', [
                        'reference' => $reference,
                        'range_index' => $index + 1,
                        'found_ched_id' => $foundCertificate['id'] ?? 'unknown'
                    ]);
                    return $foundCertificate;
                }

            } catch (\Exception $e) {
                \Log::warning('Failed to search date range', [
                    'reference' => $reference,
                    'range_index' => $index + 1,
                    'error' => $e->getMessage()
                ]);
                // Continue to next date range
                continue;
            }
        }

        return null;
    }

    /**
     * Find certificate in API results by reference
     */
    private function findCertificateInResults(array $certificates, string $reference): ?array
    {
        foreach ($certificates as $certificate) {
            // Check if this is the certificate we're looking for

            // 1. Check CHED ID match
            if (isset($certificate['id']) && $certificate['id'] === $reference) {
                return $certificate;
            }

            // 2. Check sanitary references
            if (isset($certificate['references']) && is_array($certificate['references'])) {
                foreach ($certificate['references'] as $ref) {
                    if (isset($ref['id']) && $ref['id'] === $reference) {
                        return $certificate;
                    }
                }
            }

            // 3. Check local reference
            if (isset($certificate['local_reference']) && $certificate['local_reference'] === $reference) {
                return $certificate;
            }
        }

        return null;
    }

    /**
     * Store certificate data from API into database
     */
    private function storeCertificateFromApiData(array $data): \App\Models\ChedCertificate
    {
        $chedId = $data['id'] ?? null;
        if (!$chedId) {
            throw new \Exception('Certificate data missing ID');
        }

        // Use database transaction to ensure data consistency
        return \DB::transaction(function () use ($data, $chedId) {
            // Check if certificate already exists (might have been created by another request)
            $certificate = \App\Models\ChedCertificate::where('ched_id', $chedId)->first();

            if ($certificate) {
                // Update existing certificate
                $certificate->update([
                    'type' => $data['type'] ?? $certificate->type,
                    'local_reference' => $data['local_reference'] ?? $certificate->local_reference,
                    'status' => $data['status'] ?? $certificate->status,
                    'status_name' => $data['status_name'] ?? $certificate->status_name,
                    'bcp_code' => $data['bcp_code'] ?? $certificate->bcp_code,
                    'bcp_unlocode' => $data['bcp_unlocode'] ?? $certificate->bcp_unlocode,
                    'country_of_issuance' => $data['country_of_issuance'] ?? $certificate->country_of_issuance,
                    'country_of_entry' => $data['country_of_entry'] ?? $certificate->country_of_entry,
                    'country_of_dispatch' => $data['country_of_dispatch'] ?? $certificate->country_of_dispatch,
                    'country_of_origin' => $data['country_of_origin'] ?? $certificate->country_of_origin,
                    'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? $certificate->country_of_place_of_destination,
                    'country_of_consignor' => $data['country_of_consignor'] ?? $certificate->country_of_consignor,
                    'consignor_name' => $data['consignor_name'] ?? $certificate->consignor_name,
                    'country_of_consignee' => $data['country_of_consignee'] ?? $certificate->country_of_consignee,
                    'consignee_name' => $data['consignee_name'] ?? $certificate->consignee_name,
                    'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null) ?? $certificate->update_date_time,
                    'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null) ?? $certificate->status_change_date_time,
                    'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null) ?? $certificate->declaration_date_time,
                    'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null) ?? $certificate->decision_date_time,
                    'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null) ?? $certificate->prior_notification_date_time,
                    'raw_data' => $data,
                ]);
            } else {
                // Create new certificate
                $certificate = \App\Models\ChedCertificate::create([
                    'ched_id' => $chedId,
                    'type' => $data['type'] ?? null,
                    'local_reference' => $data['local_reference'] ?? null,
                    'status' => $data['status'] ?? null,
                    'status_name' => $data['status_name'] ?? null,
                    'bcp_code' => $data['bcp_code'] ?? null,
                    'bcp_unlocode' => $data['bcp_unlocode'] ?? null,
                    'country_of_issuance' => $data['country_of_issuance'] ?? null,
                    'country_of_entry' => $data['country_of_entry'] ?? null,
                    'country_of_dispatch' => $data['country_of_dispatch'] ?? null,
                    'country_of_origin' => $data['country_of_origin'] ?? null,
                    'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? null,
                    'country_of_consignor' => $data['country_of_consignor'] ?? null,
                    'consignor_name' => $data['consignor_name'] ?? null,
                    'country_of_consignee' => $data['country_of_consignee'] ?? null,
                    'consignee_name' => $data['consignee_name'] ?? null,
                    'create_date_time' => $this->parseDateTime($data['create_date_time'] ?? null),
                    'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null),
                    'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null),
                    'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null),
                    'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null),
                    'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null),
                    'raw_data' => $data,
                ]);
            }

            // Process sanitary references
            $this->processSanitaryReferencesFromApi($certificate, $data);

            // Process products
            $this->processProductsFromApi($certificate, $data);

            // Load relationships for return
            return $certificate->load(['products', 'sanitaryReferences']);
        });
    }

    /**
     * Process sanitary references from API data
     */
    private function processSanitaryReferencesFromApi(\App\Models\ChedCertificate $certificate, array $data): void
    {




        \Log::info('Processing sanitary references from API data'.json_encode($data));
        \Log::info('references'.json_encode($data['references']));

        
        if (!isset($data['references']) || !is_array($data['references'])) {
            return;
        }

        foreach ($data['references'] as $reference) {
            // Only process sanitary certificate references (type_code 852)
            if (($reference['type_code'] ?? null) === '852' ||
                strpos($reference['id'] ?? '', 'IMPORT.EU.') === 0) {

                $sanitaryRef = $reference['id'] ?? null;
                if (!$sanitaryRef) continue;

                // Check if this sanitary reference already exists for this certificate
                $exists = \App\Models\ChedSanitaryReference::where('ched_id', $certificate->ched_id)
                    ->where('sanitary_reference', $sanitaryRef)
                    ->exists();

                if (!$exists) {
                    \App\Models\ChedSanitaryReference::create([
                        'ched_id' => $certificate->ched_id,
                        'sanitary_reference' => $sanitaryRef,
                        'type_code' => $reference['type_code'] ?? null,
                        'type_name' => $reference['type_name'] ?? null,
                        'relationship_type' => $reference['relationship_type'] ?? null,
                        'relationship_name' => $reference['relationship_name'] ?? null,
                        'scheme_agency' => $reference['scheme_agency'] ?? null,
                        'attachment_uri' => $reference['attachment'] ?? null,
                        'issue_date_time' => $this->parseDateTime($reference['issue_date_time'] ?? null),
                    ]);
                }
            }
        }
    }

    /**
     * Process products from API data
     */
    private function processProductsFromApi(\App\Models\ChedCertificate $certificate, array $data): void
    {
        if (!isset($data['commodities']) || !is_array($data['commodities'])) {
            return;
        }

        // Clear existing products for this certificate to avoid duplicates
        \App\Models\ChedProduct::where('ched_id', $certificate->ched_id)->delete();

        foreach ($data['commodities'] as $commodity) {
            $classNameEn = null;
            if (isset($commodity['class_names']['en'])) {
                $classNameEn = is_array($commodity['class_names']['en'])
                    ? implode(' | ', $commodity['class_names']['en'])
                    : $commodity['class_names']['en'];
            }

            \App\Models\ChedProduct::create([
                'ched_id' => $certificate->ched_id,
                'system_id' => $commodity['system_id'] ?? null,
                'system_name' => $commodity['system_name'] ?? null,
                'class_code' => $commodity['class_code'] ?? null,
                'class_names' => $commodity['class_names'] ?? null,
                'class_name_en' => $classNameEn,
            ]);
        }
    }

    /**
     * Parse datetime string
     */
    private function parseDateTime(?string $dateTime): ?\Carbon\Carbon
    {
        if (!$dateTime) return null;

        try {
            return \Carbon\Carbon::parse($dateTime);
        } catch (\Exception $e) {
            \Log::warning('Failed to parse datetime', ['datetime' => $dateTime]);
            return null;
        }
    }

    /**
     * Get information about the last sync
     */
    private function getLastSyncInfo(): array
    {
        $lastSync = \App\Models\ChedSyncLog::getLatestSuccessfulSync();

        if (!$lastSync) {
            return [
                'status' => 'no_sync_found',
                'message' => 'No successful sync found. Run: php artisan ched:sync --recent'
            ];
        }

        return [
            'last_sync_time' => $lastSync->sync_end_time->toISOString(),
            'last_sync_human' => $lastSync->sync_end_time->diffForHumans(),
            'certificates_in_last_sync' => $lastSync->total_certificates_fetched,
            'date_range' => $lastSync->date_range_start . ' to ' . $lastSync->date_range_end
        ];
    }

    /**
     * Get certificate data mapped for inspection form
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCertificateForInspectionForm(Request $request)
    {
        $reference = $request->input('reference');

        if (!$reference) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate reference is required',
                'error_code' => 'MISSING_REFERENCE'
            ], 400);
        }

        try {
            \Log::info('Getting certificate data for inspection form', [
                'reference' => $reference
            ]);

            // Get the raw certificate data first
            $certificateResponse = $this->getCertificateProducts($request);
            $certificateData = $certificateResponse->getData(true);

            if (!$certificateData['success']) {
                return response()->json($certificateData, $certificateResponse->getStatusCode());
            }

            // Map the data to inspection form format
            $mappedData = \App\Services\ProductInspectionMappingService::mapCertificateToInspectionForm($certificateData);

            \Log::info('Certificate data mapped for inspection form', [
                'reference' => $reference,
                'products_count' => count($mappedData['products'])
            ]);

            return response()->json([
                'success' => true,
                'reference' => $reference,
                'mapped_data' => $mappedData,
                'form_suggestions' => $this->getFormSuggestions($mappedData),
                'original_data' => $certificateData // Include original for reference
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get certificate data for inspection form', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process certificate for inspection form: ' . $e->getMessage(),
                'error_code' => 'MAPPING_FAILED'
            ], 500);
        }
    }

    /**
     * Get form suggestions based on mapped data
     */
    private function getFormSuggestions(array $mappedData): array
    {
        $suggestions = [
            'dropdown_options' => [
                'produit' => [],
                'origine_de_produit' => [],
                'lieu_de_stockage' => [],
                'nature_de_piece' => ['Sauvage', 'Élevage'],
                'type_de_produits' => ['Entier', 'Filet', 'Transformé'],
                'types_emballage' => ['Carton Master', 'Caisse', 'Sac', 'Palette'],
                'unite_de_mesure' => ['kg', 'sac', 'pièce', 'tonne']
            ],
            'pre_filled_values' => []
        ];

        // Extract unique values for dropdowns
        foreach ($mappedData['products'] as $product) {
            // Product suggestions
            if ($product['produit']['suggested_dropdown_value']) {
                $suggestions['dropdown_options']['produit'][] = $product['produit']['suggested_dropdown_value'];
            }

            // Origin suggestions
            if ($product['origine_de_produit']['suggested_dropdown_value']) {
                $suggestions['dropdown_options']['origine_de_produit'][] = $product['origine_de_produit']['suggested_dropdown_value'];
            }

            // Storage location suggestions
            if ($product['lieu_de_stockage']['suggested_dropdown_value']) {
                $suggestions['dropdown_options']['lieu_de_stockage'][] = $product['lieu_de_stockage']['suggested_dropdown_value'];
            }
        }

        // Remove duplicates
        foreach ($suggestions['dropdown_options'] as $key => $values) {
            $suggestions['dropdown_options'][$key] = array_unique($values);
        }

        // Pre-filled values for first product
        if (!empty($mappedData['products'])) {
            $firstProduct = $mappedData['products'][0];
            $suggestions['pre_filled_values'] = [
                'produit' => $firstProduct['produit']['suggested_dropdown_value'],
                'origine_de_produit' => $firstProduct['origine_de_produit']['suggested_dropdown_value'],
                'lieu_de_stockage' => $firstProduct['lieu_de_stockage']['suggested_dropdown_value'],
                'nature_de_piece' => $firstProduct['nature_de_piece']['suggested_value'],
                'type_de_produits' => $firstProduct['type_de_produits']['suggested_value'],
                'types_emballage' => $firstProduct['types_emballage']['suggested_value'],
                'unite_de_mesure' => $firstProduct['unite_de_mesure']['suggested_value']
            ];
        }

        return $suggestions;
    }

    /**
     * Extract products from TRACES API certificate data
     *
     * @param array $certificate
     * @return array
     */
    private function extractProductsFromTracesData(array $certificate)
    {
        $products = [];
        $consignments = $certificate['consignments'] ?? [];

        \Log::info('Extracting products from certificate data', [
            'consignments_count' => count($consignments),
            'certificate_keys' => array_keys($certificate)
        ]);

        foreach ($consignments as $consignmentIndex => $consignment) {
            $consignmentItems = $consignment['consignment_items'] ?? [];

            \Log::info("Processing consignment $consignmentIndex", [
                'consignment_items_count' => count($consignmentItems),
                'consignment_keys' => array_keys($consignment)
            ]);

            foreach ($consignmentItems as $itemIndex => $item) {
                $sequence = $item['sequence_numeric'] ?? count($products) + 1;
                $classifications = $item['classifications'] ?? [];

                \Log::info("Processing item $itemIndex", [
                    'sequence' => $sequence,
                    'classifications_count' => count($classifications),
                    'item_keys' => array_keys($item)
                ]);

                // Skip sequence 0 as it's usually the totals/summary
                if ($sequence == 0) {
                    \Log::info("Skipping sequence 0 (totals/summary)");
                    continue;
                }

                foreach ($classifications as $classIndex => $classification) {
                    \Log::info("Processing classification $classIndex", [
                        'system_id' => $classification['system_id'] ?? null,
                        'class_code' => $classification['class_code'] ?? null
                    ]);

                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => $classification['system_id'] ?? null,
                        'system_name' => $classification['system_name'] ?? null,
                        'classification_code' => $classification['class_code'] ?? null,
                        'description' => $classification['class_names']['en'] ?? null,
                        'descriptions' => $classification['class_names'] ?? [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }

                // If no classifications found, still add the item with basic info
                if (empty($classifications)) {
                    \Log::info("No classifications found for item, adding basic info");
                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => null,
                        'system_name' => null,
                        'classification_code' => null,
                        'description' => $item['description'] ?? 'No classification data available',
                        'descriptions' => [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }
            }
        }

        \Log::info('Product extraction completed', ['products_count' => count($products)]);
        return $products;
    }

    /**
     * Test complete certificate information retrieval
     * This endpoint tests the new complete certificate retrieval methods
     *
     * GET /api/certificates/test-complete?reference=IMPORT.EU.MR.2025.0003940
     */
    public function testCompleteCertificateRetrieval(Request $request)
    {
        $reference = $request->query('reference');

        if (!$reference) {
            return response()->json([
                'success' => false,
                'message' => 'Reference parameter is required',
                'usage' => 'GET /api/certificates/test-complete?reference=IMPORT.EU.MR.2025.0003940'
            ], 400);
        }

        try {
            // Get TRACES configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                throw new \Exception('TRACES API configuration not found');
            }

            // Create TracesNtChedClient
            $client = new \App\Providers\TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $results = [];
            $startTime = microtime(true);

            // Test 1: Direct URL access
            try {
                $urlResult = $client->testDirectCertificateUrl($reference);
                $results['direct_url_access'] = [
                    'success' => true,
                    'data' => $urlResult
                ];
            } catch (\Exception $e) {
                $results['direct_url_access'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            // Test 2: Smart certificate lookup
            try {
                $smartResult = $client->getCertificateByAnyReference($reference);
                $results['smart_lookup'] = [
                    'success' => true,
                    'data' => $smartResult,
                    'is_array' => is_array($smartResult) && isset($smartResult[0])
                ];
            } catch (\Exception $e) {
                $results['smart_lookup'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            // Test 3: Complete certificate information
            try {
                $completeResult = $client->getCompleteCertificateInfo($reference);
                $results['complete_info'] = [
                    'success' => true,
                    'data' => $completeResult,
                    'count' => count($completeResult)
                ];
            } catch (\Exception $e) {
                $results['complete_info'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            // Test 4: Basic search (for comparison)
            try {
                $basicResult = $client->findChedCertificatesBySanitaryReference($reference);
                $results['basic_search'] = [
                    'success' => true,
                    'data' => $basicResult,
                    'count' => count($basicResult)
                ];
            } catch (\Exception $e) {
                $results['basic_search'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds

            return response()->json([
                'success' => true,
                'reference' => $reference,
                'execution_time_ms' => $executionTime,
                'test_results' => $results,
                'summary' => [
                    'direct_url_works' => $results['direct_url_access']['success'] ?? false,
                    'smart_lookup_works' => $results['smart_lookup']['success'] ?? false,
                    'complete_info_works' => $results['complete_info']['success'] ?? false,
                    'basic_search_works' => $results['basic_search']['success'] ?? false,
                ],
                'recommendations' => $this->generateRecommendations($results),
                'debug_files' => [
                    'direct_response' => 'storage/logs/direct_certificate_response.html',
                    'certificate_response' => 'storage/logs/traces_certificate_response.xml',
                    'raw_response' => 'raw_response.xml'
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'reference' => $reference,
                'error' => $e->getMessage(),
                'message' => 'Failed to test certificate retrieval methods'
            ], 500);
        }
    }

    /**
     * Generate recommendations based on test results
     */
    private function generateRecommendations(array $results): array
    {
        $recommendations = [];

        if ($results['direct_url_access']['success'] ?? false) {
            $urlData = $results['direct_url_access']['data'];
            if ($urlData['http_code'] === 200) {
                $recommendations[] = "✓ Direct URL access works! This could be used for web scraping or direct data access.";
            } elseif ($urlData['http_code'] === 401 || $urlData['http_code'] === 403) {
                $recommendations[] = "⚠ Direct URL requires authentication. Consider implementing authentication for direct access.";
            }
        }

        if ($results['smart_lookup']['success'] ?? false) {
            $recommendations[] = "✓ Smart lookup method works! This is the most efficient method for single certificate retrieval.";
        }

        if ($results['complete_info']['success'] ?? false) {
            $recommendations[] = "✓ Complete certificate info method works! This provides the most comprehensive data including attachments.";
        }

        if ($results['basic_search']['success'] ?? false) {
            $recommendations[] = "✓ Basic search method works as fallback.";
        }

        if (empty($recommendations)) {
            $recommendations[] = "⚠ All methods failed. Check TRACES API credentials and network connectivity.";
        }

        return $recommendations;
    }
}