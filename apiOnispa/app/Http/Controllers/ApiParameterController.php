<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class ApiParameterController extends Controller
{
    public function index()
    {
        $parameters = [
            'username' => 'n00385tm',
            'auth_key' => '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',
            'client_id' => 'onispa-mr',
            'use_production' => true,
            'endpoint_production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'endpoint_training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];
        
        return view('api-parameters.index', compact('parameters'));
    }

    public function update(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|string',
            'auth_key' => 'required|string',
            'client_id' => 'required|string',
            'use_production' => 'boolean',
        ]);

        // In a real application, you would save these to a configuration file or database
        // For now, we'll just log the update
        \Log::info('API parameters updated', $validated);

        return redirect()->route('api-parameters')->with('success', 'API parameters updated successfully.');
    }
} 