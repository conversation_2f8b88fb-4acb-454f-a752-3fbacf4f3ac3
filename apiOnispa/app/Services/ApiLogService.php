<?php

namespace App\Services;

use App\Models\ApiLog;
use Illuminate\Http\Request;

class ApiLogService
{
    public static function logTracesApiCall($startDate, $endDate, $certificates, $duration, $success = true, $errorMessage = null, $statusCode = null)
    {
        return ApiLog::create([
            'type' => 'traces_outgoing',
            'endpoint' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'method' => 'POST',
            'request_data' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'certificate_type' => 'P',
                'status' => '70',
                'soap_action' => 'findChedCertificate'
            ],
            'response_data' => [
                'certificates_count' => count($certificates),
                'certificates' => array_slice($certificates, 0, 5), // Store first 5 for reference
                'total_returned' => count($certificates)
            ],
            'status_code' => $statusCode,
            'status_message' => $success ? 'Success' : 'Failed',
            'duration_ms' => $duration,
            'date_range_start' => $startDate,
            'date_range_end' => $endDate,
            'certificates_count' => count($certificates),
            'error_message' => $errorMessage,
            'success' => $success
        ]);
    }

    public static function logIncomingApiCall(Request $request, $response, $duration, $success = true, $errorMessage = null)
    {
        return ApiLog::create([
            'type' => 'our_incoming',
            'endpoint' => $request->fullUrl(),
            'method' => $request->method(),
            'request_data' => [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'query' => $request->query(),
                'ip' => $request->ip()
            ],
            'response_data' => [
                'status_code' => $response->getStatusCode(),
                'content' => $response->getContent()
            ],
            'status_code' => $response->getStatusCode(),
            'status_message' => $success ? 'Success' : 'Failed',
            'duration_ms' => $duration,
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'error_message' => $errorMessage,
            'success' => $success
        ]);
    }

    public static function getTracesApiStats($days = 30)
    {
        $logs = ApiLog::tracesOutgoing()->recent($days);
        
        return [
            'total_calls' => $logs->count(),
            'successful_calls' => $logs->successful()->count(),
            'failed_calls' => $logs->failed()->count(),
            'total_certificates_fetched' => $logs->sum('certificates_count'),
            'average_duration' => $logs->avg('duration_ms'),
            'success_rate' => $logs->count() > 0 ? round(($logs->successful()->count() / $logs->count()) * 100, 2) : 0
        ];
    }

    public static function getIncomingApiStats($days = 30)
    {
        $logs = ApiLog::ourIncoming()->recent($days);
        
        return [
            'total_requests' => $logs->count(),
            'successful_requests' => $logs->successful()->count(),
            'failed_requests' => $logs->failed()->count(),
            'average_duration' => $logs->avg('duration_ms'),
            'success_rate' => $logs->count() > 0 ? round(($logs->successful()->count() / $logs->count()) * 100, 2) : 0,
            'top_endpoints' => $logs->selectRaw('endpoint, COUNT(*) as count')
                ->groupBy('endpoint')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get()
        ];
    }
} 