<?php

namespace App\Services;

use SimpleXMLElement;
use Exception;

/**
 * Standalone EU Import Service for Legacy Code
 * 
 * This class provides a complete solution for fetching EU import certificates
 * from TRACES API and parsing products without any database dependencies.
 * Perfect for integration with legacy systems.
 */
class EuImportLegacyService
{
    // Hardcoded TRACES API credentials
    private const TRACES_USERNAME = 'n00385tm';
    private const TRACES_AUTH_KEY = '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh';
    private const TRACES_CLIENT_ID = 'onispa-mr';
    private const USE_PRODUCTION = true;
    private const TIMEOUT = 60;
    
    private $endpoint;
    
    public function __construct()
    {
        $this->endpoint = self::USE_PRODUCTION
            ? 'https://webgate.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01'
            : 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/EuImportCertificateServiceV01';
    }
    
    /**
     * Fetch EU import certificate and parse products by reference
     * 
     * @param string $reference The EU-Import reference (e.g., "IMPORT.EU.MR.2025.0003940")
     * @return array Array containing certificate data and parsed products
     * @throws Exception If certificate not found or API error
     */
    public function fetchAndParseProducts(string $reference): array
    {
        // Fetch certificate from TRACES API
        $certificateData = $this->fetchCertificateFromApi($reference);
        
        if (!$certificateData || !isset($certificateData['raw_xml'])) {
            throw new Exception("Certificate not found or invalid response for reference: {$reference}");
        }
        
        // Parse products from XML
        $products = $this->parseProductsFromXml($certificateData['raw_xml'], $reference);
        
        return [
            'success' => true,
            'reference' => $reference,
            'certificate' => $certificateData,
            'products' => $products,
            'products_count' => count($products),
            'total_weight' => array_sum(array_column($products, 'net_weight')),
            'total_packages' => array_sum(array_column($products, 'package_quantity'))
        ];
    }

    /**
     * Get products formatted for form population
     * 
     * @param string $reference The EU-Import reference
     * @return array Array of products formatted for form fields
     */
    public function getProductsForForms(string $reference): array
    {
        $result = $this->fetchAndParseProducts($reference);
        $formattedProducts = [];
        
        foreach ($result['products'] as $product) {
            $formattedProducts[] = [
                'id' => $product['sequence_number'],
                'product_name' => $product['common_name'] ?: $product['scientific_name'],
                'scientific_name' => $product['scientific_name'],
                'common_name' => $product['common_name'],
                'origin_country' => $product['origin_country_name'],
                'storage_location' => $product['processing_plant_name'],
                'nature' => $product['nature_description'],
                'product_type' => $product['treatment_description'],
                'packaging_type' => $product['package_type_name'],
                'unit' => $product['net_weight_unit'],
                'quantity' => $product['net_weight'],
                'package_count' => $product['package_quantity'],
                'batch_id' => $product['production_batch_id'],
                'fao_code' => $product['fao_code'],
                'cn_code' => $product['cn_code'],
                'is_wild' => $product['is_wild_stock'],
                'collection_date' => $product['collection_date'],
                'processing_plant' => [
                    'id' => $product['processing_plant_id'],
                    'name' => $product['processing_plant_name']
                ],
                'processes' => $product['processes']
            ];
        }
        
        return [
            'success' => true,
            'reference' => $reference,
            'products' => $formattedProducts,
            'summary' => [
                'total_products' => count($formattedProducts),
                'total_weight' => $result['total_weight'],
                'total_packages' => $result['total_packages']
            ]
        ];
    }
    
    /**
     * Search for multiple certificates and get their products
     * 
     * @param array $references Array of EU-Import references
     * @return array Results for each reference
     */
    public function searchMultipleReferences(array $references): array
    {
        $results = [];
        
        foreach ($references as $reference) {
            try {
                $results[$reference] = $this->getProductsForForms($reference);
            } catch (Exception $e) {
                $results[$reference] = [
                    'success' => false,
                    'reference' => $reference,
                    'error' => $e->getMessage(),
                    'products' => []
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Fetch certificate from TRACES API
     */
    private function fetchCertificateFromApi(string $reference): ?array
    {
        $soapRequest = $this->buildSoapRequest($reference);
        
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "getEuImportCertificate"',
            'Content-Length: ' . strlen($soapRequest),
            'User-Agent: ONISPA-Legacy-EuImport-Client/1.0'
        ];
        
        $ch = curl_init($this->endpoint);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => self::TIMEOUT,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'ONISPA-Legacy-EuImport-Client/1.0',
            CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

      
        
        if ($curlError) {
            throw new Exception("CURL Error: {$curlError}");
        }
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP Error {$httpCode}: Failed to fetch certificate");
        }
        
        return $this->parseSoapResponse($response);
    }
    
    /**
     * Build SOAP request for getting certificate
     */
    private function buildSoapRequest(string $reference): string
    {
        // Set UTC timezone for consistent timestamp generation
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        // Get current time and apply timestamp adjustments for TRACES API synchronization
        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // TRACES API timestamp synchronization fixes:
        // 1. Subtract a few seconds to account for network delay and server time drift
        $now->modify('-10 seconds');

        // 2. Use longer expiration time (15 minutes instead of 5) as suggested in docs
        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+15 minutes')->format('Y-m-d\TH:i:s\Z');

        // Create password digest: Base64(SHA1(nonce + created + password))
        $passwordDigest = base64_encode(sha1($nonceRaw . $created . self::TRACES_AUTH_KEY, true));

        $body = '<euimport:GetEuImportCertificateRequest xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01">
            <euimport:ID>' . htmlspecialchars($reference) . '</euimport:ID>
        </euimport:GetEuImportCertificateRequest>';

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:v3="http://ec.europa.eu/sanco/tracesnt/base/v3"
    xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars(self::TRACES_USERNAME) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <v3:LanguageCode>en</v3:LanguageCode>
        <v3:WebServiceClientId>' . htmlspecialchars(self::TRACES_CLIENT_ID) . '</v3:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        ' . $body . '
    </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * Parse SOAP response and extract certificate data
     */
    private function parseSoapResponse(string $response): ?array
    {
        try {


            try {
               
                $dom = new \DOMDocument();
                $dom->loadXML($response);
                $xpath = new \DOMXPath($dom);
                $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
                $xpath->registerNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
                $xpath->registerNamespace('ns3', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
                $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
                $xpath->registerNamespace('ns5', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
                $xpath->registerNamespace('ns6', 'http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01');

                // Check for SOAP faults
                $faultNodes = $xpath->query('//soap:Fault | //S:Fault');
                if ($faultNodes->length > 0) {
                    $fault = $faultNodes->item(0);
                    $faultString = $fault->getElementsByTagName('faultstring')->item(0);
                    $msg = $faultString ? $faultString->nodeValue : 'Unknown SOAP Fault';
                    throw new Exception("SOAP Fault: " . $msg);
                }

                // Find the SPSCertificate element
                $certificateNodes = $xpath->query('//ns6:GetEuImportCertificateResponse/ns6:SPSCertificate | //ns5:SPSCertificate | //ns6:SPSCertificate');
                if ($certificateNodes->length === 0) {
                    throw new Exception("No certificate found in SOAP response");
                }
                $certificateNode = $certificateNodes->item(0);
                $certificateXml = $dom->saveXML($certificateNode);

                \Log::info('TRACES XML', [
                    'certificateXml' => $certificateXml
                ]);
                return [
                    'raw_xml' => $certificateXml
                ];
            } catch (Exception $e) {
                throw new Exception("Failed to parse SOAP response: " . $e->getMessage());
            }


            return [
                'raw_xml' => $certificateXml,
                'issue_date_time' => $this->extractIssueDateFromXml($certificateXml),
                'type_code' => 'EU_IMPORT',
                'purpose_code' => null,
                'status' => 'VALIDATED',
                'consignments' => $this->extractConsignmentsFromXml($certificateXml)
            ];

        } catch (Exception $e) {
            throw new Exception("Failed to parse SOAP response: " . $e->getMessage());
        }
    }




    public function parseProductsFromXml(string $xmlContent, string $importId): array
{
    try {
        $xml = new SimpleXMLElement($xmlContent, LIBXML_NOERROR | LIBXML_NOWARNING | LIBXML_PARSEHUGE);
        $this->registerNamespaces($xml);

        $products = [];
        // FIX: use sps namespace instead of ns4
        $tradeLineItems = $xml->xpath('//sps:IncludedSPSTradeLineItem');

        if (empty($tradeLineItems)) {
            \Log::warning("No trade line items found in legacy parser", ['import_id' => $importId]);
            return $products;
        }

        foreach ($tradeLineItems as $item) {
            // FIX: children are in ram namespace, not ns4
            $sequenceNumber = (int) ($item->xpath('ram:SequenceNumeric')[0] ?? 0);

            if ($sequenceNumber === 0) {
                continue;
            }

            $productData = $this->parseProductData($item, $importId);
            if ($productData) {
                $products[] = $productData;
            }
        }

        return $products;

    } catch (Exception $e) {
        throw new Exception("Failed to parse products from XML: " . $e->getMessage());
    }
}

private function registerNamespaces(SimpleXMLElement $xml): void
{
    $xml->registerXPathNamespace('sps', 'urn:un:unece:uncefact:data:standard:SPSCertificate:17');
    $xml->registerXPathNamespace('ram', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
    $xml->registerXPathNamespace('udt', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
}

    /**
     * Parse individual product data from XML element
     */
    private function parseProductData(SimpleXMLElement $item, string $importId): ?array
    {
        try {


            $sequenceNumber = (int) ($item->xpath('ram:SequenceNumeric')[0] ?? 0);
$scientificName = (string) ($item->xpath('ram:ScientificName[@languageID="la"]')[0] ?? '');
$productionBatchId = (string) ($item->xpath('ram:ProductionBatchID')[0] ?? '');
$netWeightElements = $item->xpath('ram:NetWeightMeasure');


            // Get weight information
            $netWeight = null;
            $netWeightUnit = null;

            if (!empty($netWeightElements)) {
                $netWeight = (float) $netWeightElements[0];
                $netWeightUnit = (string) $netWeightElements[0]['unitCode'] ?? 'KGM';
            }

            // Parse classifications
            $classifications = $this->parseClassifications($item);

            // Extract specific classification data
            $cnCode = $classifications['CN']['code'] ?? null;
            $cnDescription = $classifications['CN']['description'] ?? null;
            $faoCode = $classifications['FAO_ASFIS']['code'] ?? null;
            $faoDescription = $classifications['FAO_ASFIS']['description'] ?? null;
            $natureOfCommodity = $classifications['NATURE_OF_COMMODITY']['code'] ?? null;
            $natureDescription = $classifications['NATURE_OF_COMMODITY']['description'] ?? null;
            $treatmentType = $classifications['TREATMENT_TYPE']['code'] ?? null;
            $treatmentDescription = $classifications['TREATMENT_TYPE']['description'] ?? null;
            $forFinalConsumer = ($classifications['FINAL_CONSUMER']['code'] ?? 'FALSE') === 'TRUE';
            $isWildStock = $natureOfCommodity === 'WILD_STOCK';

            // Get common name from FAO description or scientific name
            $commonName = $this->extractCommonName($faoDescription, $scientificName);

            // Parse packaging information
            $packageData = $this->parsePackagingData($item);

            // Parse processes
            $processes = $this->parseProcesses($item);

            // Extract collection date and processing plant info
            $collectionDate = $this->extractCollectionDate($processes);
            $processingPlant = $this->extractProcessingPlant($processes);

            return [
                'import_id' => $importId,
                'sequence_number' => $sequenceNumber,
                'scientific_name' => $scientificName,
                'common_name' => $commonName,
                'production_batch_id' => $productionBatchId,
                'net_weight' => $netWeight,
                'net_weight_unit' => $netWeightUnit,
                'gross_weight' => null,
                'gross_weight_unit' => null,
                'cn_code' => $cnCode,
                'cn_description' => $cnDescription,
                'fao_code' => $faoCode,
                'fao_description' => $faoDescription,
                'nature_of_commodity' => $natureOfCommodity,
                'nature_description' => $natureDescription,
                'treatment_type' => $treatmentType,
                'treatment_description' => $treatmentDescription,
                'is_wild_stock' => $isWildStock,
                'for_final_consumer' => $forFinalConsumer,
                'package_type_code' => $packageData['type_code'] ?? null,
                'package_type_name' => $packageData['type_name'] ?? null,
                'package_level_code' => $packageData['level_code'] ?? null,
                'package_level_name' => $packageData['level_name'] ?? null,
                'package_quantity' => $packageData['quantity'] ?? null,
                'processes' => $processes,
                'collection_date' => $collectionDate,
                'processing_plant_id' => $processingPlant['id'] ?? null,
                'processing_plant_name' => $processingPlant['name'] ?? null,
                'origin_country_id' => 'MR',
                'origin_country_name' => 'Mauritania',
                'all_classifications' => $classifications,
                'raw_data' => $this->elementToArray($item)
            ];

        } catch (Exception $e) {
            error_log("Failed to parse product data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Parse classifications from XML element
     */
    private function parseClassifications(SimpleXMLElement $item): array
    {
        $classifications = [];

        $classificationElements = $item->xpath('ns4:ApplicableSPSClassification');

        foreach ($classificationElements as $classification) {
            $systemId = (string) ($classification->xpath('ns4:SystemID')[0] ?? '');
            $systemName = (string) ($classification->xpath('ns4:SystemName')[0] ?? '');
            $classCode = (string) ($classification->xpath('ns4:ClassCode')[0] ?? '');
            $className = (string) ($classification->xpath('ns4:ClassName')[0] ?? '');

            if ($systemId) {
                $classifications[$systemId] = [
                    'system_id' => $systemId,
                    'system_name' => $systemName,
                    'code' => $classCode,
                    'description' => $className,
                    'all_names' => ['en' => $className]
                ];
            }
        }

        return $classifications;
    }

    /**
     * Parse packaging data from XML element
     */
    private function parsePackagingData(SimpleXMLElement $item): array
    {
        $packageData = [];

        $packageElements = $item->xpath('ns4:AppliedSPSProcess/ns4:UsedSPSPackaging');

        if (!empty($packageElements)) {
            $package = $packageElements[0];

            $packageData['type_code'] = (string) ($package->xpath('ns4:TypeCode')[0] ?? '');
            $packageData['type_name'] = (string) ($package->xpath('ns4:TypeName')[0] ?? '');
            $packageData['level_code'] = (string) ($package->xpath('ns4:LevelCode')[0] ?? '');
            $packageData['level_name'] = (string) ($package->xpath('ns4:LevelName')[0] ?? '');

            $quantityElements = $package->xpath('ns4:ItemQuantity');
            if (!empty($quantityElements)) {
                $packageData['quantity'] = (float) $quantityElements[0];
            }
        }

        return $packageData;
    }

    /**
     * Parse processes from XML element
     */
    private function parseProcesses(SimpleXMLElement $item): array
    {
        $processes = [];

        $processElements = $item->xpath('ns4:AppliedSPSProcess');

        foreach ($processElements as $process) {
            $typeCode = (string) ($process->xpath('ns4:TypeCode')[0] ?? '');
            $typeName = (string) ($process->xpath('ns4:TypeName')[0] ?? '');

            $startDateTime = null;
            $startDateElements = $process->xpath('ns4:CompletionSPSPeriod/ns4:StartDateTime/ns3:DateTime');
            if (!empty($startDateElements)) {
                $startDateTime = (string) $startDateElements[0];
            }

            // Get operator information if available
            $operator = null;
            $operatorElements = $process->xpath('ns4:OperatorSPSParty');
            if (!empty($operatorElements)) {
                $operatorParty = $operatorElements[0];
                $operator = [
                    'id' => (string) ($operatorParty->xpath('ns4:ID')[0] ?? ''),
                    'name' => (string) ($operatorParty->xpath('ns4:Name')[0] ?? ''),
                    'type' => (string) ($operatorParty->xpath('ns4:TypeCode[@listID="operator_activity_type"]')[0] ?? ''),
                    'classification' => (string) ($operatorParty->xpath('ns4:TypeCode[@listID="classification_section_code"]')[0] ?? '')
                ];
            }

            $processes[] = [
                'type_code' => $typeCode,
                'type_name' => $typeName,
                'start_date_time' => $startDateTime,
                'operator' => $operator
            ];
        }

        return $processes;
    }

    /**
     * Extract common name from FAO description or scientific name
     */
    private function extractCommonName(?string $faoDescription, string $scientificName): string
    {
        if (!$faoDescription || $faoDescription === $scientificName) {
            // Try to extract common name from scientific name mapping
            $commonNames = [
                'Epinephelus aeneus' => 'White grouper',
                'Epinephelus marginatus' => 'Dusky grouper',
                'Pagrus auriga' => 'Redbanded seabream',
                'Epinephelus costae' => 'Goldblotch grouper',
                'Pagrus caeruleostictus' => 'Bluespotted seabream',
                'Mycteroperca rubra' => 'Mottled grouper',
                'Solea senegalensis' => 'Senegalese sole',
                'Pseudupeneus prayensis' => 'West African goatfish'
            ];

            return $commonNames[$scientificName] ?? $scientificName;
        }

        return $faoDescription;
    }

    /**
     * Extract collection date from processes
     */
    private function extractCollectionDate(array $processes): ?string
    {
        foreach ($processes as $process) {
            if ($process['type_code'] === '33' && $process['start_date_time']) {
                return $process['start_date_time'];
            }
        }

        return null;
    }

    /**
     * Extract processing plant information from processes
     */
    private function extractProcessingPlant(array $processes): array
    {
        foreach ($processes as $process) {
            if (in_array($process['type_code'], ['37', '43']) && $process['operator']) {
                return $process['operator'];
            }
        }

        return [];
    }

    /**
     * Convert XML element to array for raw_data storage
     */
    private function elementToArray(SimpleXMLElement $element): array
    {
        return json_decode(json_encode($element), true);
    }

    /**
     * Extract issue date from XML
     */
    private function extractIssueDateFromXml(string $xmlContent): ?string
    {
        try {

                \Log::info('TRACES XML', [
                'xmlContent' => $xmlContent
            ]);

            $xml = new SimpleXMLElement($xmlContent, LIBXML_NOERROR | LIBXML_NOWARNING | LIBXML_PARSEHUGE);


            \Log::info('Before Namespaces', [
                'xml' => $xml
            ]);
            
            $namespaces = $xml->getDocNamespaces(true);


            \Log::info('Namespaces', [
                'namespaces' => $namespaces
            ]);

            $this->registerNamespaces($xml);

            $issueDateElements = $xml->xpath('//ns4:IssueDateTime/ns3:DateTime');
            if (!empty($issueDateElements)) {
                return (string) $issueDateElements[0];
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Extract consignments from XML (placeholder for compatibility)
     */
    private function extractConsignmentsFromXml(string $xmlContent): array
    {
        // This is a placeholder method for compatibility with existing code
        // The actual product parsing is done in parseProductsFromXml
        return [];
    }
}
