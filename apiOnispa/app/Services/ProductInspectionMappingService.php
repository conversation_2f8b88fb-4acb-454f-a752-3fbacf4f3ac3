<?php

namespace App\Services;

class ProductInspectionMappingService
{
    /**
     * Map CHED certificate data to inspection form format
     */
    public static function mapCertificateToInspectionForm(array $certificateData): array
    {
        $mappedData = [
            'certificate_info' => [
                'ched_id' => $certificateData['ched_id'] ?? null,
                'certificate_reference' => $certificateData['certificate_reference'] ?? null,
                'source' => $certificateData['source'] ?? null,
            ],
            'products' => []
        ];

        // Map each product
        if (isset($certificateData['products']) && is_array($certificateData['products'])) {
            foreach ($certificateData['products'] as $product) {
                $mappedProduct = self::mapSingleProduct($product, $certificateData);
                if ($mappedProduct) {
                    $mappedData['products'][] = $mappedProduct;
                }
            }
        }

        // Add certificate-level information
        $mappedData['certificate_details'] = self::mapCertificateDetails($certificateData);

        return $mappedData;
    }

    /**
     * Map a single product to inspection form format
     */
    private static function mapSingleProduct(array $product, array $certificateData): ?array
    {
        return [
            // Product identification
            'produit' => [
                'code' => $product['class_code'] ?? null,
                'name_en' => $product['class_name_en'] ?? null,
                'name_fr' => self::translateProductName($product['class_name_en'] ?? ''),
                'system_id' => $product['system_id'] ?? null,
                'system_name' => $product['system_name'] ?? null,
                'suggested_dropdown_value' => self::mapToFrenchProductDropdown($product['class_name_en'] ?? '')
            ],

            // Origin information (from certificate)
            'origine_de_produit' => [
                'country_code' => $certificateData['certificate_info']['country_of_dispatch'] ?? null,
                'country_name' => self::getCountryName($certificateData['certificate_info']['country_of_dispatch'] ?? ''),
                'suggested_dropdown_value' => self::mapCountryToDropdown($certificateData['certificate_info']['country_of_dispatch'] ?? '')
            ],

            // Storage location (from certificate consignee info)
            'lieu_de_stockage' => [
                'consignee_name' => $certificateData['certificate_info']['consignee_name'] ?? null,
                'suggested_dropdown_value' => self::mapConsigneeToStorageLocation($certificateData['certificate_info']['consignee_name'] ?? '')
            ],

            // Document/piece nature
            'nature_de_piece' => [
                'suggested_value' => 'Sauvage', // Default for fish certificates
                'certificate_type' => $certificateData['certificate_info']['type'] ?? null
            ],

            // Product type
            'type_de_produits' => [
                'suggested_value' => self::mapProductType($product['class_name_en'] ?? ''),
                'options' => ['Entier', 'Filet', 'Transformé']
            ],

            // Packaging
            'types_emballage' => [
                'suggested_value' => 'Carton Master', // Default
                'options' => ['Carton Master', 'Caisse', 'Sac', 'Palette']
            ],

            // Measurements (these would need to be filled manually or from other sources)
            'unite_de_mesure' => [
                'suggested_value' => 'kg',
                'options' => ['kg', 'sac', 'pièce', 'tonne']
            ],

            'quantite' => [
                'suggested_value' => null, // Not available in CHED data
                'note' => 'À remplir manuellement'
            ],

            'nombre_de_colis' => [
                'suggested_value' => null, // Not available in CHED data  
                'note' => 'À remplir manuellement'
            ]
        ];
    }

    /**
     * Map certificate-level details
     */
    private static function mapCertificateDetails(array $certificateData): array
    {
        return [
            'consignor' => [
                'name' => $certificateData['certificate_info']['consignor_name'] ?? null,
                'country' => $certificateData['certificate_info']['country_of_dispatch'] ?? null
            ],
            'consignee' => [
                'name' => $certificateData['certificate_info']['consignee_name'] ?? null,
                'country' => $certificateData['certificate_info']['country_of_entry'] ?? null
            ],
            'status' => $certificateData['certificate_info']['status'] ?? null,
            'update_date' => $certificateData['certificate_info']['update_date_time'] ?? null,
            'sanitary_references' => $certificateData['sanitary_references'] ?? []
        ];
    }

    /**
     * Map English product names to French dropdown options
     */
    private static function mapToFrenchProductDropdown(string $englishName): ?string
    {
        $productMapping = [
            // Fish species mapping
            'Other fish' => 'Autres poissons',
            'Octopus' => 'Tako - Octopus vulgaris',
            'Corvina' => 'Corvina (Argyrosomus regius)',
            'Mackerel' => 'Maquereau (Arius heudeloti)',
            'Sole' => 'Sole (Alosa alosa)',
            'Sardine' => 'Sardine',
            'Tuna' => 'Thon',
            'Salmon' => 'Saumon',
            'Cod' => 'Cabillaud',
            'Hake' => 'Merlu',
            'Sea bream' => 'Daurade',
            'Sea bass' => 'Bar',
            'Shrimp' => 'Crevette',
            'Crab' => 'Crabe',
            'Lobster' => 'Homard',
            'Squid' => 'Calmar',
            'Cuttlefish' => 'Seiche'
        ];

        // Try exact match first
        if (isset($productMapping[$englishName])) {
            return $productMapping[$englishName];
        }

        // Try partial matches
        foreach ($productMapping as $english => $french) {
            if (stripos($englishName, $english) !== false) {
                return $french;
            }
        }

        return null;
    }

    /**
     * Translate product names to French
     */
    private static function translateProductName(string $englishName): string
    {
        $translations = [
            'Other fish' => 'Autres poissons',
            'Fish' => 'Poisson',
            'Octopus' => 'Poulpe',
            'Corvina' => 'Corvina',
            'Mackerel' => 'Maquereau',
            'Sole' => 'Sole',
            'Sardine' => 'Sardine',
            'Tuna' => 'Thon',
            'Salmon' => 'Saumon'
        ];

        return $translations[$englishName] ?? $englishName;
    }

    /**
     * Map country codes to names
     */
    private static function getCountryName(string $countryCode): string
    {
        $countries = [
            'MR' => 'Mauritanie',
            'FR' => 'France',
            'ES' => 'Espagne',
            'IT' => 'Italie',
            'PT' => 'Portugal',
            'MA' => 'Maroc',
            'SN' => 'Sénégal',
            'GN' => 'Guinée'
        ];

        return $countries[$countryCode] ?? $countryCode;
    }

    /**
     * Map country to dropdown values
     */
    private static function mapCountryToDropdown(string $countryCode): ?string
    {
        // This would map to your actual dropdown options
        $mapping = [
            'MR' => 'MAURITANIE',
            'FR' => 'FRANCE', 
            'ES' => 'ESPAGNE',
            'IT' => 'ITALIE',
            'PT' => 'PORTUGAL',
            'MA' => 'MAROC',
            'SN' => 'SENEGAL'
        ];

        return $mapping[$countryCode] ?? null;
    }

    /**
     * Map consignee to storage location
     */
    private static function mapConsigneeToStorageLocation(string $consigneeName): ?string
    {
        // Map known consignees to storage locations
        $storageMapping = [
            'FIORITAL SPA' => 'DEWLA SEAFOOD',
            'BEST FRIGO' => 'BEST FRIGO',
            'COPMER SARL' => 'DEWLA SEAFOOD'
        ];

        foreach ($storageMapping as $consignee => $storage) {
            if (stripos($consigneeName, $consignee) !== false) {
                return $storage;
            }
        }

        return null;
    }

    /**
     * Map product type based on English name
     */
    private static function mapProductType(string $englishName): string
    {
        if (stripos($englishName, 'fillet') !== false || stripos($englishName, 'filet') !== false) {
            return 'Filet';
        }
        
        if (stripos($englishName, 'processed') !== false || stripos($englishName, 'prepared') !== false) {
            return 'Transformé';
        }

        return 'Entier'; // Default
    }

    /**
     * Get form-ready data for frontend
     */
    public static function getFormReadyData(string $reference): array
    {
        try {
            // This would call your API internally
            $apiUrl = config('app.url') . "/api/certificates/products?reference=" . urlencode($reference);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception("API returned HTTP {$httpCode}");
            }

            $data = json_decode($response, true);
            
            if (!$data || !$data['success']) {
                throw new \Exception($data['message'] ?? 'API call failed');
            }

            return self::mapCertificateToInspectionForm($data);

        } catch (\Exception $e) {
            return [
                'error' => true,
                'message' => $e->getMessage(),
                'products' => []
            ];
        }
    }
}
