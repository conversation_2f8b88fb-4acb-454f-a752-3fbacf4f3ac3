<?php

namespace App\Services;

use App\Models\ChedCertificate;
use App\Models\ChedSanitaryReference;
use App\Models\ChedProduct;
use App\Models\ChedSyncLog;
use App\Providers\TracesNtChedClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ChedSyncService
{
    private TracesNtChedClient $client;
    private ChedSyncLog $syncLog;
    private int $newCertificates = 0;
    private int $updatedCertificates = 0;
    private int $sanitaryReferencesAdded = 0;
    private int $productsAdded = 0;
    private array $apiCalls = [];

    public function __construct()
    {
        $tracesConfig = config('services.traces');
        $this->client = new TracesNtChedClient(
            $tracesConfig['username'],
            $tracesConfig['auth_key'],
            $tracesConfig['client_id'],
            $tracesConfig['use_production']
        );
    }

    /**
     * Sync certificates for a date range
     */
    public function syncCertificates(string $startDate, string $endDate): ChedSyncLog
    {
        $this->initializeSyncLog($startDate, $endDate);

        try {
            Log::info('Starting CHED certificate sync', [
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);

            $allCertificates = $this->fetchAllCertificates($startDate, $endDate);
            
            Log::info('Fetched certificates from TRACES API', [
                'total_certificates' => count($allCertificates)
            ]);

            $this->processCertificates($allCertificates);
            
            $this->completeSyncLog();
            
            Log::info('CHED certificate sync completed successfully', [
                'new_certificates' => $this->newCertificates,
                'updated_certificates' => $this->updatedCertificates,
                'sanitary_references' => $this->sanitaryReferencesAdded,
                'products' => $this->productsAdded
            ]);

        } catch (\Exception $e) {
            $this->failSyncLog($e);
            Log::error('CHED certificate sync failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }

        return $this->syncLog;
    }

    /**
     * Sync recent certificates (last 2 days)
     */
    public function syncRecentCertificates(): ChedSyncLog
    {
        $endDate = now()->format('Y-m-d');
        $startDate = now()->subDays(2)->format('Y-m-d');
        
        return $this->syncCertificates($startDate, $endDate);
    }

    /**
     * Fetch all certificates with pagination handling
     */
    private function fetchAllCertificates(string $startDate, string $endDate): array
    {
        $allCertificates = [];
        $offset = 0;
        $pageSize = 100; // TRACES API limit
        $hasMoreData = true;

        while ($hasMoreData) {
            Log::info('Fetching certificates page', [
                'offset' => $offset,
                'page_size' => $pageSize
            ]);

            $certificates = $this->fetchCertificatesPage($startDate, $endDate, $offset, $pageSize);
            
            $this->apiCalls[] = [
                'offset' => $offset,
                'returned_count' => count($certificates),
                'timestamp' => now()->toISOString()
            ];

            if (empty($certificates) || count($certificates) < $pageSize) {
                $hasMoreData = false;
            }

            $allCertificates = array_merge($allCertificates, $certificates);
            $offset += $pageSize;

            // Safety break to prevent infinite loops
            if ($offset > 10000) {
                Log::warning('Reached maximum offset limit, stopping pagination');
                break;
            }
        }

        return $allCertificates;
    }

    /**
     * Fetch a single page of certificates
     */
    private function fetchCertificatesPage(string $startDate, string $endDate, int $offset, int $pageSize): array
    {
        // We'll need to modify the TracesNtChedClient to support pagination
        // For now, let's use the existing method and handle pagination manually
        try {
            return $this->client->getValidatedFishCertificates($startDate, $endDate);
        } catch (\Exception $e) {
            Log::error('Failed to fetch certificates page', [
                'offset' => $offset,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Process and store certificates
     */
    private function processCertificates(array $certificates): void
    {
        DB::transaction(function () use ($certificates) {
            foreach ($certificates as $certificateData) {
                $this->processSingleCertificate($certificateData);
            }
        });
    }

    /**
     * Process a single certificate
     */
    private function processSingleCertificate(array $data): void
    {
        $chedId = $data['id'] ?? null;
        if (!$chedId) {
            Log::warning('Certificate missing ID, skipping', ['data' => $data]);
            return;
        }

        // Check if certificate exists
        $certificate = ChedCertificate::where('ched_id', $chedId)->first();
        
        if ($certificate) {
            // Update existing certificate if data has changed
            if ($this->shouldUpdateCertificate($certificate, $data)) {
                $this->updateCertificate($certificate, $data);
                $this->updatedCertificates++;
            }
        } else {
            // Create new certificate
            $certificate = $this->createCertificate($data);
            $this->newCertificates++;
        }

        // Process sanitary references
        $this->processSanitaryReferences($certificate, $data);
        
        // Process products
        $this->processProducts($certificate, $data);
    }

    /**
     * Create new certificate
     */
    private function createCertificate(array $data): ChedCertificate
    {
        return ChedCertificate::create([
            'ched_id' => $data['id'],
            'type' => $data['type'] ?? null,
            'local_reference' => $data['local_reference'] ?? null,
            'status' => $data['status'] ?? null,
            'status_name' => $data['status_name'] ?? null,
            'bcp_code' => $data['bcp_code'] ?? null,
            'bcp_unlocode' => $data['bcp_unlocode'] ?? null,
            'country_of_issuance' => $data['country_of_issuance'] ?? null,
            'country_of_entry' => $data['country_of_entry'] ?? null,
            'country_of_dispatch' => $data['country_of_dispatch'] ?? null,
            'country_of_origin' => $data['country_of_origin'] ?? null,
            'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? null,
            'country_of_consignor' => $data['country_of_consignor'] ?? null,
            'consignor_name' => $data['consignor_name'] ?? null,
            'country_of_consignee' => $data['country_of_consignee'] ?? null,
            'consignee_name' => $data['consignee_name'] ?? null,
            'create_date_time' => $this->parseDateTime($data['create_date_time'] ?? null),
            'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null),
            'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null),
            'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null),
            'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null),
            'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null),
            'raw_data' => $data,
        ]);
    }

    /**
     * Update existing certificate
     */
    private function updateCertificate(ChedCertificate $certificate, array $data): void
    {
        $certificate->update([
            'type' => $data['type'] ?? $certificate->type,
            'local_reference' => $data['local_reference'] ?? $certificate->local_reference,
            'status' => $data['status'] ?? $certificate->status,
            'status_name' => $data['status_name'] ?? $certificate->status_name,
            'bcp_code' => $data['bcp_code'] ?? $certificate->bcp_code,
            'bcp_unlocode' => $data['bcp_unlocode'] ?? $certificate->bcp_unlocode,
            'country_of_issuance' => $data['country_of_issuance'] ?? $certificate->country_of_issuance,
            'country_of_entry' => $data['country_of_entry'] ?? $certificate->country_of_entry,
            'country_of_dispatch' => $data['country_of_dispatch'] ?? $certificate->country_of_dispatch,
            'country_of_origin' => $data['country_of_origin'] ?? $certificate->country_of_origin,
            'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? $certificate->country_of_place_of_destination,
            'country_of_consignor' => $data['country_of_consignor'] ?? $certificate->country_of_consignor,
            'consignor_name' => $data['consignor_name'] ?? $certificate->consignor_name,
            'country_of_consignee' => $data['country_of_consignee'] ?? $certificate->country_of_consignee,
            'consignee_name' => $data['consignee_name'] ?? $certificate->consignee_name,
            'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null) ?? $certificate->update_date_time,
            'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null) ?? $certificate->status_change_date_time,
            'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null) ?? $certificate->declaration_date_time,
            'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null) ?? $certificate->decision_date_time,
            'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null) ?? $certificate->prior_notification_date_time,
            'raw_data' => $data,
        ]);
    }

    /**
     * Check if certificate should be updated
     */
    private function shouldUpdateCertificate(ChedCertificate $certificate, array $data): bool
    {
        $newUpdateTime = $this->parseDateTime($data['update_date_time'] ?? null);
        
        if (!$newUpdateTime || !$certificate->update_date_time) {
            return true; // Update if we can't compare dates
        }
        
        return $newUpdateTime->gt($certificate->update_date_time);
    }

    /**
     * Process sanitary references for a certificate
     */
    private function processSanitaryReferences(ChedCertificate $certificate, array $data): void
    {
        if (!isset($data['references']) || !is_array($data['references'])) {
            return;
        }

        foreach ($data['references'] as $reference) {
            // Only process sanitary certificate references (type_code 852)
            if (($reference['type_code'] ?? null) === '852' || 
                strpos($reference['id'] ?? '', 'IMPORT.EU.') === 0) {
                
                $sanitaryRef = $reference['id'] ?? null;
                if (!$sanitaryRef) continue;

                // Check if this sanitary reference already exists for this certificate
                $exists = ChedSanitaryReference::where('ched_id', $certificate->ched_id)
                    ->where('sanitary_reference', $sanitaryRef)
                    ->exists();

                if (!$exists) {
                    ChedSanitaryReference::create([
                        'ched_id' => $certificate->ched_id,
                        'sanitary_reference' => $sanitaryRef,
                        'type_code' => $reference['type_code'] ?? null,
                        'type_name' => $reference['type_name'] ?? null,
                        'relationship_type' => $reference['relationship_type'] ?? null,
                        'relationship_name' => $reference['relationship_name'] ?? null,
                        'scheme_agency' => $reference['scheme_agency'] ?? null,
                        'attachment_uri' => $reference['attachment'] ?? null,
                        'issue_date_time' => $this->parseDateTime($reference['issue_date_time'] ?? null),
                    ]);
                    
                    $this->sanitaryReferencesAdded++;
                }
            }
        }
    }

    /**
     * Process products for a certificate
     */
    private function processProducts(ChedCertificate $certificate, array $data): void
    {
        if (!isset($data['commodities']) || !is_array($data['commodities'])) {
            return;
        }

        // Clear existing products for this certificate to avoid duplicates
        ChedProduct::where('ched_id', $certificate->ched_id)->delete();

        foreach ($data['commodities'] as $commodity) {
            $classNameEn = null;
            if (isset($commodity['class_names']['en'])) {
                $classNameEn = is_array($commodity['class_names']['en']) 
                    ? implode(' | ', $commodity['class_names']['en'])
                    : $commodity['class_names']['en'];
            }

            ChedProduct::create([
                'ched_id' => $certificate->ched_id,
                'system_id' => $commodity['system_id'] ?? null,
                'system_name' => $commodity['system_name'] ?? null,
                'class_code' => $commodity['class_code'] ?? null,
                'class_names' => $commodity['class_names'] ?? null,
                'class_name_en' => $classNameEn,
            ]);
            
            $this->productsAdded++;
        }
    }

    /**
     * Parse datetime string
     */
    private function parseDateTime(?string $dateTime): ?Carbon
    {
        if (!$dateTime) return null;
        
        try {
            return Carbon::parse($dateTime);
        } catch (\Exception $e) {
            Log::warning('Failed to parse datetime', ['datetime' => $dateTime]);
            return null;
        }
    }

    /**
     * Initialize sync log
     */
    private function initializeSyncLog(string $startDate, string $endDate): void
    {
        $this->syncLog = ChedSyncLog::create([
            'sync_start_time' => now(),
            'date_range_start' => $startDate,
            'date_range_end' => $endDate,
            'status' => 'running',
        ]);
    }

    /**
     * Complete sync log
     */
    private function completeSyncLog(): void
    {
        $this->syncLog->update([
            'sync_end_time' => now(),
            'total_certificates_fetched' => $this->newCertificates + $this->updatedCertificates,
            'new_certificates_added' => $this->newCertificates,
            'certificates_updated' => $this->updatedCertificates,
            'sanitary_references_added' => $this->sanitaryReferencesAdded,
            'products_added' => $this->productsAdded,
            'status' => 'completed',
            'api_calls_made' => $this->apiCalls,
        ]);
    }

    /**
     * Fail sync log
     */
    private function failSyncLog(\Exception $e): void
    {
        $this->syncLog->update([
            'sync_end_time' => now(),
            'status' => 'failed',
            'error_message' => $e->getMessage(),
            'api_calls_made' => $this->apiCalls,
        ]);
    }
}
