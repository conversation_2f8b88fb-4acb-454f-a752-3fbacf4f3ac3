<?php

namespace App\Services;

use App\Models\EuImportProduct;
use App\Models\EuImportCertificate;
use SimpleXMLElement;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class EuImportProductParserService
{
    /**
     * Parse and store products from EU Import certificate XML
     */
    public function parseAndStoreProducts(EuImportCertificate $certificate, string $xmlContent): array
    {
        try {
            Log::info("Starting product parsing for certificate", ['import_id' => $certificate->import_id]);
            
            $xml = new SimpleXMLElement($xmlContent);
            $this->registerNamespaces($xml);
            
            $products = [];
            $tradeLineItems = $xml->xpath('//ns4:IncludedSPSTradeLineItem');
            
            if (empty($tradeLineItems)) {
                Log::warning("No trade line items found in XML", ['import_id' => $certificate->import_id]);
                return $products;
            }
            
            foreach ($tradeLineItems as $item) {
                $sequenceNumber = (int) $item->xpath('ns4:SequenceNumeric')[0] ?? 0;
                
                // Skip summary items (sequence 0)
                if ($sequenceNumber === 0) {
                    continue;
                }
                
                $productData = $this->parseProductData($item, $certificate->import_id);
                
                if ($productData) {
                    $product = $this->createOrUpdateProduct($productData);
                    $products[] = $product;
                    
                    Log::info("Product parsed and stored", [
                        'import_id' => $certificate->import_id,
                        'sequence' => $sequenceNumber,
                        'scientific_name' => $productData['scientific_name'],
                        'net_weight' => $productData['net_weight']
                    ]);
                }
            }
            
            Log::info("Product parsing completed", [
                'import_id' => $certificate->import_id,
                'products_count' => count($products)
            ]);
            
            return $products;
            
        } catch (\Exception $e) {
            Log::error("Error parsing products", [
                'import_id' => $certificate->import_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Register XML namespaces
     */
    private function registerNamespaces(SimpleXMLElement $xml): void
    {
        $xml->registerXPathNamespace('ns4', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');
        $xml->registerXPathNamespace('ns5', 'urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21');
    }
    
    /**
     * Parse individual product data from XML element
     */
    private function parseProductData(SimpleXMLElement $item, string $importId): ?array
    {
        try {
            $sequenceNumber = (int) ($item->xpath('ns4:SequenceNumeric')[0] ?? 0);
            $scientificName = (string) ($item->xpath('ns4:ScientificName[@languageID="la"]')[0] ?? '');
            $productionBatchId = (string) ($item->xpath('ns4:ProductionBatchID')[0] ?? '');
            
            // Get weight information
            $netWeightElements = $item->xpath('ns4:NetWeightMeasure');
            $netWeight = null;
            $netWeightUnit = null;
            
            if (!empty($netWeightElements)) {
                $netWeight = (float) $netWeightElements[0];
                $netWeightUnit = (string) $netWeightElements[0]['unitCode'] ?? 'KGM';
            }
            
            // Parse classifications
            $classifications = $this->parseClassifications($item);
            
            // Extract specific classification data
            $cnCode = $classifications['CN']['code'] ?? null;
            $cnDescription = $classifications['CN']['description'] ?? null;
            $faoCode = $classifications['FAO_ASFIS']['code'] ?? null;
            $faoDescription = $classifications['FAO_ASFIS']['description'] ?? null;
            $natureOfCommodity = $classifications['NATURE_OF_COMMODITY']['code'] ?? null;
            $natureDescription = $classifications['NATURE_OF_COMMODITY']['description'] ?? null;
            $treatmentType = $classifications['TREATMENT_TYPE']['code'] ?? null;
            $treatmentDescription = $classifications['TREATMENT_TYPE']['description'] ?? null;
            $finalConsumerCode = $classifications['FINAL_CONSUMER']['code'] ?? null;
            $forFinalConsumer = $finalConsumerCode === 'TRUE';
            
            // Determine if wild stock
            $isWildStock = $natureOfCommodity === 'WILD_STOCK';
            
            // Parse packaging information
            $packageData = $this->parsePackaging($item);
            
            // Parse processes
            $processes = $this->parseProcesses($item);
            
            // Get collection date from processes
            $collectionDate = $this->extractCollectionDate($processes);
            
            // Get processing plant information
            $processingPlant = $this->extractProcessingPlantInfo($processes);
            
            // Get common name (could be derived from scientific name or other sources)
            $commonName = $this->deriveCommonName($scientificName, $faoDescription);
            
            return [
                'import_id' => $importId,
                'sequence_number' => $sequenceNumber,
                'scientific_name' => $scientificName,
                'common_name' => $commonName,
                'production_batch_id' => $productionBatchId,
                'net_weight' => $netWeight,
                'net_weight_unit' => $netWeightUnit,
                'gross_weight' => null, // Not available in this XML structure
                'gross_weight_unit' => null,
                'cn_code' => $cnCode,
                'cn_description' => $cnDescription,
                'fao_code' => $faoCode,
                'fao_description' => $faoDescription,
                'nature_of_commodity' => $natureOfCommodity,
                'nature_description' => $natureDescription,
                'treatment_type' => $treatmentType,
                'treatment_description' => $treatmentDescription,
                'is_wild_stock' => $isWildStock,
                'for_final_consumer' => $forFinalConsumer,
                'package_type_code' => $packageData['type_code'] ?? null,
                'package_type_name' => $packageData['type_name'] ?? null,
                'package_level_code' => $packageData['level_code'] ?? null,
                'package_level_name' => $packageData['level_name'] ?? null,
                'package_quantity' => $packageData['quantity'] ?? null,
                'processes' => $processes,
                'collection_date' => $collectionDate,
                'processing_plant_id' => $processingPlant['id'] ?? null,
                'processing_plant_name' => $processingPlant['name'] ?? null,
                'origin_country_id' => 'MR', // From the certificate
                'origin_country_name' => 'Mauritania',
                'all_classifications' => $classifications,
                'raw_data' => $this->elementToArray($item)
            ];
            
        } catch (\Exception $e) {
            Log::error("Error parsing individual product", [
                'import_id' => $importId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Parse product classifications
     */
    private function parseClassifications(SimpleXMLElement $item): array
    {
        $classifications = [];
        
        $classificationElements = $item->xpath('ns4:ApplicableSPSClassification');
        
        foreach ($classificationElements as $classification) {
            $systemId = (string) ($classification->xpath('ns4:SystemID')[0] ?? '');
            $systemName = (string) ($classification->xpath('ns4:SystemName')[0] ?? '');
            $classCode = (string) ($classification->xpath('ns4:ClassCode')[0] ?? '');
            
            // Get class names
            $classNames = [];
            $classNameElements = $classification->xpath('ns4:ClassName');
            foreach ($classNameElements as $className) {
                $languageId = (string) ($className['languageID'] ?? 'en');
                $classNames[$languageId] = (string) $className;
            }
            
            $classifications[$systemId] = [
                'system_id' => $systemId,
                'system_name' => $systemName,
                'code' => $classCode,
                'description' => $classNames['en'] ?? $classNames['la'] ?? reset($classNames) ?: null,
                'all_names' => $classNames
            ];
        }
        
        return $classifications;
    }
    
    /**
     * Parse packaging information
     */
    private function parsePackaging(SimpleXMLElement $item): array
    {
        $packageElements = $item->xpath('ns4:PhysicalSPSPackage');
        
        if (empty($packageElements)) {
            return [];
        }
        
        $package = $packageElements[0];
        
        return [
            'level_code' => (string) ($package->xpath('ns4:LevelCode')[0] ?? null),
            'level_name' => (string) ($package->xpath('ns4:LevelCode')[0]['name'] ?? null),
            'type_code' => (string) ($package->xpath('ns4:TypeCode')[0] ?? null),
            'type_name' => (string) ($package->xpath('ns4:TypeCode')[0]['name'] ?? null),
            'quantity' => (float) ($package->xpath('ns4:ItemQuantity')[0] ?? 0)
        ];
    }
    
    /**
     * Parse process information
     */
    private function parseProcesses(SimpleXMLElement $item): array
    {
        $processes = [];
        $processElements = $item->xpath('ns4:AppliedSPSProcess');
        
        foreach ($processElements as $process) {
            $typeCode = (string) ($process->xpath('ns4:TypeCode')[0] ?? '');
            $typeName = (string) ($process->xpath('ns4:TypeCode')[0]['name'] ?? '');
            
            $startDateTime = null;
            $startDateTimeElements = $process->xpath('ns4:CompletionSPSPeriod/ns4:StartDateTime/ns5:DateTime');
            if (!empty($startDateTimeElements)) {
                $startDateTime = (string) $startDateTimeElements[0];
            }
            
            // Get operator information if available
            $operator = null;
            $operatorElements = $process->xpath('ns4:OperatorSPSParty');
            if (!empty($operatorElements)) {
                $operatorParty = $operatorElements[0];
                $operator = [
                    'id' => (string) ($operatorParty->xpath('ns4:ID')[0] ?? ''),
                    'name' => (string) ($operatorParty->xpath('ns4:Name')[0] ?? ''),
                    'type' => (string) ($operatorParty->xpath('ns4:TypeCode[@listID="operator_activity_type"]')[0] ?? ''),
                    'classification' => (string) ($operatorParty->xpath('ns4:TypeCode[@listID="classification_section_code"]')[0] ?? '')
                ];
            }
            
            $processes[] = [
                'type_code' => $typeCode,
                'type_name' => $typeName,
                'start_date_time' => $startDateTime,
                'operator' => $operator
            ];
        }
        
        return $processes;
    }
    
    /**
     * Extract collection date from processes
     */
    private function extractCollectionDate(array $processes): ?Carbon
    {
        foreach ($processes as $process) {
            if ($process['type_code'] === '33' && $process['start_date_time']) { // Collecting process
                try {
                    return Carbon::parse($process['start_date_time']);
                } catch (\Exception $e) {
                    Log::warning("Could not parse collection date", ['date' => $process['start_date_time']]);
                }
            }
        }
        
        return null;
    }
    
    /**
     * Extract processing plant information
     */
    private function extractProcessingPlantInfo(array $processes): array
    {
        foreach ($processes as $process) {
            if ($process['type_code'] === '37' && $process['operator']) { // Manufacturing process
                return [
                    'id' => $process['operator']['id'],
                    'name' => $process['operator']['name']
                ];
            }
        }
        
        return [];
    }
    
    /**
     * Derive common name from scientific name and other sources
     */
    private function deriveCommonName(string $scientificName, ?string $faoDescription): ?string
    {
        // Map of scientific names to common names
        $commonNameMap = [
            'Epinephelus aeneus' => 'White grouper',
            'Epinephelus marginatus' => 'Dusky grouper',
            'Pagrus auriga' => 'Redbanded seabream',
            'Epinephelus costae' => 'Goldblotch grouper',
            'Pagrus caeruleostictus' => 'Bluespotted seabream',
            'Mycteroperca rubra' => 'Mottled grouper',
            'Solea senegalensis' => 'Senegalese sole',
            'Pseudupeneus prayensis' => 'West African goatfish'
        ];
        
        return $commonNameMap[$scientificName] ?? $faoDescription ?? null;
    }
    
    /**
     * Create or update product record
     */
    private function createOrUpdateProduct(array $productData): EuImportProduct
    {
        return EuImportProduct::updateOrCreate(
            [
                'import_id' => $productData['import_id'],
                'sequence_number' => $productData['sequence_number']
            ],
            $productData
        );
    }
    
    /**
     * Convert XML element to array for raw_data storage
     */
    private function elementToArray(SimpleXMLElement $element): array
    {
        return json_decode(json_encode($element), true);
    }
    
    /**
     * Get products for a certificate
     */
    public function getProductsForCertificate(string $importId): \Illuminate\Database\Eloquent\Collection
    {
        return EuImportProduct::where('import_id', $importId)
            ->orderBy('sequence_number')
            ->get();
    }
    
    /**
     * Get product summary statistics
     */
    public function getProductSummary(string $importId): array
    {
        $products = EuImportProduct::where('import_id', $importId)->get();
        
        return [
            'total_products' => $products->count(),
            'total_weight' => $products->sum('net_weight'),
            'total_packages' => $products->sum('package_quantity'),
            'species_count' => $products->unique('scientific_name')->count(),
            'wild_stock_count' => $products->where('is_wild_stock', true)->count(),
            'aquaculture_count' => $products->where('is_wild_stock', false)->count(),
            'treatment_types' => $products->pluck('treatment_type')->unique()->values()->toArray(),
            'processing_plants' => $products->whereNotNull('processing_plant_name')
                ->pluck('processing_plant_name')->unique()->values()->toArray()
        ];
    }
}