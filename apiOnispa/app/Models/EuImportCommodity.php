<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EuImportCommodity extends Model
{
    use HasFactory;

    protected $fillable = [
        'import_id',
        'system_id',
        'system_name',
        'class_code',
        'class_name',
        'sequence_numeric',
    ];

    /**
     * Get the certificate that owns this commodity
     */
    public function certificate(): BelongsTo
    {
        return $this->belongsTo(EuImportCertificate::class, 'import_id', 'import_id');
    }

    /**
     * Search commodities by CN code
     */
    public static function searchByCnCode(string $cnCode, bool $exactMatch = false)
    {
        $query = static::where('system_id', 'CN');
        
        if ($exactMatch) {
            $query->where('class_code', $cnCode);
        } else {
            $query->where('class_code', 'like', $cnCode . '%');
        }
        
        return $query->with('certificate')->get();
    }

    /**
     * Get commodities by import reference
     */
    public static function getByImportReference(string $importReference)
    {
        return static::where('import_id', $importReference)
            ->with('certificate')
            ->get();
    }
}
