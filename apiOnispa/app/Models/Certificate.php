<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    protected $fillable = [
        'certificate_id', 'type', 'local_reference', 'status', 'status_name', 'bcp_code', 'bcp_unlocode',
        'country_of_issuance', 'country_of_entry', 'country_of_dispatch', 'country_of_origin',
        'country_of_place_of_destination', 'country_of_consignor', 'consignor_name', 'country_of_consignee',
        'consignee_name', 'create_date_time', 'update_date_time', 'status_change_date_time',
        'declaration_date_time', 'decision_date_time', 'prior_notification_date_time',
        'commodities', 'references', 'validated_by', 'validated_at'
    ];

    protected $casts = [
        'commodities' => 'array',
        'references' => 'array',
        'create_date_time' => 'datetime',
        'update_date_time' => 'datetime',
        'status_change_date_time' => 'datetime',
        'declaration_date_time' => 'datetime',
        'decision_date_time' => 'datetime',
        'prior_notification_date_time' => 'datetime',
        'validated_at' => 'datetime',
    ];

    /**
     * Get the user who validated this certificate.
     */
    public function validator()
    {
        return $this->belongsTo(User::class, 'validated_by');
    }
}