<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChedProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'ched_id',
        'system_id',
        'system_name',
        'class_code',
        'class_names',
        'class_name_en',
    ];

    protected $casts = [
        'class_names' => 'array',
    ];

    /**
     * Get the certificate that owns this product
     */
    public function certificate(): BelongsTo
    {
        return $this->belongsTo(ChedCertificate::class, 'ched_id', 'ched_id');
    }

    /**
     * Get products by sanitary reference
     */
    public static function getBySanitaryReference(string $sanitaryReference)
    {
        return static::whereHas('certificate.sanitaryReferences', function ($query) use ($sanitaryReference) {
            $query->where('sanitary_reference', $sanitaryReference);
        })->with('certificate')->get();
    }
}
