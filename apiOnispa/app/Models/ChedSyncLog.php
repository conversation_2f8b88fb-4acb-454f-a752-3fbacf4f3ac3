<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChedSyncLog extends Model
{
    use HasFactory;

    protected $table = 'ched_sync_log';

    protected $fillable = [
        'sync_start_time',
        'sync_end_time',
        'date_range_start',
        'date_range_end',
        'total_certificates_fetched',
        'new_certificates_added',
        'certificates_updated',
        'sanitary_references_added',
        'products_added',
        'status',
        'error_message',
        'api_calls_made',
    ];

    protected $casts = [
        'sync_start_time' => 'datetime',
        'sync_end_time' => 'datetime',
        'api_calls_made' => 'array',
    ];

    /**
     * Get the latest successful sync
     */
    public static function getLatestSuccessfulSync()
    {
        return static::where('status', 'completed')
            ->orderBy('sync_end_time', 'desc')
            ->first();
    }

    /**
     * Get sync statistics
     */
    public static function getSyncStats($days = 7)
    {
        return static::where('sync_start_time', '>=', now()->subDays($days))
            ->selectRaw('
                COUNT(*) as total_syncs,
                SUM(new_certificates_added) as total_new_certificates,
                SUM(certificates_updated) as total_updated_certificates,
                SUM(sanitary_references_added) as total_sanitary_references,
                SUM(products_added) as total_products,
                AVG(total_certificates_fetched) as avg_certificates_per_sync
            ')
            ->first();
    }
}
