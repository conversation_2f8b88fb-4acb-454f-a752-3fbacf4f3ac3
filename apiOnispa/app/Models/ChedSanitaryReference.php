<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChedSanitaryReference extends Model
{
    use HasFactory;

    protected $fillable = [
        'ched_id',
        'sanitary_reference',
        'type_code',
        'type_name',
        'relationship_type',
        'relationship_name',
        'scheme_agency',
        'attachment_uri',
        'issue_date_time',
    ];

    protected $casts = [
        'issue_date_time' => 'datetime',
    ];

    /**
     * Get the certificate that owns this sanitary reference
     */
    public function certificate(): BelongsTo
    {
        return $this->belongsTo(ChedCertificate::class, 'ched_id', 'ched_id');
    }

    /**
     * Find all certificates by sanitary reference
     */
    public static function findCertificatesBySanitaryReference(string $sanitaryReference)
    {
        return static::where('sanitary_reference', $sanitaryReference)
            ->with('certificate.products')
            ->get()
            ->pluck('certificate');
    }
}
