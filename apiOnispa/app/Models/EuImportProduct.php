<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EuImportProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'import_id',
        'sequence_number',
        'scientific_name',
        'common_name',
        'production_batch_id',
        'net_weight',
        'net_weight_unit',
        'gross_weight',
        'gross_weight_unit',
        'cn_code',
        'cn_description',
        'fao_code',
        'fao_description',
        'nature_of_commodity',
        'nature_description',
        'treatment_type',
        'treatment_description',
        'is_wild_stock',
        'for_final_consumer',
        'package_type_code',
        'package_type_name',
        'package_level_code',
        'package_level_name',
        'package_quantity',
        'processes',
        'collection_date',
        'processing_plant_id',
        'processing_plant_name',
        'origin_country_id',
        'origin_country_name',
        'all_classifications',
        'raw_data'
    ];

    protected $casts = [
        'net_weight' => 'decimal:3',
        'gross_weight' => 'decimal:3',
        'package_quantity' => 'decimal:2',
        'is_wild_stock' => 'boolean',
        'for_final_consumer' => 'boolean',
        'processes' => 'array',
        'all_classifications' => 'array',
        'raw_data' => 'array',
        'collection_date' => 'datetime'
    ];

    /**
     * Get the certificate this product belongs to
     */
    public function certificate()
    {
        return $this->belongsTo(EuImportCertificate::class, 'import_id', 'import_id');
    }

    /**
     * Scope to filter by wild stock products
     */
    public function scopeWildStock($query)
    {
        return $query->where('is_wild_stock', true);
    }

    /**
     * Scope to filter by aquaculture products
     */
    public function scopeAquaculture($query)
    {
        return $query->where('is_wild_stock', false);
    }

    /**
     * Scope to filter by treatment type
     */
    public function scopeByTreatment($query, $treatmentType)
    {
        return $query->where('treatment_type', $treatmentType);
    }

    /**
     * Scope to filter by CN code
     */
    public function scopeByCnCode($query, $cnCode)
    {
        return $query->where('cn_code', 'like', $cnCode . '%');
    }

    /**
     * Scope to filter by scientific name
     */
    public function scopeBySpecies($query, $scientificName)
    {
        return $query->where('scientific_name', 'like', '%' . $scientificName . '%');
    }

    /**
     * Scope to filter by origin country
     */
    public function scopeByOriginCountry($query, $countryId)
    {
        return $query->where('origin_country_id', $countryId);
    }

    /**
     * Get formatted weight with unit
     */
    public function getFormattedNetWeightAttribute()
    {
        if ($this->net_weight && $this->net_weight_unit) {
            return $this->net_weight . ' ' . $this->net_weight_unit;
        }
        return null;
    }

    /**
     * Get formatted gross weight with unit
     */
    public function getFormattedGrossWeightAttribute()
    {
        if ($this->gross_weight && $this->gross_weight_unit) {
            return $this->gross_weight . ' ' . $this->gross_weight_unit;
        }
        return null;
    }

    /**
     * Get the main CN classification
     */
    public function getCnClassificationAttribute()
    {
        if ($this->all_classifications) {
            foreach ($this->all_classifications as $classification) {
                if (isset($classification['system_id']) && $classification['system_id'] === 'CN') {
                    return $classification;
                }
            }
        }
        return null;
    }

    /**
     * Get the FAO classification
     */
    public function getFaoClassificationAttribute()
    {
        if ($this->all_classifications) {
            foreach ($this->all_classifications as $classification) {
                if (isset($classification['system_id']) && $classification['system_id'] === 'FAO_ASFIS') {
                    return $classification;
                }
            }
        }
        return null;
    }

    /**
     * Get processing information summary
     */
    public function getProcessingSummaryAttribute()
    {
        if ($this->processes && is_array($this->processes)) {
            $summary = [];
            foreach ($this->processes as $process) {
                if (isset($process['type_name'])) {
                    $summary[] = $process['type_name'];
                }
            }
            return implode(', ', $summary);
        }
        return null;
    }

    /**
     * Check if product is chilled
     */
    public function isChilled()
    {
        return $this->treatment_type === 'CHILLED';
    }

    /**
     * Check if product is frozen
     */
    public function isFrozen()
    {
        return $this->treatment_type === 'FROZEN';
    }

    /**
     * Check if product is fresh
     */
    public function isFresh()
    {
        return $this->treatment_type === 'FRESH';
    }

    /**
     * Get all products for a certificate
     */
    public static function getByCertificate($importId)
    {
        return static::where('import_id', $importId)
            ->orderBy('sequence_number')
            ->get();
    }

    /**
     * Search products by multiple criteria
     */
    public static function search($criteria)
    {
        $query = static::query();

        if (isset($criteria['scientific_name'])) {
            $query->bySpecies($criteria['scientific_name']);
        }

        if (isset($criteria['cn_code'])) {
            $query->byCnCode($criteria['cn_code']);
        }

        if (isset($criteria['treatment_type'])) {
            $query->byTreatment($criteria['treatment_type']);
        }

        if (isset($criteria['origin_country'])) {
            $query->byOriginCountry($criteria['origin_country']);
        }

        if (isset($criteria['is_wild_stock'])) {
            if ($criteria['is_wild_stock']) {
                $query->wildStock();
            } else {
                $query->aquaculture();
            }
        }

        return $query->with('certificate')->get();
    }
}
