<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChedCertificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'ched_id',
        'type',
        'local_reference',
        'status',
        'status_name',
        'bcp_code',
        'bcp_unlocode',
        'country_of_issuance',
        'country_of_entry',
        'country_of_dispatch',
        'country_of_origin',
        'country_of_place_of_destination',
        'country_of_consignor',
        'consignor_name',
        'country_of_consignee',
        'consignee_name',
        'create_date_time',
        'update_date_time',
        'status_change_date_time',
        'declaration_date_time',
        'decision_date_time',
        'prior_notification_date_time',
        'raw_data',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'create_date_time' => 'datetime',
        'update_date_time' => 'datetime',
        'status_change_date_time' => 'datetime',
        'declaration_date_time' => 'datetime',
        'decision_date_time' => 'datetime',
        'prior_notification_date_time' => 'datetime',
    ];

    /**
     * Get the sanitary references for this certificate
     */
    public function sanitaryReferences(): HasMany
    {
        return $this->hasMany(ChedSanitaryReference::class, 'ched_id', 'ched_id');
    }

    /**
     * Get the products for this certificate
     */
    public function products(): HasMany
    {
        return $this->hasMany(ChedProduct::class, 'ched_id', 'ched_id');
    }

    /**
     * Find certificate by sanitary reference
     */
    public static function findBySanitaryReference(string $sanitaryReference)
    {
        return static::whereHas('sanitaryReferences', function ($query) use ($sanitaryReference) {
            $query->where('sanitary_reference', $sanitaryReference);
        })->with(['sanitaryReferences', 'products'])->first();
    }

    /**
     * Search certificates by sanitary reference pattern
     */
    public static function searchBySanitaryReference(string $pattern)
    {
        return static::whereHas('sanitaryReferences', function ($query) use ($pattern) {
            $query->where('sanitary_reference', 'LIKE', "%{$pattern}%");
        })->with(['sanitaryReferences', 'products'])->get();
    }
}
