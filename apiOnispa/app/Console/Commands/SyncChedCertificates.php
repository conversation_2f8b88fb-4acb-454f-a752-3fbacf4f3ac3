<?php

namespace App\Console\Commands;

use App\Services\ChedSyncService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncChedCertificates extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ched:sync 
                            {--start-date= : Start date (YYYY-MM-DD)}
                            {--end-date= : End date (YYYY-MM-DD)}
                            {--recent : Sync recent certificates (last 2 days)}
                            {--force : Force sync even if another sync is running}';

    /**
     * The console command description.
     */
    protected $description = 'Sync CHED certificates from TRACES NT API';

    /**
     * Execute the console command.
     */
    public function handle(ChedSyncService $syncService): int
    {
        $this->info('Starting CHED certificate synchronization...');

        try {
            // Check if another sync is running (unless forced)
            if (!$this->option('force') && $this->isSyncRunning()) {
                $this->warn('Another sync is already running. Use --force to override.');
                return 1;
            }

            if ($this->option('recent')) {
                $this->info('Syncing recent certificates (last 2 days)...');
                $syncLog = $syncService->syncRecentCertificates();
            } else {
                $startDate = $this->option('start-date') ?? now()->subDays(1)->format('Y-m-d');
                $endDate = $this->option('end-date') ?? now()->format('Y-m-d');
                
                $this->info("Syncing certificates from {$startDate} to {$endDate}...");
                $syncLog = $syncService->syncCertificates($startDate, $endDate);
            }

            $this->displaySyncResults($syncLog);
            
            $this->info('CHED certificate synchronization completed successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error('Sync failed: ' . $e->getMessage());
            Log::error('CHED sync command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Check if a sync is currently running
     */
    private function isSyncRunning(): bool
    {
        return \App\Models\ChedSyncLog::where('status', 'running')
            ->where('sync_start_time', '>', now()->subHours(2)) // Consider stuck after 2 hours
            ->exists();
    }

    /**
     * Display sync results
     */
    private function displaySyncResults($syncLog): void
    {
        $this->info('');
        $this->info('=== Sync Results ===');
        $this->info("Duration: {$syncLog->sync_start_time->diffForHumans($syncLog->sync_end_time, true)}");
        $this->info("Date Range: {$syncLog->date_range_start} to {$syncLog->date_range_end}");
        $this->info("Total Certificates Fetched: {$syncLog->total_certificates_fetched}");
        $this->info("New Certificates Added: {$syncLog->new_certificates_added}");
        $this->info("Certificates Updated: {$syncLog->certificates_updated}");
        $this->info("Sanitary References Added: {$syncLog->sanitary_references_added}");
        $this->info("Products Added: {$syncLog->products_added}");
        $this->info("API Calls Made: " . count($syncLog->api_calls_made ?? []));
        $this->info('');
    }
}
