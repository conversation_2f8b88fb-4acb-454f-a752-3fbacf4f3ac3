<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CompareTimezones extends Command
{
    protected $signature = 'traces:timezone-debug';
    protected $description = 'Debug timezone issues for TRACES API';

    public function handle()
    {
        $this->info('🕐 Timezone Debug Information');
        $this->newLine();

        // System timezone info
        $this->info('📍 System Information:');
        $this->line('PHP Default Timezone: ' . date_default_timezone_get());
        $this->line('System Date Command: ' . shell_exec('date'));
        $this->line('System TZ Environment: ' . (getenv('TZ') ?: 'Not set'));
        $this->newLine();

        // Current time in different formats
        $this->info('⏰ Current Time Formats:');
        $now = new \DateTime('now', new \DateTimeZone('UTC'));
        
        $this->line('DateTime UTC: ' . $now->format('Y-m-d H:i:s T'));
        $this->line('ISO 8601: ' . $now->format('c'));
        $this->line('TRACES Format 1: ' . $now->format('Y-m-d\TH:i:s\Z'));
        $this->line('TRACES Format 2: ' . $now->format('Y-m-d\TH:i:s.v\Z'));
        $this->line('Unix Timestamp: ' . $now->getTimestamp());
        $this->newLine();

        // Test different timezone scenarios
        $timezones = ['UTC', 'Europe/Paris', 'America/New_York', 'Africa/Casablanca'];
        
        $this->info('🌍 Time in Different Zones:');
        foreach ($timezones as $tz) {
            try {
                $time = new \DateTime('now', new \DateTimeZone($tz));
                $this->line("{$tz}: " . $time->format('Y-m-d H:i:s T'));
            } catch (\Exception $e) {
                $this->line("{$tz}: Error - " . $e->getMessage());
            }
        }
        $this->newLine();

        // NTP sync check
        $this->info('🔄 Time Synchronization:');
        $ntpCheck = shell_exec('which ntpdate 2>/dev/null') ?: shell_exec('which chrony 2>/dev/null');
        if ($ntpCheck) {
            $this->line('NTP client found: ' . trim($ntpCheck));
        } else {
            $this->warn('No NTP client found - time might drift');
        }
        
        // Check if we can reach time servers
        $timeServers = ['pool.ntp.org', 'time.google.com'];
        foreach ($timeServers as $server) {
            $result = shell_exec("ping -c 1 -W 2 {$server} 2>/dev/null");
            if ($result && strpos($result, '1 received') !== false) {
                $this->line("✅ Can reach {$server}");
            } else {
                $this->line("❌ Cannot reach {$server}");
            }
        }
        $this->newLine();

        // Test TRACES timestamp generation
        $this->info('🔐 TRACES Authentication Timestamps:');
        $this->testTracesTimestamps();
        
        return 0;
    }

    private function testTracesTimestamps()
    {
        // Force UTC timezone
        date_default_timezone_set('UTC');
        
        for ($i = 0; $i < 3; $i++) {
            $now = new \DateTime('now', new \DateTimeZone('UTC'));
            
            // Different timestamp formats used in TRACES
            $format1 = $now->format('Y-m-d\TH:i:s\Z');           // For digest
            $format2 = $now->format('Y-m-d\TH:i:s.v\Z');         // For timestamp element
            
            $this->line("Test {$i}: Digest={$format1}, Timestamp={$format2}");
            
            if ($i < 2) sleep(1); // Wait 1 second between tests
        }
        
        $this->newLine();
        $this->info('💡 Recommendations:');
        $this->line('1. Ensure server time is synchronized with NTP');
        $this->line('2. Verify timezone is set to UTC');
        $this->line('3. Check if server time matches your local time');
        $this->line('4. TRACES API expects timestamps within ±5 minutes of actual time');
    }
}
