<?php

namespace App\Console\Commands;

use App\Providers\TracesNtChedClient;
use Illuminate\Console\Command;

class TestTracesConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'traces:test {--debug : Show debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test TRACES API connection and authentication';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing TRACES API Connection...');
        $this->newLine();

        // Get configuration
        $config = config('services.traces');

        $this->info('📋 Configuration:');
        $this->line("Username: {$config['username']}");
        $this->line("Client ID: {$config['client_id']}");
        $this->line("Use Production: " . ($config['use_production'] ? 'Yes' : 'No'));
        $this->line("Auth Key Length: " . strlen($config['auth_key']));
        $this->line("Timeout: {$config['timeout']}s");
        $this->line("Verify SSL: " . ($config['verify_ssl'] ? 'Yes' : 'No'));
        $this->newLine();

        // Show environment info
        $this->info('🖥️  Environment Information:');
        $this->line("PHP Version: " . PHP_VERSION);
        $this->line("Server Timezone: " . date_default_timezone_get());
        $this->line("Current Time: " . date('Y-m-d H:i:s T'));
        $this->line("CURL Version: " . (curl_version()['version'] ?? 'unknown'));
        $this->line("OpenSSL Version: " . (curl_version()['ssl_version'] ?? 'unknown'));
        $this->newLine();

        try {
            // Create client
            $client = new TracesNtChedClient(
                $config['username'],
                $config['auth_key'],
                $config['client_id'],
                $config['use_production']
            );

            if ($this->option('debug')) {
                $this->info('🔐 Authentication Debug:');
                $client->debugAuthentication();
                $this->newLine();
            }

            // Test connection with a small date range
            $this->info('🚀 Testing API Connection...');
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d', strtotime('-6 days'));

            $this->line("Fetching certificates from {$startDate} to {$endDate}");

            $startTime = microtime(true);
            $certificates = $client->getValidatedFishCertificates($startDate, $endDate);
            $duration = round((microtime(true) - $startTime) * 1000, 2);

            $this->newLine();
            $this->info("✅ SUCCESS!");
            $this->line("Found " . count($certificates) . " certificates");
            $this->line("Request took {$duration}ms");

            if (count($certificates) > 0) {
                $this->newLine();
                $this->info('📄 Sample Certificate:');
                $sample = $certificates[0];
                $this->line("ID: " . ($sample['id'] ?? 'N/A'));
                $this->line("Type: " . ($sample['type'] ?? 'N/A'));
                $this->line("Status: " . ($sample['status_name'] ?? 'N/A'));
                $this->line("Country: " . ($sample['country_of_issuance'] ?? 'N/A'));
            }

        } catch (\Exception $e) {
            $this->newLine();
            $this->error("❌ FAILED!");
            $this->error("Error: " . $e->getMessage());

            $this->newLine();
            $this->info('🔧 Troubleshooting Tips:');
            $this->line('1. Check your internet connection');
            $this->line('2. Verify TRACES credentials in .env file');
            $this->line('3. Check server firewall settings');
            $this->line('4. Try with --debug flag for more details');
            $this->line('5. Check Laravel logs: storage/logs/laravel.log');

            return 1;
        }

        return 0;
    }
}
