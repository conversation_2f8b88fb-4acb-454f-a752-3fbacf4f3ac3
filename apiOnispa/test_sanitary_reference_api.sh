#!/bin/bash

# Test script for the updated CHED certificate API
# This script tests the new sanitary reference search functionality

# Configuration
API_BASE_URL="http://localhost:8000"  # Adjust to your Laravel app URL
API_ENDPOINT="$API_BASE_URL/api/certificates/products"

echo "=== CHED Certificate API Test - Sanitary Reference Search ==="
echo "API Endpoint: $API_ENDPOINT"
echo ""

# Test 1: Search by sanitary certificate reference
echo "TEST 1: Search by Sanitary Certificate Reference"
echo "------------------------------------------------"
SANITARY_REF="IMPORT.EU.MR.2025.0000007"
echo "Testing with sanitary reference: $SANITARY_REF"
echo ""

curl -s -H "Accept: application/json" \
     -H "Content-Type: application/json" \
     "$API_ENDPOINT?reference=$SANITARY_REF" | \
     python3 -m json.tool 2>/dev/null || \
     curl -s -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT?reference=$SANITARY_REF"

echo -e "\n\n"

# Test 2: Search by CHED ID (for comparison)
echo "TEST 2: Search by CHED ID (for comparison)"
echo "-------------------------------------------"
CHED_ID="CHEDP.FR.2025.0000038"
echo "Testing with CHED ID: $CHED_ID"
echo ""

curl -s -H "Accept: application/json" \
     -H "Content-Type: application/json" \
     "$API_ENDPOINT?reference=$CHED_ID" | \
     python3 -m json.tool 2>/dev/null || \
     curl -s -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT?reference=$CHED_ID"

echo -e "\n\n"

# Test 3: Invalid reference
echo "TEST 3: Invalid Reference (Error Handling)"
echo "-------------------------------------------"
INVALID_REF="INVALID.REFERENCE.123"
echo "Testing with invalid reference: $INVALID_REF"
echo ""

curl -s -H "Accept: application/json" \
     -H "Content-Type: application/json" \
     "$API_ENDPOINT?reference=$INVALID_REF" | \
     python3 -m json.tool 2>/dev/null || \
     curl -s -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT?reference=$INVALID_REF"

echo -e "\n\n"

# Test 4: Missing reference parameter
echo "TEST 4: Missing Reference Parameter"
echo "------------------------------------"
echo "Testing without reference parameter:"
echo ""

curl -s -H "Accept: application/json" \
     -H "Content-Type: application/json" \
     "$API_ENDPOINT" | \
     python3 -m json.tool 2>/dev/null || \
     curl -s -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT"

echo -e "\n\n"

echo "=== Test Summary ==="
echo "1. Sanitary reference search: $SANITARY_REF"
echo "2. CHED ID search: $CHED_ID"
echo "3. Invalid reference handling"
echo "4. Missing parameter handling"
echo ""
echo "The API should now support both CHED IDs and sanitary certificate references!"
echo ""
echo "Usage examples:"
echo "  curl \"$API_ENDPOINT?reference=CHEDP.FR.2025.0000038\""
echo "  curl \"$API_ENDPOINT?reference=IMPORT.EU.MR.2025.0000007\""
