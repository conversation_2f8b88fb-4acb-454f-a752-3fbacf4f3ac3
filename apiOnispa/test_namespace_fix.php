<?php

/**
 * Quick test to verify the namespace fix for EU-Import API
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Providers\TracesNtEuImportClient;

echo "Testing EU-Import API Namespace Fix\n";
echo "===================================\n\n";

// Test the SOAP request generation
$client = new TracesNtEuImportClient('test_user', 'test_key', 'test_client', false);

// Use reflection to access private method for testing
$reflection = new ReflectionClass($client);
$method = $reflection->getMethod('createGetEuImportCertificateRequest');
$method->setAccessible(true);

$testReference = 'IMPORT.EU.MR.2025.0003940';
$soapRequest = $method->invoke($client, $testReference);

echo "Generated SOAP Request:\n";
echo "======================\n";
echo $soapRequest . "\n\n";

// Check if the correct namespace is used
if (strpos($soapRequest, 'xmlns:euimport="http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01"') !== false) {
    echo "✓ Correct namespace found: euimport\n";
} else {
    echo "✗ Incorrect namespace - still using old namespace\n";
}

if (strpos($soapRequest, '<euimport:GetEuImportCertificateRequest') !== false) {
    echo "✓ Correct request element: euimport:GetEuImportCertificateRequest\n";
} else {
    echo "✗ Incorrect request element\n";
}

if (strpos($soapRequest, '<euimport:ID>' . $testReference . '</euimport:ID>') !== false) {
    echo "✓ Correct ID element: euimport:ID\n";
} else {
    echo "✗ Incorrect ID element\n";
}

// Check if old namespace is removed
if (strpos($soapRequest, 'xmlns:v1="http://ec.europa.eu/tracesnt/certificate/import/v01"') === false) {
    echo "✓ Old namespace removed\n";
} else {
    echo "✗ Old namespace still present\n";
}

echo "\nNamespace fix verification complete!\n";
echo "The SOAP request should now use the correct namespace from the WSDL.\n";
