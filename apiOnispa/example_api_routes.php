<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CertificateController;

/*
|--------------------------------------------------------------------------
| CHED Certificate API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for CHED certificate management using the
| TracesNtChedClient. These routes demonstrate how to integrate the
| certificate retrieval functionality into your Laravel application.
|
*/

// Group routes under 'api/certificates' prefix with rate limiting
Route::prefix('certificates')->middleware(['api', 'throttle:60,1'])->group(function () {
    
    /**
     * Get a single CHED certificate by reference number
     * 
     * GET /api/certificates/by-reference?reference=CHEDPP.IT.2020.1000091
     * 
     * Query Parameters:
     * - reference (required): CHED reference number (e.g., CHEDPP.IT.2020.1000091)
     * 
     * Response:
     * {
     *   "success": true,
     *   "data": {
     *     "certificate_id": "...",
     *     "issue_date": "...",
     *     "parties": [...],
     *     "consignments": [...],
     *     "locations": [...],
     *     "referenced_documents": [...]
     *   }
     * }
     */
    Route::get('/by-reference', [CertificateController::class, 'getCertificateByReference'])
        ->name('certificates.by-reference');

    /**
     * Get multiple CHED certificates by reference numbers
     * 
     * POST /api/certificates/by-references
     * 
     * Request Body:
     * {
     *   "references": [
     *     "CHEDPP.IT.2020.1000091",
     *     "CHEDP.MR.2025.0000001"
     *   ]
     * }
     * 
     * Response:
     * {
     *   "success": true,
     *   "results": [...],
     *   "errors": [...],
     *   "summary": {
     *     "total": 2,
     *     "successful": 1,
     *     "failed": 1
     *   }
     * }
     */
    Route::post('/by-references', [CertificateController::class, 'getCertificatesByReferences'])
        ->name('certificates.by-references');

    /**
     * Validate CHED reference format
     * 
     * GET /api/certificates/validate-reference?reference=CHEDPP.IT.2020.1000091
     * 
     * Query Parameters:
     * - reference (required): CHED reference number to validate
     * 
     * Response:
     * {
     *   "reference": "CHEDPP.IT.2020.1000091",
     *   "is_valid": true,
     *   "format": "CHED[TYPE].[COUNTRY].[YEAR].[NUMBER]",
     *   "examples": [...]
     * }
     */
    Route::get('/validate-reference', [CertificateController::class, 'validateReference'])
        ->name('certificates.validate-reference');

});

/*
|--------------------------------------------------------------------------
| Additional Configuration Examples
|--------------------------------------------------------------------------
*/

// Example of how to add authentication middleware
Route::prefix('certificates')->middleware(['api', 'auth:sanctum', 'throttle:60,1'])->group(function () {
    
    // Protected routes that require authentication
    Route::get('/protected/by-reference', [CertificateController::class, 'getCertificateByReference'])
        ->name('certificates.protected.by-reference');
        
});

// Example of how to add role-based access control
Route::prefix('certificates')->middleware(['api', 'auth:sanctum', 'role:admin', 'throttle:60,1'])->group(function () {
    
    // Admin-only routes
    Route::post('/admin/by-references', [CertificateController::class, 'getCertificatesByReferences'])
        ->name('certificates.admin.by-references');
        
});

/*
|--------------------------------------------------------------------------
| Configuration Instructions
|--------------------------------------------------------------------------
|
| To use these routes in your Laravel application:
|
| 1. Add the routes to your routes/api.php file
| 2. Make sure you have the CertificateController in app/Http/Controllers/
| 3. Configure your TRACES NT credentials in config/services.php:
|
|    'traces' => [
|        'username' => env('TRACES_USERNAME'),
|        'auth_key' => env('TRACES_AUTH_KEY'),
|        'client_id' => env('TRACES_CLIENT_ID'),
|        'use_production' => env('TRACES_USE_PRODUCTION', false),
|        'timeout' => env('TRACES_TIMEOUT', 60),
|        'verify_ssl' => env('TRACES_VERIFY_SSL', true),
|    ],
|
| 4. Add the environment variables to your .env file:
|
|    TRACES_USERNAME=your_username
|    TRACES_AUTH_KEY=your_auth_key
|    TRACES_CLIENT_ID=your_client_id
|    TRACES_USE_PRODUCTION=false
|    TRACES_TIMEOUT=60
|    TRACES_VERIFY_SSL=true
|
| 5. Test the endpoints using tools like Postman or curl:
|
|    curl -X GET "http://your-app.com/api/certificates/by-reference?reference=CHEDPP.IT.2020.1000091"
|
|    curl -X POST "http://your-app.com/api/certificates/by-references" \
|         -H "Content-Type: application/json" \
|         -d '{"references": ["CHEDPP.IT.2020.1000091", "CHEDP.MR.2025.0000001"]}'
|
|--------------------------------------------------------------------------
*/
