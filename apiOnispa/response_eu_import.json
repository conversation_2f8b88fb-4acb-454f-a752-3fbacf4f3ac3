{"Envelope": {"Header": {"Security": {"Timestamp": {"Created": {"__prefix": "ns1", "__text": "2025-09-13T22:41:56.394+02:00"}, "Expires": {"__prefix": "ns1", "__text": "2025-09-13T22:42:01.394+02:00"}, "__prefix": "ns1"}, "_xmlns:ns1": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd", "_xmlns:ns0": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd", "__prefix": "ns0"}, "__prefix": "S"}, "Body": {"GetEuImportCertificateResponse": {"SPSCertificate": {"SPSExchangedDocument": {"Name": {"__prefix": "ns3", "__text": "2024/1333 (2020/2235) MODEL FISH-CRUST-HC"}, "ID": {"__prefix": "ns3", "__text": "IMPORT.EU.MR.2025.0003940"}, "TypeCode": {"_name": "Sanitary certificate (EU IMPORT - Veterinary Certificate to EU)", "__prefix": "ns3", "__text": "852"}, "StatusCode": {"_name": "Issued (Valid)", "__prefix": "ns3", "__text": "70"}, "IssueDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:28:03.000+02:00"}, "__prefix": "ns3"}, "IncludedSPSNote": [{"Content": {"__prefix": "ns3", "__text": "2025-09-09T20:29:02.000+02:00"}, "SubjectCode": {"_listID": "euimport_note_subject_code", "_listName": "EU IMPORT Note SubjectCode list", "_name": "Last update datetime", "__prefix": "ns3", "__text": "LAST_UPDATE_DATETIME"}, "__prefix": "ns3"}, {"ContentCode": {"_listID": "product_temperature", "_listName": "Product temperature", "_name": "Chilled", "__prefix": "ns3", "__text": "CHILLED"}, "Content": {"__prefix": "ns3"}, "SubjectCode": {"_listID": "certificate_note_subject_code", "_listName": "Certificate Note SubjectCode list", "_name": "Transport conditions", "__prefix": "ns3", "__text": "TRANSPORT_CONDITIONS"}, "__prefix": "ns3"}], "ReferenceSPSReferencedDocument": [{"TypeCode": {"_name": "Related document", "__prefix": "ns3", "__text": "916"}, "RelationshipTypeCode": {"_name": "Local reference number (Local reference)", "__prefix": "ns3", "__text": "AVZ"}, "ID": {"__prefix": "ns3", "__text": "1330/E2/01/25/MR"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Sanitary certificate (EU IMPORT - Veterinary Certificate to EU)", "__prefix": "ns3", "__text": "852"}, "RelationshipTypeCode": {"_name": "Document reference, internal (Document reference, internal)", "__prefix": "ns3", "__text": "CAW"}, "ID": {"__prefix": "ns3", "__text": "IMPORT.EU.MR.2025.0003940"}, "AttachmentBinaryObject": {"_format": "url", "_mimeCode": "text/url", "_uri": "https://webgate.ec.europa.eu/tracesnt/certificate/eu-import/IMPORT.EU.MR.2025.0003940", "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Air waybill (International transport document for Airplane)", "__prefix": "ns3", "__text": "740"}, "RelationshipTypeCode": {"_name": "Bill of lading number (International transport document)", "__prefix": "ns3", "__text": "BM"}, "ID": {"__prefix": "ns3", "__text": "LTA 474-1034469-2"}, "Information": {"__prefix": "ns3", "__text": "BINTER"}, "__prefix": "ns3"}], "SignatorySPSAuthentication": [{"TypeCode": {"_name": "Inspection (Identification of Applicant)", "__prefix": "ns3", "__text": "4"}, "ActualDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:17:38.000+02:00"}, "__prefix": "ns3"}, "ProviderSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "02.121"}, "Name": {"__prefix": "ns3", "__text": "Mauritanienne DES Produits Halieutiques (MPH)"}, "RoleCode": {"_name": "Officer (Identification of Applicant)", "__prefix": "ns3", "__text": "VJ"}, "TypeCode": {"_listID": "user_body_role", "_listName": "User body role", "_name": "Operator", "__prefix": "ns3", "__text": "OPERATOR"}, "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": "."}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouadhibou"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON> Nouadhibou"}, "__prefix": "ns3"}, "SpecifiedSPSPerson": {"Name": {"__prefix": "ns3", "__text": "BRAHIM LIMAM"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "IncludedSPSClause": [{"ID": {"_schemeID": "euimport_consignment_clause", "_schemeName": "EUIMPORT consignment's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "PURPOSE"}, "Content": [{"__prefix": "ns3", "__text": "IMPORT"}, {"_languageID": "en", "__prefix": "ns3", "__text": "For internal market"}], "__prefix": "ns3"}, {"ID": {"_schemeID": "euimport_consignment_clause", "_schemeName": "EUIMPORT consignment's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "GOODS_CERTIFIED_AS"}, "Content": [{"__prefix": "ns3", "__text": "HUMAN_CONSUMPTION"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Products for human consumption"}], "__prefix": "ns3"}, {"ID": {"_schemeID": "euimport_consignment_clause", "_schemeName": "EUIMPORT consignment's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "SIGNATORY_PERSON_EMAIL"}, "Content": {"__prefix": "ns3", "__text": "<EMAIL>"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"TypeCode": {"_name": "Clearance (Official inspector)", "__prefix": "ns3", "__text": "1"}, "ActualDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:28:03.000+02:00"}, "__prefix": "ns3"}, "ProviderSPSParty": {"ID": {"_schemeID": "authority_activity_id", "_schemeName": "Authority activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "MR00000"}, "Name": {"__prefix": "ns3", "__text": "Office National d'Inspection Sanitaire des Produits de la Pèche et de l'Aquaculture (ONISPA)"}, "RoleCode": {"_name": "Authorized official", "__prefix": "ns3", "__text": "AM"}, "TypeCode": {"_listID": "user_body_role", "_listName": "User body role", "_name": "Authority", "__prefix": "ns3", "__text": "AUTHORITY"}, "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": "BP 1416"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouadhibou"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON> Nouadhibou"}, "__prefix": "ns3"}, "SpecifiedSPSPerson": {"Name": {"__prefix": "ns3", "__text": "<PERSON>"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "IncludedSPSClause": [{"ID": {"_schemeID": "euimport_certification_clause", "_schemeName": "EUIMPORT certification's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "SIGNATORY_PERSON_EMAIL"}, "Content": {"__prefix": "ns3", "__text": "<EMAIL>"}, "__prefix": "ns3"}, {"ID": {"_schemeID": "euimport_certification_clause", "_schemeName": "EUIMPORT certification's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "DIGITALLY_SIGNED_DOCUMENT_TYPE"}, "Content": {"__prefix": "ns3", "__text": "PDF"}, "__prefix": "ns3"}, {"ID": {"_schemeID": "euimport_certification_clause", "_schemeName": "EUIMPORT certification's clauses", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "CERTIFICATION_CLAUSES"}, "Content": {"_languageID": "en", "__prefix": "ns3", "__text": "(1)- [x] II.1.  Public health attestation [Deleted when the Union is not the final destination of the live fish, live crustaceans or products of animal origin from those animals]  \n I, the undersigned, declare that I am aware of the relevant requirements of Regulation (EC) No 178/2002 of the European Parliament and of the Council, Regulation (EC) No 852/2004 of the European Parliament and of the Council, Regulation (EC) No 853/2004 of the European Parliament and of the Council and Regulation (EU) 2017/625 of the European Parliament and of the Council and hereby certify that the fishery products described in Part I were produced in accordance with these requirements, in particular that they:  \n (a)  have been obtained in the region(s) or country(ies) which, at the date of issue of this animal health/official certificate is/are authorised for the entry into the Union of fishery products and in Annex IX to Commission Implementing Regulation (EU) 2021/405;  \n (b)  come from (an) establishment(s) applying general hygiene requirements and implementing a programme based on the hazard analysis and critical control points (HACCP) principles in accordance with Article 5 of Regulation (EC) No 852/2004, regularly audited by the competent authorities, and is listed as a Union approved establishment;  \n (c)  have been caught and handled on board vessels, landed, handled and where appropriate prepared, processed, frozen and thawed hygienically in compliance with the requirements laid down in Section VIII, Chapters I to IV of Annex III to Regulation (EC) No 853/2004;  \n (d)  have not been stored in holds, tanks or containers used for other purposes than the production and/or storage of fishery products;  \n (e)  satisfy the health standards laid down in Section VIII, Chapter V of Annex III to Regulation (EC) No 853/2004 and the criteria laid down in Commission Regulation (EC) No 2073/2005;  \n (f)  have been packaged, stored and transported in compliance with Section VIII, Chapters VI to VIII of Annex III to Regulation (EC) No 853/2004;  \n (g)  have been marked in accordance with Section I of Annex II to Regulation (EC) No 853/2004;  \n (h)  fulfil the guarantees covering live animals and products thereof, if of aquaculture origin, provided by the control plan submitted in accordance with Article 6(2) of Commission Delegated Regulation (EU) 2022/2292 and the concerned animals and products are listed in Annex -I to Implementing Regulation (EU) 2021/405 for the concerned third country or territory;  \n(i)  for the live animals from wild catch and products thereof monitoring arrangements are in place to control compliance with the Union legislation on contaminants, in accordance with Commission Regulation (EU) 2023/915 on maximum levels for certain contaminants in food and on pesticide residues and in accordance with Regulation (EC) No 396/2005 of the European Parliament and of the Council on maximum residue levels of pesticides in or on food and feed of plant and animal origin;  \n(j)  have satisfactorily undergone the official controls laid down in Articles 67 to 71 of Commission Implementing Regulation (EU) 2019/627 .  \n(4) (13) - [ ] ~~[II.1.a~~  ~~Attestation as regards Commission Delegated Regulation (EU) 2023/905 [Delete when the Union is not the final destination of the fishery products]~~  \n~~I, the undersigned, declare that I am aware of the relevant requirements of Regulation (EU) 2019/6 of the European Parliament and of the Council and Commission Delegated Regulation (EU) 2023/905 and hereby certify that the fishery products of aquaculture origin described in Part I were produced in accordance with these requirements, and in particular that the aquacultured animals from which the products have been derived have not been administered antimicrobial medicinal products for growth promotion or yield increase or antimicrobial medicinal products containing an antimicrobial that is included in the list of antimicrobials reserved for the treatment of certain infections in humans laid down in Commission Implementing Regulation (EU) 2022/1255 as set out in Article 3 of Delegated Regulation (EU) 2023/905 and originate from a third country or region thereof listed in accordance with Article 5(2) of Delegated Regulation (EU) 2023/905.]~~ - [ ] - [ ]  \n \n- [ ] ~~(2)[II.2.~~  ~~Animal health attestation for live fish and live crustaceans of listed (3) species intended for human consumption and products of animal origin from those aquatic animals intended for further processing in the Union before human consumption, excluding live fish and live crustaceans and their products landed from fishing vessels~~  \n ~~II.2.1.~~  ~~According to official information, the~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] ~~~~ - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I, have been obtained from animals which]~~ - [ ] ~~](4) meet the following animal health requirements:~~  \n  ~~II.2.1.1.~~  ~~They originate from~~ - [ ] ~~[an establishment] (4)~~ - [ ] ~~~~ - [ ] ~~[a habitat] (4)~~ - [ ] ~~which is not subject to national restriction measures for animal health reasons or because of the occurrence of abnormal mortalities with an undetermined cause, including the relevant listed diseases referred to in Annex I to Commission Delegated Regulation (EU) 2020/692 and emerging diseases;~~  \n  ~~II.2.1.2.~~  ~~The~~ - [ ] ~~[aquatic animals are not intended to be killed] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals, have been obtained from animals which were not intended to be killed] (4)~~ - [ ] ~~under a national programme for the eradication of diseases, including the listed diseases referred to in Annex I to Delegated Regulation (EU) 2020/692 relevant for the species and emerging diseases.~~  \n - [ ] ~~(4) [II.2.2.~~  ~~The~~ - [ ] ~~[aquaculture animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquaculture animals other than live aquaculture animals described in Part I, have been obtained from animals which] (4)~~ - [ ] ~~meet the following requirements:~~  \n  ~~II.2.2.1.~~  ~~They come from an aquaculture establishment which is~~ - [ ] ~~[registered] (4)~~ - [ ] - [ ] ~~[approved] (4)~~ - [ ] ~~by, and under the control of, the competent authority of the third country or territory of origin and which has a system in place to maintain and to keep for at least three years, up-to-date records containing information regarding:~~  \n   ~~(a)~~  ~~the species, categories and number of aquaculture animals on the establishment;~~  \n   ~~(b)~~  ~~movements of aquatic animals into, and aquaculture animals out of, the establishment;~~  \n   ~~(c)~~  ~~mortality in the establishment;~~  \n  ~~II.2.2.2.~~  ~~They come from an aquaculture establishment which receives regular animal health visits from a veterinarian for the purpose of the detection of, and information on, signs indicative of the occurrence of diseases, including the listed diseases referred to in Annex I to Delegated Regulation (EU) 2020/692 relevant for the species and emerging diseases, at a frequency that is proportionate to the risk posed by the establishment.]~~ - [ ]  \n ~~II.2.3.~~  ~~General animal health requirements~~  \n  ~~The~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I]~~ - [ ] ~~, have been obtained from animals which](4) meet the following animal health requirements:~~  \n  - [ ] ~~(4)(6) [II.2.3.1.~~  ~~They are subject to the requirements in point II.2.4. and they originate from a~~ - [ ] ~~[country] (4)~~ - [ ] - [ ] ~~[territory] (4)~~ - [ ] ~~~~ - [ ] ~~[zone] (4)~~ - [ ] - [ ] ~~[compartment] (4)~~ - [ ] ~~with code:~~ null~~-~~ null~~(5) which, at the date of issue of this animal health/official certificate, is listed in Part 1 of Annex XXI to Commission Implementing Regulation (EU) 2021/404 for the entry into the Union of~~ - [ ] ~~[aquatic animals] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals] (4)~~ - [ ] ~~;]~~ - [ ] ~~]~~  \n  - [ ] ~~(4)(6) [II.2.3.2.~~  ~~They are aquatic animals which have undergone clinical inspection in accordance with Article 166 of Delegated Regulation (EU) 2020/692 within a period of 72 hours prior to the time of loading. During the inspection, the animals showed no signs of transmissible disease and, according to the relevant records of the establishment, there was no indication of disease problems;]~~ - [ ]  \n ~~(11)~~  ~~II.2.3.3.~~  ~~They are aquatic animals which are dispatched to the Union directly from the place of origin;~~  \n  ~~II.2.3.4.~~  ~~They have not been in contact with aquatic animals of a lower health status.~~  \n~~(4)(6)either~~  - [ ] ~~[II.2.4.~~  ~~Specific health requirements~~  \n ~~(4)~~  - [ ] ~~[II.2.4.1~~  ~~Requirements for listed (3) species for Epizootic haematopoietic necrosis, infection with Taura syndrome virus, infection with yellow head virus~~  \n   ~~The~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I have been obtained from animals which] (4)~~ - [ ] ~~originate from a~~ - [ ] ~~[country] (4)~~ - [ ] - [ ] ~~[territory] (4)~~ - [ ] - [ ] ~~[zone] (4)~~ - [ ] - [ ] ~~[compartment~~ - [ ] ~~](4) declared free from~~ - [ ] ~~[Epizootic haematopoietic necrosis] (4)~~ - [ ] - [ ] ~~[infection with Taura syndrome virus] (4)~~ - [ ] - [ ] ~~[infection with yellow head virus] (4)~~ - [ ] ~~in accordance with conditions which are at least as stringent as those laid down in Article 66 or in Article 73(1) and Article 73(2), point (a), of Commission Delegated Regulation (EU) 2020/689 and in the case of aquatic animals, all listed (3) species for the relevant disease(s):~~  \n    ~~(a)~~  ~~are introduced from another country or territory, or zone or compartment thereof which has been declared free from the same disease(s);~~  \n    ~~(b)~~  ~~are not vaccinated against~~ - [ ] ~~[that] (4)~~ - [ ] - [ ] ~~[those] (4)~~ - [ ] ~~disease(s).]~~ - [ ]  \n  - [ ] ~~(4)(7) [II.2.4.2.~~  ~~Requirements for listed (3) species for Viral haemorrhagic septicaemia (VHS), Infectious haematopoietic necrosis (IHN), infection with HPR-deleted infectious salmon anaemia virus (ISAV) or infection with White spot syndrome virus~~  \n   ~~The~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I, have been obtained from animals which] (4)~~ - [ ] ~~originate from a~~ - [ ] ~~[country] (4)~~ - [ ] - [ ] ~~[territory] (4)~~ - [ ] - [ ] ~~[zone] (4)~~ - [ ] - [ ] ~~[compartment] (4)~~ - [ ] ~~declared free from~~ - [ ] ~~[Viral haemorrhagic septicaemia (VHS)] (4)~~ - [ ] - [ ] ~~[Infectious haematopoietic necrosis (IHN)] (4)~~ - [ ] - [ ] ~~[infection with HPR-deleted infectious salmon anaemia virus (ISAV)] (4)~~ - [ ] - [ ] ~~[infection with White spot syndrome virus] (4)~~ - [ ] ~~] in accordance with Part II, Chapter 4, of Delegated Regulation (EU) 2020/689 and in the case of aquatic animals, all listed (3) species for the relevant disease(s):~~  \n    ~~(a)~~  ~~are introduced from another country or territory, or zone or compartment thereof which has been declared free from the same disease(s);~~  \n    ~~(b)~~  ~~are not vaccinated against~~ - [ ] ~~[that] (4)~~ - [ ] ~~~~ - [ ] ~~[those] (4)~~ - [ ] ~~disease(s).]~~ - [ ]  \n  - [ ] ~~(4)(8) [II.2.4.3.~~  ~~Requirements for species (9) susceptible to infection with Spring viraemia of carp (SVC), Bacterial Kidney disease (BKD), infection with Infectious pancreatic necrosis virus (IPN), infection with Gyrodactylus salaris (GS), infection with Salmonid alphavirus (SAV) and species (3) susceptible to Koi herpes virus disease (KHV)~~  \n   ~~The~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I have been obtained from animals which ]~~ - [ ] ~~](4) originate from a~~ - [ ] ~~[country] (4)~~ - [ ] - [ ] ~~[territory] (4)~~ - [ ] - [ ] ~~[zone] (4)~~ - [ ] - [ ] ~~[compartment]~~ - [ ] ~~(4) which fulfils the health guarantees as regards~~ - [ ] ~~[SVC,] (4)~~ - [ ] - [ ] ~~[BKD,] (4)~~ - [ ] - [ ] ~~[IPN], (4)~~ - [ ] - [ ] ~~[GS,] (4)~~ - [ ] - [ ] ~~[SAV,] (4)~~ - [ ] - [ ] ~~[KHV,] (4)~~ - [ ] ~~, which are necessary to comply with the national measures which apply in the Member State of destination in accordance with Article 175 of Delegated Regulation (EU) 2020/692, and for which the Member State or part thereof, is listed in~~ - [ ] ~~[Annex I] (4)~~ - [ ] - [ ] ~~[Annex II] (4)~~ - [ ] ~~to Commission Implementing Decision (EU) 2021/260.]~~ - [ ] ~~]~~ - [ ]  \n~~(4)(6)or~~  - [ ] ~~[II.2.4.~~  ~~Specific health requirements~~  \n  ~~The~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I have been obtained from animals which] (4)~~ - [ ] ~~are destined for an disease control aquatic food establishment within the Union which is approved in accordance with Article 11 of Commission Delegated Regulation (EU) 2020/691, where they are to be processed for human consumption.]~~ - [ ]  \n ~~II.2.5.~~  ~~To the best of my knowledge, and as declared by the operator, the~~ - [ ] ~~[aquatic animals described in Part I] (4)~~ - [ ] - [ ] ~~[products of animal origin from aquatic animals other than live aquatic animals described in Part I have been obtained from animals which] (4)~~ - [ ] ~~originate from~~ - [ ] ~~[an establishment] (4)~~ - [ ] - [ ] ~~[a habitat] (4)~~ - [ ] ~~where:~~  \n  ~~(a)~~  ~~there were no abnormal mortalities with an undetermined cause; and~~  \n  ~~(b)~~  ~~they have not been in contact with aquatic animals of listed (3) species which did not comply with the requirements referred to in point II.2.1.~~  \n ~~II.2.6.~~  ~~Transport requirements~~  \n  ~~Arrangements have been made to transport the aquatic animals described in Part I in accordance with the requirements set out in Articles 167 and 168 of Delegated Regulation (EU) 2020/692 and specifically that:~~  \n  ~~II.2.6.1.~~  ~~when the aquatic animals are transported in water, the water in which they are transported is not changed in a third country or territory, or zone or compartment thereof which is not listed for entry into the Union of the particular species and category of aquatic animals;~~  \n  ~~II.2.6.2.~~  ~~the aquatic animals are not transported under conditions that jeopardise their health status, in particular:~~  \n   ~~(i)~~  ~~when the aquatic animals are transported in water, it does not alter their health status;~~  \n   ~~(ii)~~  ~~the means of transport and the containers are constructed in such a way that the health status of the aquatic animals is not jeopardised during transportation;~~  \n   ~~(iii)~~  ~~the~~ - [ ] ~~[container] (4)~~ - [ ] - [ ] ~~[well-boat] (4)~~ - [ ] ~~is~~ - [ ] ~~[previously unused] (4)~~ - [ ] - [ ] ~~[cleaned and disinfected in accordance with a protocol and with products approved by the competent authority of the third country or territory of origin,] (4)~~ - [ ] ~~, prior to the time of loading for dispatch to the Union;~~  \n  ~~II.2.6.3.~~  ~~from the time of loading at the place of origin until the time of arrival in the Union, the animals in the consignment are not transported in the same water or~~ - [ ] ~~[container] (4)~~ - [ ] - [ ] ~~[well-boat] (4)~~ - [ ] ~~together with aquatic animals which are of a lower health status or which are not intended for the entry into the Union;~~  \n  ~~II.2.6.4.~~  ~~where a water exchange is necessary in a~~ - [ ] ~~[country] (4)~~ - [ ] - [ ] ~~[territory] (4)~~ - [ ] - [ ] ~~[zone] (4)~~ - [ ] ~~~~ - [ ] ~~[compartment] (4)~~ - [ ] ~~which is listed for entry into the Union of the particular species and category of aquatic animals, it only occurs~~ - [ ] ~~[in the case of transport on land, at water exchange points approved by the competent authority of the third countryor territory where the water exchange takes place] (4)~~ - [ ] - [ ] ~~[in the case of transport by well-boat, at a distance which is at least 10 km from any aquaculture establishments which are located en-route from the place of origin to the place of destination in the Union.] (4)~~ - [ ]  \n ~~II.2.7.~~  ~~Labelling requirements~~  \n  ~~II.2.7.1.~~  ~~Arrangements have been made to identify and label the~~ - [ ] ~~[means of transport] (4)~~ - [ ] - [ ] ~~[containers] (4)~~ - [ ] ~~in accordance with Article 169 of Delegated Regulation (EU) 2020/692 and specifically that the consignment is identified by~~ - [ ] ~~[a legible and visible label on the exterior of the container] (4)~~ - [ ] - [ ] ~~[an entry in the ships manifest when transported by well boat,] (4)~~ - [ ] ~~which clearly links the consignment to this animal health/official certificate;~~  \n  ~~(4)~~ - [ ] ~~[II.2.7.2.~~  ~~In the case of aquatic animals, the legible and visible label referred to in point II.2.7.1. contains at least the following information:~~  \n   ~~(a)~~  ~~the number of containers in the consignment;~~  \n   ~~(b)~~  ~~the name of the species present in each container;~~  \n   ~~(c)~~  ~~the number of aquatic animals in each container for each of the species present;~~  \n   ~~(d)~~  ~~a statement saying:~~ - [ ] ~~[“live fish intended for human consumption in the Union”] (4)~~ - [ ] - [ ] ~~[“live crustaceans intended for human consumption in the European Union’] (4)~~ - [ ] ~~.]~~ - [ ]  \n  ~~(4)~~ - [ ] ~~[II.2.7.3.In the case of products of animal origin from aquatic animals other than live aquatic animals, the legible and visible label referred to in point II.2.7.1. contains one of the following statements:~~  \n   ~~(a)~~  ~~“products of animal origin from fish, other than live fish, intended for further processing in the Union”;~~  \n   ~~(b)~~  ~~“products of animal origin from crustaceans, other than live crustaceans, intended for further processing in the Union”.]~~ - [ ]  \n~~(4) (10)~~  ~~II.2.8.~~  ~~Validity of animal health/official certificate~~  \n  ~~This animal health/official certificate is valid for 10 days from the date of issue. In the case of transport by waterway/sea of aquatic animals, this period of 10 days may be extended by the duration of the journey by waterway/sea.]~~ - [ ]  \nNotes \nIn accordance with the Agreement on the withdrawal of the United Kingdom of Great Britain and Northern Ireland from the European Union and the European Atomic Energy Community, and in particular Article 5(4) of the Protocol on Ireland/Northern Ireland in conjunction with Annex 2 to that Protocol, references to the Union in this animal health/official certificate include the United Kingdom in respect of Northern Ireland. \nThis animal health/official certificate is intended for the entry into the Union of live fish, live crustaceans and products of animal origin from those animals, including when the Union is not the final destination of such live aquatic animals and their products. \n“Aquatic animals” are animals as defined in Article 4, point (3), of Regulation (EU) 2016/429 of the European Parliament and of the Council. “Aquaculture animals” are aquatic animals which are subject to aquaculture as defined in Article 4, point (7), of Regulation (EU) 2016/429. \n“Further processing” means any type of measures and techniques, carried out before the placing on the market for human consumption, affecting anatomical wholeness, such as bleeding, evisceration, heading, slicing and filleting which produce waste or by-products which could cause a risk of disease spread. \nAll aquatic animals and products of animal origin from aquatic animals other than live aquatic animals, to which Part II.2.4. of this animal health/official certificate applies, must originate from a third country or territory, or zone or compartment thereof which appears in column 2 of the table in Part 1 of Annex XXI to Implementing Regulation (EU) 2021/404. \nPart II.2.4. of the animal health/official certificate does not apply to the following crustaceans and fish, and they may therefore originate from a country or regions, which is listed in Annex IX to Implementing Regulation (EU) 2021/405: \n (a) crustaceans which are packaged and labelled for human consumption in accordance with the specific requirements for those animals as set out in Regulation (EC) No 853/2004 and which are no longer able to survive as living animals if returned to the aquatic environment, \n (b) crustaceans which are intended for human consumption without further processing, provided they are packaged for retail sale in compliance with the requirements for such packages as set out in Regulation (EC) No 853/2004, \n (c) crustaceans which are packaged and labelled for human consumption in accordance with the specific requirements for those animals as set out in Regulation (EC) No 853/2004 and which are intended for further processing without temporary storage at the place of processing, \n (d) fish which are slaughtered and eviscerated before dispatch. \nThis animal health/official certificate applies to products of animal origin as well as to live aquatic animals including those destined for a disease control aquatic food establishment as defined in Article 4, point (52), of Regulation (EU) 2016/429 which are intended for human consumption in accordance with Section VII of Annex III to Regulation (EC) No 853/2004. \nThis animal health/official certificate shall be completed in accordance with the notes for the completion of certificates provided for in Chapter 4 of Annex I to Commission Implementing Regulation (EU) 2020/2235. \nPart I: \nBox reference I.20.: Tick “Canning industry” for whole fish initially frozen in brine at -9°C or at a temperature higher than -18°C and intended for canning in accordance with the requirements of Section VIII, Chapter I, Part II, point 7, of Annex III to Regulation (EC) No 853/2004. Tick “Products for human consumption” or “Further processing” for the other cases. \nBox reference I.27.: Description of consignment: \n“CN code”: Insert the appropriate Harmonised System (HS) code(s) of the World Customs Organisation under the following headings: 0301, 0302, 0303, 0304, 0305, 0306, 0307, 0308, 0511, 1504, 1516, 1518, 1603, 1604, 1605 or 2106. \n“Nature of commodity”: Specify whether aquaculture or wild origin. \n  “Treatment type”: Specify whether live, chilled, frozen or processed. \n  “Manufacturing plant”: includes factory vessel, freezer vessel, reefer vessels, cold store and processing plant. \nPart II: \n(1) Part II.1. of this animal health/official certificate does not apply to countries with special public health certification requirements laid down in equivalence agreements or other Union legislation. \n(2) Part II.2. of this animal health/official certificate shall not apply and shall be deleted when the consignment consists of: (a) species other than those listed in the Annex to Commission Implementing Regulation (EU) 2018/1882; or (b) wild aquatic animals and products of animal origin from those aquatic animals which are landed from fishing vessels for direct human consumption; or (c) products of animal origin from aquatic animals, other than live aquatic animals, which are ready for direct human consumption without undergoing further processing in the Union. \n(3) Species listed in columns 3 and 4 in the table of the Annex to Implementing Regulation (EU) 2018/1882. Species listed in column 4 shall only be regarded as vectors under the conditions set out in Article 171 of Delegated Regulation (EU) 2020/692. \n(4) Keep if appropriate/delete if not applicable. In the case of Part II.2.4.1., deletion is not permitted if the consignment contains listed species for Epizootic haematopoietic necrosis, infection with Taura syndrome virus or infection with yellow head virus, other than in the circumstances referred to in note (6). \n(5) Code of the third country or territory, or zone, or compartment thereof as it appears in column 2 of the table in Part 1 of Annex XXI to Implementing Regulation (EU) 2021/404. \n(6) Parts II.2.3.1., II.2.3.2. and Part II.2.4. of this animal health/official certificate do not apply and shall be deleted if the consignment contains only the following crustaceans or fish: \n  (a) crustaceans which are packaged and labelled for human consumption in accordance with the specific requirements for those animals set out Regulation (EC) No 853/2004 and which are no longer able to survive as living animals if returned to the aquatic environment, \n  (b) crustaceans which are intended for human consumption without further processing, provided that they are packaged for retail sale in compliance with the requirements for such packages set out in Regulation (EC) No 853/2004, \n  (c) crustaceans which are packaged and labelled for human consumption in compliance with the specific requirements for those animals set out in Regulation (EC) No 853/2004 and which are intended for further processing without temporary storage at the place of processing, \n  (d) fish which are slaughtered and eviscerated before dispatch to the Union. \n(7) Applicable when the Member State of destination in the Union either has disease-free status for a category C disease as defined in Article 1, point (3), of Implementing Regulation (EU) 2018/1882, or is subject to an optional eradication programme established in accordance with Article 31(2) of Regulation (EU) 2016/429, otherwise delete. \n(8) Applicable when the Member State of destination or part thereof, in the Union has approved national measures for a specific disease as listed in Annex I or Annex II to Commission Implementing Decision (EU) 2021/260, otherwise delete \n(9) Susceptible species as referred to in the second column of the table in Annex III to Implementing Decision (EU) 2021/260. \n(10) Shall apply only to the consignments of live aquatic animals. \n(11) Part II.2.3.3. of this animal health/official certificate does not apply and shall be deleted if the consignment contains only the crustaceans referred to in note (6), points (a) to (c). \n(12) to be signed by: \n- an official veterinarian when Part II.2. Animal health attestation is not deleted, \n-  a certifying officer or an official veterinarian when Part II.2. Animal health attestation is deleted. \n(13) Applicable to consignments entering the Union as from 3 September 2026."}, "__prefix": "ns3"}], "__prefix": "ns3"}], "__prefix": "ns5"}, "SPSConsignment": {"AvailabilityDueDateTime": {"DateTimeString": {"__prefix": "ns4"}, "__prefix": "ns3"}, "ExportExitDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T18:27:00.000Z"}, "__prefix": "ns3"}, "ConsignorSPSParty": {"ID": {"_schemeID": "operator_internal_activity_id", "_schemeName": "Operator internal activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "486059"}, "Name": {"__prefix": "ns3", "__text": "BAMBA KEVIN FISH SARL"}, "RoleCode": {"_name": "Exporter", "__prefix": "ns3", "__text": "EX"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Exporter", "__prefix": "ns3", "__text": "EXPORTER"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Other establishments", "__prefix": "ns3", "__text": "FOOD-OTH"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": "Plage des pêcheurs NKC-MR"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ConsigneeSPSParty": {"ID": {"_schemeID": "operator_internal_activity_id", "_schemeName": "Operator internal activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "2383737"}, "Name": {"__prefix": "ns3", "__text": "SANCORY S.L"}, "RoleCode": {"_name": "Consignee", "__prefix": "ns3", "__text": "CN"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Importer", "__prefix": "ns3", "__text": "IMPORTER"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Other operators", "__prefix": "ns3", "__text": "OTH-OPER"}], "SpecifiedSPSAddress": {"PostcodeCode": {"__prefix": "ns3", "__text": "38107"}, "LineOne": {"__prefix": "ns3", "__text": "LONJA PESQUERA - 9 38180 SANTA CRUZ DE TENERIFE 38107 ESPAGNE"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Santa Cruz de Tenerife"}, "CountryID": {"__prefix": "ns3", "__text": "ES"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Spain"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Santa Cruz De Tenerife"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ExportSPSCountry": {"ID": {"__prefix": "ns3"}, "Name": {"__prefix": "ns3"}, "SubordinateSPSCountrySubDivision": {"Name": {"__prefix": "ns3"}, "HierarchicalLevelCode": {"_name": "None", "__prefix": "ns3", "__text": "0"}, "FunctionTypeCode": {"_name": "Place of authentication of document", "__prefix": "ns3", "__text": "44"}, "ActivityAuthorizedSPSParty": [{"ID": {"_schemeID": "authority_activity_id", "_schemeName": "Authority activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "MR00000"}, "Name": {"__prefix": "ns3", "__text": "Office National d'Inspection Sanitaire des Produits de la Pèche et de l'Aquaculture (ONISPA)"}, "RoleCode": {"_name": "Central bank or regulatory authority (Central competent authority)", "__prefix": "ns3", "__text": "RA"}, "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": "BP 1416"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouadhibou"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON> Nouadhibou"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"ID": {"_schemeID": "authority_activity_id", "_schemeName": "Authority activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "MR00000"}, "Name": {"__prefix": "ns3", "__text": "Office National d'Inspection Sanitaire des Produits de la Pèche et de l'Aquaculture (ONISPA)"}, "RoleCode": {"_name": "Local government sponsor (Local competent authority)", "__prefix": "ns3", "__text": "VG"}, "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": "BP 1416"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouadhibou"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON> Nouadhibou"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, "__prefix": "ns3"}, "LoadingBaseportSPSLocation": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "_schemeDataURI": "operator_activity", "__prefix": "ns3", "__text": "01.061"}, "Name": [{"__prefix": "ns3", "__text": "MR"}, {"__prefix": "ns3", "__text": "M.F.C Sarl"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, {"__prefix": "ns3"}, {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}], "__prefix": "ns3"}, "ImportSPSCountry": {"ID": {"__prefix": "ns3", "__text": "ES"}, "Name": {"_languageID": "en", "__prefix": "ns3", "__text": "Spain"}, "__prefix": "ns3"}, "UnloadingBaseportSPSLocation": {"ID": {"_schemeID": "un_locode", "_schemeName": "UN/LOCODE", "_schemeAgencyID": "un", "_schemeAgencyName": "United Nations", "_schemeDataURI": "authority_activity", "__prefix": "ns3", "__text": "ESLPA"}, "Name": [{"__prefix": "ns3", "__text": "ES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Las Palmas de Gran Canaria"}, {"__prefix": "ns3", "__text": "ESLPA4-AnimalHealth"}, {"__prefix": "ns3", "__text": "Gran Canaria"}, {"__prefix": "ns3", "__text": "Aeropuerto de Gran Canaria. Ctra Gral del Sur.Terminal de Carga.\n\nSOIVRE"}, {"__prefix": "ns3", "__text": "ESLPA"}], "__prefix": "ns3"}, "ExaminationSPSEvent": {"OccurrenceSPSLocation": {"Name": {"__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "DespatchSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "RoleCode": {"_name": "Despatch party (Place of dispatch)", "__prefix": "ns3", "__text": "PW"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "DeliverySPSParty": {"ID": {"_schemeID": "operator_internal_activity_id", "_schemeName": "Operator internal activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "2383737"}, "Name": {"__prefix": "ns3", "__text": "SANCORY S.L"}, "RoleCode": {"_name": "Delivery party (Place of destination)", "__prefix": "ns3", "__text": "DP"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Importer", "__prefix": "ns3", "__text": "IMPORTER"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Other operators", "__prefix": "ns3", "__text": "OTH-OPER"}], "SpecifiedSPSAddress": {"PostcodeCode": {"__prefix": "ns3", "__text": "38107"}, "LineOne": {"__prefix": "ns3", "__text": "LONJA PESQUERA - 9 38180 SANTA CRUZ DE TENERIFE 38107 ESPAGNE"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Santa Cruz de Tenerife"}, "CountryID": {"__prefix": "ns3", "__text": "ES"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Spain"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Santa Cruz De Tenerife"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "CustomsTransitAgentSPSParty": {"ID": {"_schemeID": "operator_internal_activity_id", "_schemeName": "Operator internal activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "239000"}, "Name": {"__prefix": "ns3", "__text": "<PERSON>"}, "RoleCode": {"_name": "Customs broker (Responsible for consignment)", "__prefix": "ns3", "__text": "CB"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Responsible for the load", "__prefix": "ns3", "__text": "RESPONSIBLE_FOR_THE_LOAD"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Responsible For the Load (Freight Forwarder)", "__prefix": "ns3", "__text": "RFL"}], "SpecifiedSPSAddress": {"PostcodeCode": {"__prefix": "ns3", "__text": "35001"}, "LineOne": {"__prefix": "ns3", "__text": "Aeropuerto de Gran Canaria. Terminal de Carga, oficina 19"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Las Palmas de Gran Canaria"}, "CountryID": {"__prefix": "ns3", "__text": "ES"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Spain"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Provincia de Las Palmas"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "MainCarriageSPSTransportMovement": {"ID": {"_schemeID": "airplane_flight_number", "_schemeName": "Flight number", "__prefix": "ns3", "__text": "BINTER"}, "ModeCode": {"_name": "Air transport", "__prefix": "ns3", "__text": "4"}, "__prefix": "ns3"}, "IncludedSPSConsignmentItem": {"NatureIdentificationSPSCargo": {"TypeCode": {"_name": "General cargo (Commodities)", "__prefix": "ns3", "__text": "12"}, "__prefix": "ns3"}, "IncludedSPSTradeLineItem": [{"SequenceNumeric": {"__prefix": "ns3", "__text": "0"}, "Description": {"__prefix": "ns3", "__text": "Consignment totals and summary"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "544.4"}, "GrossWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "609"}, "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "43.0"}, "__prefix": "ns3"}, "OriginSPSCountry": {"ID": {"__prefix": "ns3", "__text": "MR"}, "Name": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "1"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON><PERSON> aeneus"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "181"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028990"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "GPW"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "<PERSON><PERSON><PERSON><PERSON> aeneus"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "10.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "2"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Epine<PERSON><PERSON> marginatus"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "3.3"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028990"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "GPD"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Epine<PERSON><PERSON> marginatus"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "1.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "3"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pag<PERSON> auriga"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "61.5"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028590"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Sea bream (Sparidae)"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "REA"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pag<PERSON> auriga"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "4.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "4"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Epine<PERSON><PERSON> costae"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "17.3"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028990"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "EPK"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Epine<PERSON><PERSON> costae"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "2.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "5"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pag<PERSON> caeruleostictus"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "75.7"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028590"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Sea bream (Sparidae)"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "BSC"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pag<PERSON> caeruleostictus"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "5.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "6"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Mycteroperca rubra"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "7.5"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028990"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "MKU"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Mycteroperca rubra"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "1.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "7"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Sol<PERSON> se<PERSON>ensis"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "99.6"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03022300"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Flat fish (Pleuronectidae, Bothidae, Cynoglossidae, Soleidae, Scophthalmidae and Citharidae), excluding livers and roes"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Sole (Solea spp.)"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Sole (Solea spp.)"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "OAL"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Sol<PERSON> se<PERSON>ensis"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "10.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}, {"SequenceNumeric": {"__prefix": "ns3", "__text": "8"}, "Description": {"__prefix": "ns3"}, "ScientificName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pseudupene<PERSON> prayensis"}, "ProductionBatchID": {"__prefix": "ns3", "__text": "57/25"}, "NetWeightMeasure": {"_unitCode": "KGM", "__prefix": "ns3", "__text": "98.5"}, "ApplicableSPSClassification": [{"SystemID": {"__prefix": "ns3", "__text": "CN"}, "SystemName": {"__prefix": "ns3", "__text": "CN Code (Combined Nomenclature)"}, "ClassCode": {"__prefix": "ns3", "__text": "03028990"}, "ClassName": [{"_languageID": "en", "__prefix": "ns3", "__text": "FISH AND CRUSTACEANS, <PERSON><PERSON><PERSON><PERSON><PERSON> AND OTHER AQUATIC INVERTEBRATES"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Fish, fresh or chilled, excluding fish fillets and other fish meat of heading|0304"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other fish, excluding edible fish offal of subheadings 0302 91 to 0302 99"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}, {"_languageID": "en", "__prefix": "ns3", "__text": "Other"}], "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FAO_ASFIS"}, "SystemName": {"__prefix": "ns3", "__text": "FAO Aquatic Sciences and Fisheries Information System Code (ASFIS)"}, "ClassCode": {"__prefix": "ns3", "__text": "GOA"}, "ClassName": {"_languageID": "la", "__prefix": "ns3", "__text": "Pseudupene<PERSON> prayensis"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "NATURE_OF_COMMODITY"}, "SystemName": {"__prefix": "ns3", "__text": "Nature of Commodity"}, "ClassCode": {"__prefix": "ns3", "__text": "WILD_STOCK"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Fishery products, Wild stock"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "TREATMENT_TYPE"}, "SystemName": {"__prefix": "ns3", "__text": "Treatment Type"}, "ClassCode": {"__prefix": "ns3", "__text": "CHILLED"}, "ClassName": {"_languageID": "en", "__prefix": "ns3", "__text": "Chilled"}, "__prefix": "ns3"}, {"SystemID": {"__prefix": "ns3", "__text": "FINAL_CONSUMER"}, "SystemName": {"__prefix": "ns3", "__text": "Final Consumer"}, "ClassCode": {"__prefix": "ns3", "__text": "FALSE"}, "ClassName": {"__prefix": "ns3"}, "__prefix": "ns3"}], "PhysicalSPSPackage": {"LevelCode": {"_name": "No packaging hierarchy", "__prefix": "ns3", "__text": "4"}, "TypeCode": {"_name": "Polystyrene Box", "__prefix": "ns3", "__text": "QR"}, "ItemQuantity": {"__prefix": "ns3", "__text": "10.0"}, "__prefix": "ns3"}, "AppliedSPSProcess": [{"TypeCode": {"_name": "Collecting (Collection Centre)", "__prefix": "ns3", "__text": "33"}, "CompletionSPSPeriod": {"StartDateTime": {"DateTime": {"__prefix": "ns4", "__text": "2025-09-09T20:07:00.000+02:00"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "ApplicableSPSProcessCharacteristic": {"Description": {"__prefix": "ns3", "__text": "COLLECTION_PRODUCTION_DATE"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Manufacturing (Manufacturing)", "__prefix": "ns3", "__text": "37"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}, {"TypeCode": {"_name": "Storing (Cold store)", "__prefix": "ns3", "__text": "43"}, "OperatorSPSParty": {"ID": {"_schemeID": "operator_activity_id", "_schemeName": "Operator activity ID", "_schemeAgencyID": "ec_sante_traces", "_schemeAgencyName": "European commission - DG SANTE - Traces", "__prefix": "ns3", "__text": "01.061"}, "Name": {"__prefix": "ns3", "__text": "M.F.C Sarl"}, "TypeCode": [{"_listID": "operator_activity_type", "_listName": "Operator activity type", "_name": "Processing Plant", "__prefix": "ns3", "__text": "PROCESSING_PLANT"}, {"_listID": "classification_section_code", "_listName": "Operator classification section code", "_name": "Fishery products", "__prefix": "ns3", "__text": "FFP"}], "SpecifiedSPSAddress": {"LineOne": {"__prefix": "ns3", "__text": ".PLAGE DES PECHEURS NOUAKCHOTT"}, "CityName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "CountryID": {"__prefix": "ns3", "__text": "MR"}, "CountryName": {"_languageID": "en", "__prefix": "ns3", "__text": "Mauritania"}, "CountrySubDivisionName": {"_languageID": "en", "__prefix": "ns3", "__text": "Nouakchott"}, "__prefix": "ns3"}, "__prefix": "ns3"}, "__prefix": "ns3"}], "__prefix": "ns3"}], "__prefix": "ns3"}, "__prefix": "ns5"}, "__prefix": "ns6"}, "_xmlns:ns2": "http://ec.europa.eu/sanco/tracesnt/base/v4", "_xmlns:ns3": "urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21", "_xmlns:ns4": "urn:un:unece:uncefact:data:standard:UnqualifiedDataType:21", "_xmlns:ns5": "urn:un:unece:uncefact:data:standard:SPSCertificate:17", "_xmlns:ns6": "http://ec.europa.eu/tracesnt/certificate/euimport/retrieval/v01", "_xmlns:ns7": "http://ec.europa.eu/tracesnt/certificate/base/v01", "_xmlns:ns8": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd", "_xmlns:ns9": "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd", "_xmlns:ns10": "http://www.w3.org/2000/09/xmldsig#", "_xmlns:ns11": "http://ec.europa.eu/tracesnt/body/v3", "_xmlns:ns12": "urn:un:unece:uncefact:codelist:standard:EDIFICAS-EU:CertificateType:D11A", "_xmlns:ns13": "urn:un:unece:uncefact:codelist:standard:ISO:ISO2AlphaLanguageCode:2006-10-27", "_xmlns:ns14": "urn:un:unece:uncefact:codelist:standard:UNECE:AddressType:D16B", "_xmlns:ns15": "urn:un:unece:uncefact:codelist:standard:UNECE:CargoTypeClassificationCode:D16B", "_xmlns:ns16": "urn:un:unece:uncefact:codelist:standard:UNECE:DocumentNameCode:D16B", "_xmlns:ns17": "urn:un:unece:uncefact:codelist:standard:UNECE:DocumentStatusCode:D16B", "_xmlns:ns18": "urn:un:unece:uncefact:codelist:standard:UNECE:GovernmentActionCode:D16B", "_xmlns:ns19": "urn:un:unece:uncefact:codelist:standard:UNECE:LocationFunctionCode:D16B", "_xmlns:ns20": "urn:un:unece:uncefact:codelist:standard:UNECE:PackageTypeCode:2006", "_xmlns:ns21": "urn:un:unece:uncefact:codelist:standard:UNECE:PackagingLevelCode:D16B", "_xmlns:ns22": "urn:un:unece:uncefact:codelist:standard:UNECE:ProcessTypeCode:D16B", "_xmlns:ns23": "urn:un:unece:uncefact:codelist:standard:UNECE:StatusCode:D16B", "_xmlns:ns24": "urn:un:unece:uncefact:codelist:standard:UNECE:TemperatureTypeCode:D16B", "_xmlns:ns25": "urn:un:unece:uncefact:codelist:standard:UNECE:TransportMeansTypeCode:2007", "_xmlns:ns26": "urn:un:unece:uncefact:codelist:standard:UNECE:TransportModeCode:2", "__prefix": "ns6"}, "__prefix": "S"}, "_xmlns:S": "http://schemas.xmlsoap.org/soap/envelope/", "__prefix": "S"}}