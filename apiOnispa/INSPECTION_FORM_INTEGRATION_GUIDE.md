# Guide d'Intégration - Formulaire d'Inspection des Produits

## 🎯 **Objectif**
Intégrer les données des certificats CHED dans votre formulaire d'inspection des produits français en mappant automatiquement les champs.

## 🔗 **Nouveau Endpoint API**

### **URL**: `GET /api/certificates/inspection-form`
### **Paramètre**: `reference` (référence sanitaire ou CHED ID)

```bash
curl "http://localhost:8011/api/certificates/inspection-form?reference=IMPORT.EU.MR.2025.0000007"
```

## 📊 **Réponse API Mappée**

```json
{
  "success": true,
  "reference": "IMPORT.EU.MR.2025.0000007",
  "mapped_data": {
    "certificate_info": {
      "ched_id": "CHEDP.FR.2025.0000038",
      "certificate_reference": "IMPORT.EU.MR.2025.0000007",
      "source": "database"
    },
    "products": [
      {
        "produit": {
          "code": "03028990",
          "name_en": "Other fish",
          "name_fr": "Autres poissons",
          "system_id": "CN",
          "system_name": "CN Code (Combined Nomenclature)",
          "suggested_dropdown_value": "Autres poissons"
        },
        "origine_de_produit": {
          "country_code": "MR",
          "country_name": "Mauritanie",
          "suggested_dropdown_value": "MAURITANIE"
        },
        "lieu_de_stockage": {
          "consignee_name": "FIORITAL SPA",
          "suggested_dropdown_value": "DEWLA SEAFOOD"
        },
        "nature_de_piece": {
          "suggested_value": "Sauvage",
          "certificate_type": "P"
        },
        "type_de_produits": {
          "suggested_value": "Entier",
          "options": ["Entier", "Filet", "Transformé"]
        },
        "types_emballage": {
          "suggested_value": "Carton Master",
          "options": ["Carton Master", "Caisse", "Sac", "Palette"]
        },
        "unite_de_mesure": {
          "suggested_value": "kg",
          "options": ["kg", "sac", "pièce", "tonne"]
        },
        "quantite": {
          "suggested_value": null,
          "note": "À remplir manuellement"
        },
        "nombre_de_colis": {
          "suggested_value": null,
          "note": "À remplir manuellement"
        }
      }
    ],
    "certificate_details": {
      "consignor": {
        "name": "COPMER SARL",
        "country": "MR"
      },
      "consignee": {
        "name": "FIORITAL SPA",
        "country": "FR"
      },
      "status": "Issued (Validated)",
      "update_date": "2025-08-05T07:42:58.000Z",
      "sanitary_references": [...]
    }
  },
  "form_suggestions": {
    "dropdown_options": {
      "produit": ["Autres poissons"],
      "origine_de_produit": ["MAURITANIE"],
      "lieu_de_stockage": ["DEWLA SEAFOOD"],
      "nature_de_piece": ["Sauvage", "Élevage"],
      "type_de_produits": ["Entier", "Filet", "Transformé"],
      "types_emballage": ["Carton Master", "Caisse", "Sac", "Palette"],
      "unite_de_mesure": ["kg", "sac", "pièce", "tonne"]
    },
    "pre_filled_values": {
      "produit": "Autres poissons",
      "origine_de_produit": "MAURITANIE",
      "lieu_de_stockage": "DEWLA SEAFOOD",
      "nature_de_piece": "Sauvage",
      "type_de_produits": "Entier",
      "types_emballage": "Carton Master",
      "unite_de_mesure": "kg"
    }
  }
}
```

## 🗺️ **Mapping des Champs**

### **Champs Automatiquement Mappés**
| Champ Formulaire | Source CHED | Exemple |
|------------------|-------------|---------|
| **Produit** | `products.class_name_en` → Traduit | "Other fish" → "Autres poissons" |
| **Origine de produit** | `certificate_info.country_of_dispatch` | "MR" → "MAURITANIE" |
| **Lieu de stockage** | `certificate_info.consignee_name` | "FIORITAL SPA" → "DEWLA SEAFOOD" |
| **Nature de pièce** | Fixe pour poissons | "Sauvage" |
| **Type de produits** | Basé sur `class_name_en` | "Entier" (défaut) |
| **Types d'emballage** | Fixe | "Carton Master" (défaut) |
| **Unité de mesure** | Fixe | "kg" (défaut) |

### **Champs à Remplir Manuellement**
- **Quantité** - Non disponible dans CHED
- **Nombre de colis** - Non disponible dans CHED

## 💻 **Code d'Intégration**

### **JavaScript/jQuery Example**
```javascript
function loadCertificateData(reference) {
    $.ajax({
        url: 'http://localhost:8011/api/certificates/inspection-form',
        method: 'GET',
        data: { reference: reference },
        success: function(response) {
            if (response.success) {
                fillInspectionForm(response);
            } else {
                alert('Certificat non trouvé: ' + response.message);
            }
        },
        error: function() {
            alert('Erreur lors de la récupération des données');
        }
    });
}

function fillInspectionForm(data) {
    const suggestions = data.form_suggestions.pre_filled_values;
    
    // Remplir les dropdowns
    $('#produit').val(suggestions.produit);
    $('#origine_de_produit').val(suggestions.origine_de_produit);
    $('#lieu_de_stockage').val(suggestions.lieu_de_stockage);
    $('#nature_de_piece').val(suggestions.nature_de_piece);
    $('#type_de_produits').val(suggestions.type_de_produits);
    $('#types_emballage').val(suggestions.types_emballage);
    $('#unite_de_mesure').val(suggestions.unite_de_mesure);
    
    // Mettre à jour les options des dropdowns si nécessaire
    updateDropdownOptions(data.form_suggestions.dropdown_options);
    
    // Afficher les informations du certificat
    displayCertificateInfo(data.mapped_data.certificate_details);
}

function updateDropdownOptions(options) {
    // Ajouter les nouvelles options aux dropdowns
    Object.keys(options).forEach(field => {
        const $select = $('#' + field);
        options[field].forEach(option => {
            if ($select.find('option[value="' + option + '"]').length === 0) {
                $select.append('<option value="' + option + '">' + option + '</option>');
            }
        });
    });
}
```

### **PHP Example**
```php
function getCertificateForForm($reference) {
    $url = "http://localhost:8011/api/certificates/inspection-form?reference=" . urlencode($reference);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return null;
}

// Utilisation
$certificateData = getCertificateForForm('IMPORT.EU.MR.2025.0000007');
if ($certificateData && $certificateData['success']) {
    $suggestions = $certificateData['form_suggestions']['pre_filled_values'];
    
    // Pré-remplir le formulaire
    echo '<select name="produit">';
    echo '<option value="' . $suggestions['produit'] . '" selected>' . $suggestions['produit'] . '</option>';
    echo '</select>';
}
```

## 🔧 **Personnalisation du Mapping**

### **Ajouter de Nouveaux Produits**
Modifiez `ProductInspectionMappingService::mapToFrenchProductDropdown()`:

```php
$productMapping = [
    'Other fish' => 'Autres poissons',
    'Octopus' => 'Tako - Octopus vulgaris',
    'Corvina' => 'Corvina (Argyrosomus regius)',
    // Ajoutez vos mappings ici
    'New Fish Species' => 'Nouvelle Espèce de Poisson'
];
```

### **Ajouter de Nouveaux Lieux de Stockage**
Modifiez `ProductInspectionMappingService::mapConsigneeToStorageLocation()`:

```php
$storageMapping = [
    'FIORITAL SPA' => 'DEWLA SEAFOOD',
    'BEST FRIGO' => 'BEST FRIGO',
    // Ajoutez vos mappings ici
    'NOUVEAU CLIENT' => 'NOUVEAU ENTREPOT'
];
```

## 🧪 **Test de l'Intégration**

```bash
# Test avec référence sanitaire
curl "http://localhost:8011/api/certificates/inspection-form?reference=IMPORT.EU.MR.2025.0000007"

# Test avec CHED ID
curl "http://localhost:8011/api/certificates/inspection-form?reference=CHEDP.FR.2025.0000038"
```

## 📋 **Workflow Complet**

1. **Utilisateur saisit** une référence sanitaire dans votre app
2. **Votre app appelle** `/api/certificates/inspection-form`
3. **API retourne** les données mappées pour le formulaire
4. **Votre app pré-remplit** automatiquement les champs
5. **Utilisateur complète** quantité et nombre de colis
6. **Formulaire prêt** pour soumission

## ✅ **Avantages**

- 🚀 **Gain de temps** - Pré-remplissage automatique
- 🎯 **Précision** - Données directement du certificat officiel
- 🔄 **Cohérence** - Mapping standardisé
- 📊 **Traçabilité** - Lien avec certificat CHED original
- 🌐 **Fallback API** - Récupération automatique si pas en base

**Votre formulaire d'inspection est maintenant connecté aux données CHED officielles!** 🎉
