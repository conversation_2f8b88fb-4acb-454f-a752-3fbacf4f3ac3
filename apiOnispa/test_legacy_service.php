<?php

/**
 * Simple test script for EuImportLegacyService
 * 
 * This script tests the fixed EuImportLegacyService to ensure it works
 * correctly with the TRACES API.
 */

// Include the service (adjust path as needed)
require_once 'app/Services/EuImportLegacyService.php';

use App\Services\EuImportLegacyService;

echo "Testing EuImportLegacyService...\n";
echo str_repeat("=", 50) . "\n";

try {
    // Initialize the service
    $service = new EuImportLegacyService();
    echo "✓ Service initialized successfully\n";
    
    // Test with a known reference
    $reference = 'IMPORT.EU.MR.2025.0003940';
    echo "Testing with reference: {$reference}\n";
    
    // Test the main method
    echo "Fetching products for forms...\n";
    $result = $service->getProductsForForms($reference);
    
    if ($result['success']) {
        echo "✓ SUCCESS! Certificate fetched and parsed\n";
        echo "  - Products found: {$result['summary']['total_products']}\n";
        echo "  - Total weight: {$result['summary']['total_weight']} kg\n";
        echo "  - Total packages: {$result['summary']['total_packages']}\n";
        
        // Show first product details
        if (!empty($result['products'])) {
            $firstProduct = $result['products'][0];
            echo "\nFirst product details:\n";
            echo "  - ID: {$firstProduct['id']}\n";
            echo "  - Name: {$firstProduct['product_name']}\n";
            echo "  - Scientific: {$firstProduct['scientific_name']}\n";
            echo "  - Origin: {$firstProduct['origin_country']}\n";
            echo "  - Weight: {$firstProduct['quantity']} {$firstProduct['unit']}\n";
            echo "  - Packages: {$firstProduct['package_count']}\n";
            echo "  - Storage: {$firstProduct['storage_location']}\n";
            echo "  - Nature: {$firstProduct['nature']}\n";
            echo "  - Type: {$firstProduct['product_type']}\n";
            echo "  - Packaging: {$firstProduct['packaging_type']}\n";
            echo "  - FAO Code: {$firstProduct['fao_code']}\n";
            echo "  - CN Code: {$firstProduct['cn_code']}\n";
            echo "  - Is Wild: " . ($firstProduct['is_wild'] ? 'Yes' : 'No') . "\n";
            echo "  - Batch ID: {$firstProduct['batch_id']}\n";
            echo "  - Collection Date: {$firstProduct['collection_date']}\n";
        }
        
        echo "\n✓ All tests passed! The service is working correctly.\n";
        
    } else {
        echo "✗ FAILED: {$result['error']}\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "✗ EXCEPTION: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Test completed successfully!\n";
echo "The EuImportLegacyService is ready for use in your legacy code.\n";
