# CHED Database Sync Solution

## Overview

This solution implements a robust cron-based system that:
1. **Fetches certificates regularly** from TRACES NT API (every 10 minutes)
2. **Stores certificates and sanitary references** in local database
3. **Extracts and stores products** for fast retrieval
4. **Handles pagination** (100 records per request limit)
5. **Ensures no duplicates** and tracks synchronization

## Database Schema

### Tables Created
- `ched_certificates` - Main certificate data
- `ched_sanitary_references` - Sanitary certificate references (searchable)
- `ched_products` - Certificate products/commodities
- `ched_sync_log` - Synchronization tracking and monitoring

## Setup Instructions

### 1. Run Database Migrations
```bash
php artisan migrate
```

### 2. Register the Console Command
Add to `app/Console/Kernel.php`:
```php
protected $commands = [
    \App\Console\Commands\SyncChedCertificates::class,
];
```

### 3. Set Up Cron Job
Add to your system crontab:
```bash
# Sync CHED certificates every 10 minutes
*/10 * * * * cd /path/to/your/project && php artisan ched:sync --recent
```

### 4. Initial Data Load
Run an initial sync to populate the database:
```bash
# Sync last 7 days
php artisan ched:sync --start-date=2025-01-03 --end-date=2025-01-10

# Or sync recent certificates (last 2 days)
php artisan ched:sync --recent
```

## Usage Examples

### 1. Command Line Usage
```bash
# Sync recent certificates (last 2 days)
php artisan ched:sync --recent

# Sync specific date range
php artisan ched:sync --start-date=2025-01-01 --end-date=2025-01-10

# Force sync even if another is running
php artisan ched:sync --recent --force
```

### 2. API Usage (Now Database-Powered)
```bash
# Search by CHED ID
curl "http://your-domain/api/certificates/products?reference=CHEDP.FR.2025.0093319"

# Search by sanitary reference (now works!)
curl "http://your-domain/api/certificates/products?reference=IMPORT.EU.MR.2025.0003344"
```

### 3. Programmatic Usage
```php
use App\Models\ChedCertificate;
use App\Models\ChedSanitaryReference;
use App\Models\ChedProduct;

// Find certificate by sanitary reference
$certificate = ChedCertificate::findBySanitaryReference('IMPORT.EU.MR.2025.0003344');

// Get products by sanitary reference
$products = ChedProduct::getBySanitaryReference('IMPORT.EU.MR.2025.0003344');

// Search certificates by pattern
$certificates = ChedCertificate::searchBySanitaryReference('IMPORT.EU.MR.2025');

// Get all certificates from Mauritania
$certificates = ChedCertificate::where('country_of_dispatch', 'MR')
    ->with(['products', 'sanitaryReferences'])
    ->get();
```

## API Response Format

The API now returns comprehensive data from the database:

```json
{
  "success": true,
  "certificate_reference": "IMPORT.EU.MR.2025.0003344",
  "ched_id": "CHEDP.FR.2025.0093319",
  "source": "database",
  "certificate_info": {
    "type": "P",
    "status": "Issued (Validated)",
    "country_of_dispatch": "MR",
    "consignor_name": "COPMER SARL",
    "consignee_name": "FIORITAL SPA",
    "update_date_time": "2025-08-05T07:42:58.000Z"
  },
  "products_count": 2,
  "products": [
    {
      "system_id": "CN",
      "system_name": "CN Code (Combined Nomenclature)",
      "class_code": "03028990",
      "class_names": {"en": "Other fish"},
      "class_name_en": "Other fish"
    }
  ],
  "sanitary_references": [
    {
      "sanitary_reference": "IMPORT.EU.MR.2025.0003344",
      "type_name": "Sanitary certificate (EU Import certificate)",
      "attachment_uri": "https://webgate.ec.europa.eu/tracesnt/certificate/eu-import/IMPORT.EU.MR.2025.0003344"
    }
  ],
  "last_sync_info": {
    "last_sync_time": "2025-01-10T10:30:00.000Z",
    "last_sync_human": "5 minutes ago",
    "certificates_in_last_sync": 45,
    "date_range": "2025-01-08 to 2025-01-10"
  }
}
```

## Monitoring and Maintenance

### 1. Check Sync Status
```php
use App\Models\ChedSyncLog;

// Get latest sync info
$lastSync = ChedSyncLog::getLatestSuccessfulSync();

// Get sync statistics for last 7 days
$stats = ChedSyncLog::getSyncStats(7);
```

### 2. Monitor Database Growth
```sql
-- Check certificate counts
SELECT country_of_dispatch, COUNT(*) as count 
FROM ched_certificates 
GROUP BY country_of_dispatch;

-- Check sanitary references
SELECT COUNT(*) as total_sanitary_refs 
FROM ched_sanitary_references;

-- Check products
SELECT COUNT(*) as total_products 
FROM ched_products;
```

### 3. Cleanup Old Data (Optional)
```php
// Delete certificates older than 1 year
ChedCertificate::where('create_date_time', '<', now()->subYear())->delete();

// Delete old sync logs
ChedSyncLog::where('sync_start_time', '<', now()->subMonths(3))->delete();
```

## Key Benefits

✅ **Fast Response Times** - Database queries vs API calls
✅ **Reliable Search** - No API limitations or timeouts
✅ **Comprehensive Data** - Full certificate, products, and references
✅ **Automatic Updates** - Cron job keeps data fresh
✅ **Monitoring** - Track sync success and data growth
✅ **Scalable** - Handle thousands of certificates efficiently
✅ **Offline Capability** - Works even if TRACES API is down

## Troubleshooting

### Sync Issues
```bash
# Check if sync is running
php artisan ched:sync --recent --force

# Check logs
tail -f storage/logs/laravel.log

# Check sync history
SELECT * FROM ched_sync_log ORDER BY sync_start_time DESC LIMIT 10;
```

### Missing Certificates
1. Check if sync is running regularly
2. Verify date range covers the certificate date
3. Check TRACES API credentials
4. Run manual sync for specific date range

### Performance Issues
1. Add database indexes if needed
2. Consider archiving old certificates
3. Monitor database size and optimize queries

## Files Created/Modified

### New Files
- `database/migrations/2025_01_10_000001_create_ched_certificates_table.php`
- `database/migrations/2025_01_10_000002_create_ched_sanitary_references_table.php`
- `database/migrations/2025_01_10_000003_create_ched_products_table.php`
- `database/migrations/2025_01_10_000004_create_ched_sync_log_table.php`
- `app/Models/ChedCertificate.php`
- `app/Models/ChedSanitaryReference.php`
- `app/Models/ChedProduct.php`
- `app/Models/ChedSyncLog.php`
- `app/Services/ChedSyncService.php`
- `app/Console/Commands/SyncChedCertificates.php`

### Modified Files
- `app/Http/Controllers/CertificateController.php` - Updated to use database

**Your sanitary reference search is now fully functional and database-powered!** 🎉
